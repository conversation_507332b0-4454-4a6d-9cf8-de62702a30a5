# NextAuth Setup Guide

## Quick Fix for Login Issues

If you're getting the `[next-auth][error][NO_SECRET]` error, follow these steps:

### 1. Create/Update Your `.env.local` File

Create or update `.env.local` in your project root with:

```bash
# Database Configuration
DATABASE_URL="file:./prisma/schema/dev.db"
NODE_ENV="development"

# NextAuth Configuration (REQUIRED)
NEXTAUTH_SECRET="a419bb77ce614604e1ace40b2668271c928a9579a1f4785b2026983be97aa3c6"
NEXTAUTH_URL="http://localhost:3000"

# Production variables (leave commented for local development)
# TURSO_DATABASE_URL=""
# TURSO_AUTH_TOKEN=""
```

### 2. Generate Your Own Secret (Recommended)

For security, generate your own secret:

```bash
# Run this command to generate a new secret
node -e "console.log('NEXTAUTH_SECRET=\"' + require('crypto').randomBytes(32).toString('hex') + '\"')"
```

Then copy the output and use it in your `.env.local` file.

### 3. Restart Your Development Server

After updating `.env.local`:

```bash
# Stop your dev server (Ctrl+C) and restart
pnpm dev
```

### 4. Verify the Fix

1. Go to `http://localhost:3000/auth/login`
2. Try to log in - the NO_SECRET error should be gone
3. Check the terminal for any remaining errors

## Production Setup

For production (Vercel), add these environment variables:

```bash
NEXTAUTH_SECRET="your-production-secret-here"
NEXTAUTH_URL="https://your-app-domain.vercel.app"
TURSO_DATABASE_URL="libsql://your-database.turso.io"
TURSO_AUTH_TOKEN="your-turso-token"
NODE_ENV="production"
```

## Troubleshooting

- **Still getting NO_SECRET error?** - Make sure `.env.local` exists and has no typos
- **Can't log in?** - Check that you've created a user via registration first
- **Database errors?** - Ensure the SQLite database file exists with `pnpm prisma db push --schema=prisma/schema`
