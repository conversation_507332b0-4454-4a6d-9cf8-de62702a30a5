// @ts-nocheck
'use client'
import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useVillageStore } from '@/components/ui/village-context';
import { useT } from './language-provider';
import { ArrowLeft, MapPin, Home, Scale, Hash, Laptop, Coffee, Users, Clock, Mail, Phone, Globe, Lightbulb } from 'lucide-react'
import { Button } from './ui/button'
import Image from 'next/image'
import type { BusinessType } from '@/app/page'
import { debounce } from '../lib/utils'
import ReactDOM from 'react-dom';

interface BusinessDistrictExplorerProps {
  onReturnToMap: () => void
  onBusinessVisit: (business: BusinessType) => void
  discoveredBusinesses: Set<BusinessType>
  currentBusiness: BusinessType | null
  onReturnToBusinessDistrict: () => void
}

const businessData = {
  sadeed: {
    icon: Scale,
    color: 'from-amber-600 to-amber-700',
    image: 'https://cdn.abacus.ai/images/9e611d47-70c8-44cb-81f5-629df994f3c0.png',
    position: { top: '20%', left: '25%' }
  },
  tamam: {
    icon: Hash,
    color: 'from-purple-600 to-purple-700',
    image: 'https://cdn.abacus.ai/images/7426ce8e-0e68-4604-96c2-92fb4d9c16e2.png',
    position: { top: '30%', left: '75%' }
  },
  'smart-access': {
    icon: Laptop,
    color: 'from-blue-600 to-blue-700',
    image: 'https://cdn.abacus.ai/images/b2a23339-c998-4db1-9206-177f0f6ae1eb.png',
    position: { top: '70%', left: '30%' }
  },
  spot: {
    icon: Coffee,
    color: 'from-orange-600 to-orange-700',
    image: 'https://cdn.abacus.ai/images/e29a5e6b-6aa6-48cd-a96b-4420d0eb4a10.png',
    position: { top: '60%', left: '70%' }
  }
}

function BusinessDistrictExplorer({ 
  onReturnToMap, 
  onBusinessVisit, 
  discoveredBusinesses, 
  currentBusiness,
  onReturnToBusinessDistrict 
}: BusinessDistrictExplorerProps) {
  const t = useT()
  const [hoveredBusiness, setHoveredBusiness] = useState<BusinessType | null>(null)
  const [tooltipPos, setTooltipPos] = useState<{ x: number; y: number } | null>(null);
  const markerRefs = React.useRef<Record<string, HTMLDivElement | null>>({});

  // Debounced hover handlers
  const debouncedSetHoveredBusiness = React.useMemo(
    () => debounce((business: BusinessType | null) => {
      setHoveredBusiness(business);
      if (business && markerRefs.current[business]) {
        const rect = markerRefs.current[business]!.getBoundingClientRect();
        setTooltipPos({ x: rect.left + rect.width / 2, y: rect.top });
      } else {
        setTooltipPos(null);
      }
    }, 40),
    []
  )

  const getBusinessName = (business: BusinessType) => {
    const nameMap = {
      sadeed: 'business_sadeed_name',
      tamam: 'business_tamam_name',
      'smart-access': 'business_smart_access_name',
      spot: 'business_spot_name'
    }
    return t(nameMap[business])
  }

  const getBusinessDesc = (business: BusinessType) => {
    const descMap = {
      sadeed: 'business_sadeed_desc',
      tamam: 'business_tamam_desc',
      'smart-access': 'business_smart_access_desc',
      spot: 'business_spot_desc'
    }
    return t(descMap[business])
  }

  const getBusinessDetails = (business: BusinessType) => {
    const detailsMap = {
      sadeed: 'business_sadeed_details',
      tamam: 'business_tamam_details',
      'smart-access': 'business_smart_access_details',
      spot: 'business_spot_details'
    }
    return t(detailsMap[business])
  }

  const getBusinessServices = (business: BusinessType) => {
    const servicesMap = {
      sadeed: 'business_sadeed_services',
      tamam: 'business_tamam_services',
      'smart-access': 'business_smart_access_services',
      spot: 'business_spot_services'
    }
    return t(servicesMap[business])
  }

  // Individual Business View
  if (currentBusiness) {
    const config = businessData[currentBusiness]
    const IconComponent = config.icon

    return (
      <section className="min-h-screen bg-gradient-to-b from-gray-50 to-white pt-20">
        {/* Hero Section for Business */}
        <div className="relative h-96 overflow-hidden">
          <Image
            src={config.image}
            alt={getBusinessName(currentBusiness)}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent" />
          
          {/* Business Header */}
          <div className="absolute inset-0 flex items-center">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                style={{ maxWidth: '32rem' }}
              >
                <div className="flex items-center space-x-4 mb-4">
                  <div className={`w-16 h-16 bg-gradient-to-br ${config.color} rounded-xl flex items-center justify-center shadow-lg`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight">
                      {getBusinessName(currentBusiness)}
                    </h1>
                    <p className="text-white/90 text-lg mt-2">
                      {getBusinessDesc(currentBusiness)}
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Business Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Navigation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '2rem' }}
          >
            <Button
              variant="outline"
              onClick={onReturnToBusinessDistrict}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>{t('return_to_business_district')}</span>
            </Button>

            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Home className="h-4 w-4" />
              <span>{t('business_district')} / {getBusinessName(currentBusiness)}</span>
            </div>
          </motion.div>

          {/* Business Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-4">{t('about_business')}</h3>
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 leading-relaxed text-lg mb-6">
                {getBusinessDetails(currentBusiness)}
              </p>
            </div>

            {/* Services */}
            <div className="border-t pt-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">{t('our_services')}</h4>
              <p className="text-gray-700 leading-relaxed">
                {getBusinessServices(currentBusiness)}
              </p>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-r from-teal-50 to-teal-100 rounded-2xl p-6 mb-8 border border-teal-200"
          >
            <div className="flex items-start space-x-4">
              <div className={`w-12 h-12 bg-gradient-to-br ${config.color} rounded-xl flex items-center justify-center`}>
                <IconComponent className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-teal-900 mb-2">{t('connect_with_us')}</h4>
                <p className="text-teal-800 text-sm leading-relaxed mb-4">
                  {t('business_contact_message')}
                </p>
                <div className="flex flex-wrap gap-3">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center space-x-2 bg-white px-3 py-2 rounded-lg shadow-sm"
                  >
                    <Mail className="h-4 w-4 text-teal-600" />
                    <span className="text-sm text-gray-700">{t('email_us')}</span>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center space-x-2 bg-white px-3 py-2 rounded-lg shadow-sm"
                  >
                    <Users className="h-4 w-4 text-teal-600" />
                    <span className="text-sm text-gray-700">{t('visit_office')}</span>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    )
  }

  // Business District Overview
  return (
    <section className="min-h-screen bg-gradient-to-b from-teal-50 to-teal-100 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-4">
            {t('district_10_name')}
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            {t('business_district_subtitle')}
          </p>
          <p className="text-gray-500">
            {t('business_district_instruction')}
          </p>
        </motion.div>

        {/* Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <Button
            variant="outline"
            onClick={onReturnToMap}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>{t('return_to_map')}</span>
          </Button>

          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <MapPin className="h-4 w-4" />
            <span>{t('current_location')}: {t('district_10_name')}</span>
          </div>
        </motion.div>

        {/* Business Houses Map */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="relative w-full max-w-6xl mx-auto bg-white rounded-3xl shadow-2xl overflow-hidden mb-12"
        >
          <div className="relative aspect-video bg-gradient-to-br from-teal-100 to-teal-200">
            {/* Business House Markers */}
            {Object.entries(businessData).map(([business, data]) => {
              const businessKey = business as BusinessType
              const IconComponent = data.icon
              const isDiscovered = discoveredBusinesses.has(businessKey)
              const isHovered = hoveredBusiness === businessKey

              return (
                <motion.div
                  key={business}
                  ref={el => (markerRefs.current[businessKey] = el)}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                  style={{ ...data.position, willChange: 'transform, opacity' }}
                  whileHover={{ scale: 1.2, zIndex: 50 }}
                  whileTap={{ scale: 0.9 }}
                  onHoverStart={() => debouncedSetHoveredBusiness(businessKey)}
                  onHoverEnd={() => debouncedSetHoveredBusiness(null)}
                  onClick={() => onBusinessVisit(businessKey)}
                >
                  {/* Pulse Animation */}
                  {!isDiscovered && (
                    <motion.div
                      className="absolute inset-0 rounded-xl bg-teal-500 opacity-30"
                      animate={{ scale: [1, 1.3, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  )}

                  {/* Business House */}
                  <motion.div 
                    className={`
                      relative w-20 h-20 md:w-24 md:h-24 bg-gradient-to-br ${data.color} rounded-xl 
                      flex items-center justify-center shadow-2xl border-4 border-white 
                      transition-all duration-300
                    `}
                    whileHover={{ opacity: 0.85 }}
                    whileTap={{ opacity: 0.75 }}
                    style={{ willChange: 'opacity', transformOrigin: '50% 50%' }}
                  >
                    <IconComponent className="w-8 h-8 md:w-10 md:h-10 text-white" />
                    {isDiscovered && (
                      <div className="absolute -top-2 -right-2 w-5 h-5 bg-green-400 rounded-full border-2 border-white" />
                    )}
                  </motion.div>
                </motion.div>
              )
            })}

            {/* Business Info Tooltip Portal */}
            {hoveredBusiness && tooltipPos && ReactDOM.createPortal(
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="fixed z-[1000] pointer-events-none transform-gpu"
                style={{
                  left: tooltipPos.x - 112, // center the tooltip (min-w-56 = 224px)
                  top: tooltipPos.y - 100, // above the marker
                  transform: 'translate(-50%, -100%)',
                  willChange: 'transform, opacity',
                }}
              >
                <div className="bg-white rounded-lg shadow-xl p-4 min-w-56 border border-gray-200 relative">
                  <h3 className="font-bold text-gray-900 mb-1">
                    {getBusinessName(hoveredBusiness)}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {getBusinessDesc(hoveredBusiness)}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {discoveredBusinesses.has(hoveredBusiness) ? 'Visited' : 'Not visited'}
                    </span>
                    <span className="text-xs bg-teal-100 text-teal-800 px-2 py-1 rounded">
                      Click to visit
                    </span>
                  </div>
                  {/* Tooltip Arrow (triangle) below the tooltip */}
                  <div className="absolute left-1/2 bottom-[-8px] transform -translate-x-1/2 w-4 h-4 bg-white border-r border-b border-gray-200 rotate-45 z-10"></div>
                </div>
              </motion.div>,
              document.body
            )}

            {/* Decorative paths between houses */}
            <svg className="absolute inset-0 w-full h-full pointer-events-none">
              <defs>
                <pattern id="business-pathway" patternUnits="userSpaceOnUse" width="6" height="6">
                  <circle cx="3" cy="3" r="0.8" fill="#14b8a6" opacity="0.4" />
                </pattern>
              </defs>
              <g stroke="url(#business-pathway)" strokeWidth="2" fill="none" opacity="0.5">
                <path d="M 25% 20% Q 50% 35% 75% 30%" />
                <path d="M 25% 20% Q 40% 45% 30% 70%" />
                <path d="M 75% 30% Q 60% 45% 70% 60%" />
                <path d="M 30% 70% Q 50% 55% 70% 60%" />
              </g>
            </svg>
          </div>

          {/* Progress indicator */}
          <div className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                {t('businesses_visited')}
              </span>
              <span className="text-sm font-bold text-teal-600">
                {discoveredBusinesses.size}/4
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <motion.div 
                className="bg-gradient-to-r from-teal-500 to-teal-600 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(discoveredBusinesses.size / 4) * 100}%` }}
                transition={{ duration: 0.8 }}
              />
            </div>
          </div>
        </motion.div>

        {/* Business Grid for Mobile */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="grid md:hidden grid-cols-2 gap-4 mb-8"
        >
          {Object.entries(businessData).map(([business, data]) => {
            const businessKey = business as BusinessType
            const IconComponent = data.icon
            const isDiscovered = discoveredBusinesses.has(businessKey)

            return (
              <motion.button
                key={business}
                className={`
                  p-4 rounded-xl border-2 transition-all duration-300
                  ${isDiscovered 
                    ? 'bg-white border-teal-300 shadow-md' 
                    : 'bg-gray-50 border-gray-200'
                  }
                `}
                whileTap={{ scale: 0.95 }}
                onClick={() => onBusinessVisit(businessKey)}
                style={{ willChange: 'transform, opacity' }}
              >
                <div className={`w-10 h-10 bg-gradient-to-br ${data.color} rounded-xl flex items-center justify-center mx-auto mb-2`}>
                  <IconComponent className="w-5 h-5 text-white" />
                </div>
                <div className="text-xs font-medium text-gray-700 text-center">
                  {getBusinessName(businessKey)}
                </div>
                {isDiscovered && (
                  <div className="text-xs text-green-600 text-center mt-1">✓</div>
                )}
              </motion.button>
            )
          })}
        </motion.div>

        {/* District Guide Message */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-2xl p-6 border border-teal-200"
        >
          <div className="flex items-start space-x-4">
            <Image
              src="https://cdn.abacus.ai/images/7b4046da-2880-4890-b9e5-41ce73062874.png"
              alt="Village Guide"
              width={60}
              height={60}
              className="rounded-full border-3 border-teal-300"
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h4 className="font-semibold text-teal-900">{t('guide_tip')}</h4>
                <Lightbulb className="h-4 w-4 text-teal-600" />
              </div>
              <p className="text-teal-800 text-sm leading-relaxed">
                {t('business_district_guide_message')}
              </p>
            </div>
          </div>
        </motion.div>

        {/* All Businesses Visited */}
        {discoveredBusinesses.size === 4 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8 }}
            className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl p-8 text-center text-white mt-8"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <Home className="w-8 h-8" />
            </motion.div>
            <h3 className="text-2xl font-bold mb-2">{t('business_district_master')}</h3>
            <p className="mb-4">{t('business_district_complete_message')}</p>
            <Button
              variant="secondary"
              onClick={onReturnToMap}
              className="bg-white text-green-600 hover:bg-gray-100"
            >
              {t('return_to_map')}
            </Button>
          </motion.div>
        )}
      </div>
    </section>
  )
}

export const MemoizedBusinessDistrictExplorer = React.memo(BusinessDistrictExplorer);
export { BusinessDistrictExplorer };
