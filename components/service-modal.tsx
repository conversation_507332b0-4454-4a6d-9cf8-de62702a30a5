
'use client'
import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useLanguage } from './language-provider'
import { X } from 'lucide-react'
import Image from 'next/image'

interface ServiceModalProps {
  service: {
    id: number
    name: string
    description: string
    details: string
    icon: React.ComponentType<any>
    image: string
  }
  isOpen: boolean
  onClose: () => void
}

export function ServiceModal({ service, isOpen, onClose }: ServiceModalProps) {
  const { t } = useLanguage()
  const IconComponent = service.icon

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          // @ts-ignore
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3 }}
            // @ts-ignore
            className="bg-white rounded-3xl overflow-hidden max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            // @ts-ignore
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header with Image */}
            <div className="relative aspect-[16/9]">
              <Image
                src={service.image}
                alt={service.name}
                fill
                placeholder="blur"
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/70 to-transparent" />
              
              <motion.button
                // @ts-ignore
                onClick={onClose}
                className="absolute top-4 right-4 p-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 rounded-full transition-colors duration-200"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <X className="h-6 w-6 text-white" />
              </motion.button>
              
              <div className="absolute bottom-6 left-6 right-6">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                    <IconComponent className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h2 className="text-3xl font-bold text-white">
                      {service.name}
                    </h2>
                    <p className="text-white/90 text-lg">
                      {service.description}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-8">
              <div className="prose prose-lg max-w-none">
                <p className="text-slate-700 leading-relaxed text-lg">
                  {service.details}
                </p>
              </div>
            </div>

            {/* Close Button */}
            <div className="px-8 pb-8 flex justify-center">
              <motion.button
                // @ts-ignore
                onClick={onClose}
                className="px-6 py-3 bg-amber-600 text-white font-semibold rounded-lg hover:bg-amber-700 transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {t('close')}
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
