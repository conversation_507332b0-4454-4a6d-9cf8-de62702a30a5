
'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useVillageStore } from '@/components/ui/village-context';
import { useT } from './language-provider';
import { ArrowLeft, ArrowRight, MapPin, Lightbulb, Users, GraduationCap, ShoppingBag, Sprout, Scale, Hammer, Compass, Building, Network, Home, Wallet, Coffee } from 'lucide-react'
import { Button } from './ui/button'
import Image from 'next/image'
import type { DistrictType } from '@/app/page'

interface VillageExplorerProps {
  district: DistrictType
  onReturnToMap: () => void
  onNextDistrict: (district: DistrictType) => void
  discoveredDistricts: Set<DistrictType>
}

const districtConfig = {
  square: { 
    icon: Users, 
    color: 'from-blue-500 to-blue-600',
    image: 'https://cdn.abacus.ai/images/2ae38a00-3ca7-4ab2-a534-5d7f5b970b2a.png'
  },
  school: { 
    icon: GraduationCap, 
    color: 'from-green-500 to-green-600',
    image: 'https://cdn.abacus.ai/images/dc0e0a5e-8952-4db6-a6fe-05ed02bfff28.png'
  },
  market: { 
    icon: ShoppingBag, 
    color: 'from-purple-500 to-purple-600',
    image: 'https://cdn.abacus.ai/images/b704b1d6-faed-47a7-bb86-a98bc103c2e3.png'
  },
  field: { 
    icon: Sprout, 
    color: 'from-emerald-500 to-emerald-600',
    image: 'https://cdn.abacus.ai/images/2ae38a00-3ca7-4ab2-a534-5d7f5b970b2a.png'
  },
  council: { 
    icon: Scale, 
    color: 'from-amber-500 to-amber-600',
    image: 'https://cdn.abacus.ai/images/921bcdc0-6395-4a3b-ad27-6fe20549ac6f.png'
  },
  workshop: { 
    icon: Hammer, 
    color: 'from-orange-500 to-orange-600',
    image: 'https://cdn.abacus.ai/images/c8285355-d43f-474a-a9ad-efb6d68e2b44.png'
  },
  guide: { 
    icon: Compass, 
    color: 'from-cyan-500 to-cyan-600',
    image: 'https://cdn.abacus.ai/images/2ae38a00-3ca7-4ab2-a534-5d7f5b970b2a.png'
  },
  bureau: { 
    icon: Building, 
    color: 'from-indigo-500 to-indigo-600',
    image: 'https://cdn.abacus.ai/images/2ae38a00-3ca7-4ab2-a534-5d7f5b970b2a.png'
  },
  gateway: { 
    icon: Network, 
    color: 'from-rose-500 to-rose-600',
    image: 'https://cdn.abacus.ai/images/2ae38a00-3ca7-4ab2-a534-5d7f5b970b2a.png'
  },
  portfolio: { 
    icon: Wallet, 
    color: 'from-violet-500 to-violet-600',
    image: 'https://cdn.abacus.ai/images/2ae38a00-3ca7-4ab2-a534-5d7f5b970b2a.png'
  },
  spot: { 
    icon: Coffee, 
    color: 'from-yellow-500 to-yellow-600',
    image: 'https://cdn.abacus.ai/images/2ae38a00-3ca7-4ab2-a534-5d7f5b970b2a.png'
  },
  business: { 
    icon: Home, 
    color: 'from-teal-500 to-teal-600',
    image: 'https://cdn.abacus.ai/images/2ae38a00-3ca7-4ab2-a534-5d7f5b970b2a.png'
  }
}

const allDistricts: DistrictType[] = ['square', 'school', 'market', 'field', 'council', 'workshop', 'guide', 'bureau', 'gateway', 'portfolio', 'spot', 'business']

function VillageExplorer({ district, onReturnToMap, onNextDistrict, discoveredDistricts }: VillageExplorerProps) {
  const t = useT()
  const [showGuideMessage, setShowGuideMessage] = useState(false)
  
  const config = districtConfig[district]
  const IconComponent = config.icon

  const getDistrictName = () => {
    const nameMap = {
      square: 'district_1_name',
      school: 'district_2_name', 
      market: 'district_3_name',
      field: 'district_4_name',
      council: 'district_5_name',
      workshop: 'district_6_name',
      guide: 'district_7_name',
      bureau: 'district_8_name',
      gateway: 'district_9_name',
      portfolio: 'district_10_name',
      spot: 'district_11_name',
      business: 'district_12_name'
    }
    return t(nameMap[district])
  }

  const getDistrictDetails = () => {
    const detailsMap = {
      square: 'district_1_details',
      school: 'district_2_details', 
      market: 'district_3_details',
      field: 'district_4_details',
      council: 'district_5_details',
      workshop: 'district_6_details',
      guide: 'district_7_details',
      bureau: 'district_8_details',
      gateway: 'district_9_details',
      portfolio: 'district_10_details',
      spot: 'district_11_details',
      business: 'district_12_details'
    }
    return t(detailsMap[district])
  }

  const getNextDistrict = () => {
    const currentIndex = allDistricts.indexOf(district)
    const undiscoveredDistricts = allDistricts.filter(d => !discoveredDistricts.has(d) && d !== district)
    return undiscoveredDistricts.length > 0 ? undiscoveredDistricts[0] : null
  }

  const nextDistrict = getNextDistrict()

  return (
    <section className="min-h-screen bg-gradient-to-b from-gray-50 to-white pt-20">
      {/* Hero Section for District */}
      <div className="relative h-96 overflow-hidden">
        <Image
          src={config.image}
          alt={getDistrictName()}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent" />
        
        {/* District Header */}
        <div className="absolute inset-0 flex items-center">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2 }}
              // @ts-ignore
              className="max-w-2xl"
            >
              <div className="flex items-center space-x-4 mb-4">
                <div className={`w-16 h-16 bg-gradient-to-br ${config.color} rounded-xl flex items-center justify-center shadow-lg`}>
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight">
                    {getDistrictName()}
                  </h1>
                  <div className="flex items-center space-x-2 mt-2">
                    <div className="w-3 h-3 bg-green-400 rounded-full" />
                    <span className="text-green-200 text-sm font-medium">Discovered</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Floating particles specific to district */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              // @ts-ignore
              className="absolute w-1 h-1 bg-white/30 rounded-full transform-gpu"
              style={{ willChange: 'transform, opacity' }}
              animate={{
                y: [-10, -30, -10],
                opacity: [0.2, 0.6, 0.2],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>

      {/* District Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
          // @ts-ignore
          className="flex items-center justify-between mb-8"
        >
          <Button
            variant="outline"
            onClick={onReturnToMap}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>{t('return_to_map')}</span>
          </Button>

          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <MapPin className="h-4 w-4" />
            <span>{t('current_location')}: {getDistrictName()}</span>
          </div>
        </motion.div>

        {/* District Description */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, delay: 0.2 }}
          // @ts-ignore
          className="bg-white rounded-2xl shadow-lg p-8 mb-8"
        >
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-700 leading-relaxed text-lg">
              {getDistrictDetails()}
            </p>
          </div>

          {/* Interactive Elements */}
          <div className="mt-8 grid md:grid-cols-3 gap-6">
            <div
              className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 text-center card-scale-effect"
            >
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-blue-900 mb-2">Community Focus</h3>
              <p className="text-blue-700 text-sm">Connect with like-minded entrepreneurs</p>
            </div>

            <div
              className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-xl p-6 text-center card-scale-effect"
            >
              <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mx-auto mb-3">
                <Lightbulb className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-emerald-900 mb-2">Innovation</h3>
              <p className="text-emerald-700 text-sm">Cutting-edge solutions and ideas</p>
            </div>

            <div
              className="bg-gradient-to-br from-amber-50 to-amber-100 rounded-xl p-6 text-center card-scale-effect"
            >
              <div className="w-12 h-12 bg-amber-500 rounded-full flex items-center justify-center mx-auto mb-3">
                <IconComponent className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-amber-900 mb-2">Specialized Service</h3>
              <p className="text-amber-700 text-sm">Expert guidance in this domain</p>
            </div>
          </div>
        </motion.div>

        {/* Village Guide Message */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          // @ts-ignore
          className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-2xl p-6 mb-8 border border-amber-200"
        >
          <div className="flex items-start space-x-4">
            <Image
              src="https://cdn.abacus.ai/images/7b4046da-2880-4890-b9e5-41ce73062874.png"
              alt="Village Guide"
              width={60}
              height={60}
              className="rounded-full border-3 border-amber-300"
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h4 className="font-semibold text-amber-900">{t('guide_tip')}</h4>
                <Lightbulb className="h-4 w-4 text-amber-600" />
              </div>
              <p className="text-amber-800 text-sm leading-relaxed">
                "This district represents the heart of {getDistrictName().toLowerCase()}. Take your time to explore the unique opportunities here. Each area of our village complements the others, creating a complete ecosystem for growth."
              </p>
            </div>
          </div>
        </motion.div>

        {/* Next District Suggestion */}
        {nextDistrict && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            // @ts-ignore
            className="bg-white rounded-2xl shadow-lg p-6"
          >
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Continue Your Journey</h3>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`w-12 h-12 bg-gradient-to-br ${districtConfig[nextDistrict].color} rounded-xl flex items-center justify-center transform-gpu`} style={{ willChange: 'transform' }}>
                  {React.createElement(districtConfig[nextDistrict].icon, { className: "w-6 h-6 text-white" })}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">
                    {t(`district_${allDistricts.indexOf(nextDistrict) + 1}_name`)}
                  </h4>
                  <p className="text-sm text-gray-500">
                    {t(`district_${allDistricts.indexOf(nextDistrict) + 1}_desc`)}
                  </p>
                </div>
              </div>
              <Button
                onClick={() => onNextDistrict(nextDistrict)}
                className="flex items-center space-x-2"
              >
                <span>{t('next_district')}</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </motion.div>
        )}

        {/* All Main Districts Discovered */}
        {discoveredDistricts.size >= 11 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8 }}
            // @ts-ignore
            className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl p-8 text-center text-white mt-8"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              // @ts-ignore
              className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 transform-gpu" style={{ willChange: 'transform' }}
            >
              <Compass className="w-8 h-8" />
            </motion.div>
            <h3 className="text-2xl font-bold mb-2">Village Master Explorer!</h3>
            <p className="mb-4">You've discovered all the main districts of نتاج village. You're now ready to join our community!</p>
            <Button
              variant="secondary"
              onClick={onReturnToMap}
              className="bg-white text-green-600 hover:bg-gray-100"
            >
              View Complete Map
            </Button>
          </motion.div>
        )}
      </div>
    </section>
  )
}

export const MemoizedVillageExplorer = React.memo(VillageExplorer);
export { VillageExplorer };
