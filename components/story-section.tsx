
'use client'
import React from 'react'
import { motion } from 'framer-motion'
import { useT } from './language-provider';
import { ArrowRight, Heart, Globe, Users, Lightbulb } from 'lucide-react'
import { Button } from './ui/button'
import Image from 'next/image'

export function StorySection() {
  const t = useT();

  const values = [
    { icon: Heart, key: 'community', color: 'text-rose-500' },
    { icon: Globe, key: 'global', color: 'text-blue-500' },
    { icon: Users, key: 'local', color: 'text-green-500' },
    { icon: Lightbulb, key: 'innovation', color: 'text-amber-500' }
  ]

  return (
    <section id="story" className="py-24 bg-gradient-to-b from-white to-amber-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            // @ts-ignore
            className="space-y-8"
          >
            <div>
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                // @ts-ignore
                className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
              >
                {t('story_title')}
              </motion.h2>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                // @ts-ignore
                className="text-xl text-amber-600 font-medium"
              >
                {t('story_subtitle')}
              </motion.p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              // @ts-ignore
              className="prose prose-lg text-gray-700 max-w-none"
            >
              <p className="leading-relaxed mb-6">
                {t('story_content')}
              </p>
              <p className="leading-relaxed font-medium text-amber-800 bg-amber-50 p-4 rounded-lg border-l-4 border-amber-400">
                {t('story_philosophy')}
              </p>
            </motion.div>

            {/* Village Values */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              // @ts-ignore
              className="grid grid-cols-2 md:grid-cols-4 gap-4"
            >
              {values.map((value, index) => (
                <motion.div
                  key={value.key}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                  // @ts-ignore
                  className="bg-white rounded-xl p-4 shadow-lg text-center border border-gray-100"
                >
                  <value.icon className={`w-8 h-8 ${value.color} mx-auto mb-2`} />
                  <p className="text-sm font-medium text-gray-700 capitalize">
                    {value.key}
                  </p>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
            >
              <p className="text-lg text-amber-700 font-medium mb-6">
                {t('story_exploration')}
              </p>
              <Button
                onClick={() => document.getElementById('map')?.scrollIntoView({ behavior: 'smooth' })}
                className="bg-amber-600 hover:bg-amber-700 text-white"
              >
                {t('start_exploration')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </motion.div>
          </motion.div>

          {/* Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            // @ts-ignore
            className="relative"
          >
            <div className="relative">
              <motion.div
                animate={{ 
                  rotate: [0, 5, -5, 0],
                  scale: [1, 1.02, 1]
                }}
                // @ts-ignore
                className="relative aspect-[4/3] rounded-3xl overflow-hidden shadow-2xl"
              >
                <Image
                  src="/village-collaboration.jpg"
                  alt="Netaj Village Community"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-amber-900/20 to-transparent" />
              </motion.div>

              {/* Floating Elements */}
              <motion.div
                animate={{ y: [-10, 10, -10] }}
                // @ts-ignore
                className="absolute -top-6 -right-6 w-16 h-16 bg-amber-500 rounded-full shadow-lg flex items-center justify-center"
              >
                <Heart className="w-8 h-8 text-white" />
              </motion.div>

              <motion.div
                animate={{ y: [10, -10, 10] }}
                // @ts-ignore
                className="absolute -bottom-4 -left-4 w-12 h-12 bg-green-500 rounded-full shadow-lg flex items-center justify-center"
              >
                <Users className="w-6 h-6 text-white" />
              </motion.div>

              {/* Stats Overlay */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                viewport={{ once: true }}
                // @ts-ignore
                className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-xl p-4"
              >
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-amber-600">9</div>
                    <div className="text-xs text-gray-600">Districts</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">∞</div>
                    <div className="text-xs text-gray-600">Ideas</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600">1</div>
                    <div className="text-xs text-gray-600">Village</div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
