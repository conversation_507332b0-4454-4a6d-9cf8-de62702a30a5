'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Eye, EyeOff, AlertTriangle, RefreshCw } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

import { RecurrenceRule, PeriodFormData } from '@/lib/types';
import { formatPeriodDuration } from '@/lib/types';
import { recurrenceEngine } from '@/lib/services/recurrence-engine';

interface PeriodPreviewProps {
  basePeriod: { start: Date; end: Date };
  recurrenceRule: RecurrenceRule;
  maxPreview?: number;
  className?: string;
}

interface GeneratedPeriod {
  start: Date;
  end: Date;
}

export const PeriodPreview: React.FC<PeriodPreviewProps> = ({
  basePeriod,
  recurrenceRule,
  maxPreview = 10,
  className = ''
}) => {
  const [previewPeriods, setPreviewPeriods] = useState<GeneratedPeriod[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAll, setShowAll] = useState(false);

  // Generate preview periods
  useEffect(() => {
    const generatePreview = async () => {
      setIsGenerating(true);
      setError(null);

      try {
        // Create a preview version of the recurrence rule
        const previewRule: RecurrenceRule = {
          ...recurrenceRule,
          count: Math.min(recurrenceRule.count || maxPreview, maxPreview)
        };

        // Remove endDate for preview to avoid cutting off early
        if (previewRule.endDate) {
          delete previewRule.endDate;
        }

        const periods = recurrenceEngine.generatePeriods(basePeriod, previewRule);
        setPreviewPeriods(periods);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to generate preview');
        setPreviewPeriods([]);
      } finally {
        setIsGenerating(false);
      }
    };

    generatePreview();
  }, [basePeriod, recurrenceRule, maxPreview]);

  const displayedPeriods = showAll ? previewPeriods : previewPeriods.slice(0, 5);
  const hasMorePeriods = previewPeriods.length > 5;

  const formatDateTimeRange = (start: Date, end: Date): string => {
    const sameDay = start.toDateString() === end.toDateString();
    
    if (sameDay) {
      return `${start.toLocaleDateString()} ${start.toLocaleTimeString()} - ${end.toLocaleTimeString()}`;
    } else {
      return `${start.toLocaleDateString()} ${start.toLocaleTimeString()} - ${end.toLocaleDateString()} ${end.toLocaleTimeString()}`;
    }
  };

  const getTotalDuration = (): string => {
    if (previewPeriods.length === 0) return '0 minutes';
    
    const totalMs = previewPeriods.reduce((total, period) => {
      return total + (period.end.getTime() - period.start.getTime());
    }, 0);

    const hours = Math.floor(totalMs / (1000 * 60 * 60));
    const minutes = Math.floor((totalMs % (1000 * 60 * 60)) / (1000 * 60));

    const parts: string[] = [];
    if (hours > 0) parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
    if (minutes > 0) parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);

    return parts.length > 0 ? parts.join(' and ') : '0 minutes';
  };

  const getDateRange = (): string => {
    if (previewPeriods.length === 0) return '';
    
    const firstPeriod = previewPeriods[0];
    const lastPeriod = previewPeriods[previewPeriods.length - 1];
    
    return `${firstPeriod.start.toLocaleDateString()} - ${lastPeriod.start.toLocaleDateString()}`;
  };

  if (isGenerating) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            Generating Preview...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-medium">Preview Generation Failed</p>
            <p className="text-sm">{error}</p>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (previewPeriods.length === 0) {
    return (
      <Alert className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No periods would be generated with the current recurrence settings.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Period Preview
          </CardTitle>
          {hasMorePeriods && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAll(!showAll)}
              className="flex items-center gap-2"
            >
              {showAll ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showAll ? 'Show Less' : `Show All (${previewPeriods.length})`}
            </Button>
          )}
        </div>
        <CardDescription>
          Preview of the first {Math.min(maxPreview, previewPeriods.length)} periods that will be created
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{previewPeriods.length}</div>
            <div className="text-sm text-muted-foreground">Total Periods</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{getTotalDuration()}</div>
            <div className="text-sm text-muted-foreground">Total Duration</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">{getDateRange()}</div>
            <div className="text-sm text-muted-foreground">Date Range</div>
          </div>
        </div>

        <Separator />

        {/* Period List */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-semibold">Generated Periods</h4>
            <Badge variant="outline">
              Showing {displayedPeriods.length} of {previewPeriods.length}
            </Badge>
          </div>

          <ScrollArea className="h-64">
            <div className="space-y-2 pr-4">
              {displayedPeriods.map((period, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <Badge 
                      variant={index === 0 ? "default" : "secondary"} 
                      className="w-8 h-8 rounded-full flex items-center justify-center p-0"
                    >
                      {index + 1}
                    </Badge>
                    <div>
                      <div className="font-medium text-sm">
                        {formatDateTimeRange(period.start, period.end)}
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatPeriodDuration(period.start, period.end)}
                        {index === 0 && (
                          <Badge variant="outline" className="ml-2 text-xs">
                            Original
                          </Badge>
                        )}
                        {index > 0 && (
                          <Badge variant="outline" className="ml-2 text-xs">
                            Recurring
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-xs text-muted-foreground">
                      {period.start.toLocaleDateString('en-US', { weekday: 'short' })}
                    </div>
                  </div>
                </div>
              ))}

              {!showAll && hasMorePeriods && (
                <div className="text-center py-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAll(true)}
                    className="text-muted-foreground"
                  >
                    ... and {previewPeriods.length - 5} more periods
                  </Button>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Warning for large number of periods */}
        {previewPeriods.length > 50 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">Large Number of Periods</p>
                <p className="text-sm">
                  This recurrence pattern will create {previewPeriods.length} periods. 
                  Consider adjusting the end conditions if this seems excessive.
                </p>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Truncation Notice */}
        {previewPeriods.length >= maxPreview && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">Preview Limited</p>
                <p className="text-sm">
                  Only showing the first {maxPreview} periods. The actual recurrence may generate more periods based on your end conditions.
                </p>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default PeriodPreview;