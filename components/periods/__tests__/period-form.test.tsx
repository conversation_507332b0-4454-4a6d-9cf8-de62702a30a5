import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { PeriodForm } from '../period-form';
import { Resource } from '@/lib/types';

// Mock the hooks and services
vi.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    watch: () => [{ start: new Date(), end: new Date(), isRecurring: false }],
    formState: { errors: {}, isValid: true }
  }),
  useFieldArray: () => ({
    fields: [{ id: '1' }],
    append: vi.fn(),
    remove: vi.fn(),
    update: vi.fn()
  })
}));

vi.mock('@/lib/services/recurrence-engine', () => ({
  recurrenceEngine: {
    generatePeriods: vi.fn(() => []),
    previewRecurringPeriods: vi.fn(() => [])
  }
}));

const mockResources: Resource[] = [
  {
    id: 1,
    name: 'Conference Room A',
    type: 'MEETING_ROOM',
    basePrice: 100,
    amenities: [],
    stageStyles: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

describe('PeriodForm', () => {
  it('renders without crashing', () => {
    const mockOnPeriodsChange = vi.fn();
    
    render(
      <PeriodForm
        selectedResources={mockResources}
        onPeriodsChange={mockOnPeriodsChange}
      />
    );
    
    expect(screen.getByText('Time Periods')).toBeInTheDocument();
  });

  it('displays selected resources', () => {
    const mockOnPeriodsChange = vi.fn();
    
    render(
      <PeriodForm
        selectedResources={mockResources}
        onPeriodsChange={mockOnPeriodsChange}
      />
    );
    
    expect(screen.getByText('Conference Room A')).toBeInTheDocument();
  });
});