import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { RecurringPeriodManager } from '../recurring-period-manager';
import { Period, RecurrenceRule } from '@/lib/types';

// Mock the UI components
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogDescription: ({ children }: any) => <div data-testid="dialog-description">{children}</div>,
  DialogFooter: ({ children }: any) => <div data-testid="dialog-footer">{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <div data-testid="dialog-title">{children}</div>,
}));

vi.mock('@/components/ui/radio-group', () => ({
  RadioGroup: ({ children, onValueChange, value }: any) => (
    <div data-testid="radio-group" data-value={value} onChange={onValueChange}>
      {children}
    </div>
  ),
  RadioGroupItem: ({ value, id }: any) => (
    <input
      type="radio"
      value={value}
      id={id}
      data-testid={`radio-${value}`}
      onChange={(e) => e.target.closest('[data-testid="radio-group"]')?.dispatchEvent(
        new CustomEvent('change', { detail: e.target.value })
      )}
    />
  ),
}));

describe('RecurringPeriodManager', () => {
  const mockRecurrenceRule: RecurrenceRule = {
    type: 'weekly',
    interval: 1,
    daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
    count: 5
  };

  const mockParentPeriod: Period = {
    id: 1,
    bookingId: 1,
    start: new Date('2024-01-01T09:00:00'),
    end: new Date('2024-01-01T10:00:00'),
    isRecurring: true,
    recurrenceRule: mockRecurrenceRule,
    parentPeriodId: null,
    booking: {} as any,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockChildPeriods: Period[] = [
    {
      id: 2,
      bookingId: 1,
      start: new Date('2024-01-03T09:00:00'),
      end: new Date('2024-01-03T10:00:00'),
      isRecurring: true,
      recurrenceRule: mockRecurrenceRule,
      parentPeriodId: 1,
      booking: {} as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 3,
      bookingId: 1,
      start: new Date('2024-01-05T09:00:00'),
      end: new Date('2024-01-05T10:00:00'),
      isRecurring: true,
      recurrenceRule: mockRecurrenceRule,
      parentPeriodId: 1,
      booking: {} as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  const mockStandalonePeriod: Period = {
    id: 4,
    bookingId: 1,
    start: new Date('2024-01-10T14:00:00'),
    end: new Date('2024-01-10T15:00:00'),
    isRecurring: false,
    parentPeriodId: null,
    booking: {} as any,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockOnEditPeriod = vi.fn();
  const mockOnDeletePeriod = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders empty state when no periods are provided', () => {
    render(<RecurringPeriodManager periods={[]} />);
    
    expect(screen.getByText('No periods have been created yet. Add periods using the form above.')).toBeInTheDocument();
  });

  it('displays recurring series correctly', () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(<RecurringPeriodManager periods={periods} />);
    
    expect(screen.getByText('Recurring Series (1)')).toBeInTheDocument();
    expect(screen.getByText('3 periods')).toBeInTheDocument();
    expect(screen.getByText('Weekly (3 days)')).toBeInTheDocument();
  });

  it('displays standalone periods correctly', () => {
    const periods = [mockStandalonePeriod];
    
    render(<RecurringPeriodManager periods={periods} />);
    
    expect(screen.getByText('Individual Periods (1)')).toBeInTheDocument();
  });

  it('displays both recurring series and standalone periods', () => {
    const periods = [mockParentPeriod, ...mockChildPeriods, mockStandalonePeriod];
    
    render(<RecurringPeriodManager periods={periods} />);
    
    expect(screen.getByText('Recurring Series (1)')).toBeInTheDocument();
    expect(screen.getByText('Individual Periods (1)')).toBeInTheDocument();
  });

  it('shows edit dialog when edit button is clicked', async () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(
      <RecurringPeriodManager 
        periods={periods} 
        onEditPeriod={mockOnEditPeriod}
      />
    );
    
    const editButtons = screen.getAllByTitle('Edit period');
    fireEvent.click(editButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.getByText('Edit Recurring Period')).toBeInTheDocument();
    });
  });

  it('shows delete dialog when delete button is clicked', async () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(
      <RecurringPeriodManager 
        periods={periods} 
        onDeletePeriod={mockOnDeletePeriod}
      />
    );
    
    const deleteButtons = screen.getAllByTitle('Delete period');
    fireEvent.click(deleteButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.getByText('Delete Recurring Period')).toBeInTheDocument();
    });
  });

  it('displays series information in dialog', async () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(
      <RecurringPeriodManager 
        periods={periods} 
        onEditPeriod={mockOnEditPeriod}
      />
    );
    
    const editButtons = screen.getAllByTitle('Edit period');
    fireEvent.click(editButtons[0]);
    
    await waitFor(() => {
      // Use getAllByText to handle multiple instances and check the dialog specifically
      const patternTexts = screen.getAllByText(/Pattern: Weekly \(3 days\)/);
      expect(patternTexts.length).toBeGreaterThan(0);
      expect(screen.getByText('Total periods: 3')).toBeInTheDocument();
      expect(screen.getByText('Future periods: 2')).toBeInTheDocument();
    });
  });

  it('provides radio options for edit/delete actions', async () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(
      <RecurringPeriodManager 
        periods={periods} 
        onEditPeriod={mockOnEditPeriod}
      />
    );
    
    const editButtons = screen.getAllByTitle('Edit period');
    fireEvent.click(editButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByTestId('radio-single')).toBeInTheDocument();
      expect(screen.getByTestId('radio-future')).toBeInTheDocument();
      expect(screen.getByTestId('radio-series')).toBeInTheDocument();
    });
  });

  it('calls onEditPeriod with correct parameters when confirmed', async () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(
      <RecurringPeriodManager 
        periods={periods} 
        onEditPeriod={mockOnEditPeriod}
      />
    );
    
    const editButtons = screen.getAllByTitle('Edit period');
    fireEvent.click(editButtons[0]);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Edit Period');
      fireEvent.click(confirmButton);
    });
    
    expect(mockOnEditPeriod).toHaveBeenCalledWith(1, 'single');
  });

  it('calls onDeletePeriod with correct parameters when confirmed', async () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(
      <RecurringPeriodManager 
        periods={periods} 
        onDeletePeriod={mockOnDeletePeriod}
      />
    );
    
    const deleteButtons = screen.getAllByTitle('Delete period');
    fireEvent.click(deleteButtons[0]);
    
    await waitFor(() => {
      const confirmButton = screen.getByText('Delete Period');
      fireEvent.click(confirmButton);
    });
    
    expect(mockOnDeletePeriod).toHaveBeenCalledWith(1, 'single');
  });

  it('shows correct period formatting', () => {
    const periods = [mockParentPeriod];
    
    render(<RecurringPeriodManager periods={periods} />);
    
    expect(screen.getByText('1/1/2024 9:00:00 AM - 10:00:00 AM')).toBeInTheDocument();
  });

  it('shows series origin badge for parent period', () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(<RecurringPeriodManager periods={periods} />);
    
    expect(screen.getByText('Series Origin')).toBeInTheDocument();
  });

  it('shows recurring badge for child periods', () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(<RecurringPeriodManager periods={periods} />);
    
    const recurringBadges = screen.getAllByText('Recurring');
    expect(recurringBadges).toHaveLength(2); // Two child periods
  });

  it('handles periods without edit/delete handlers gracefully', () => {
    const periods = [mockParentPeriod, ...mockChildPeriods];
    
    render(<RecurringPeriodManager periods={periods} />);
    
    // Should not show edit/delete buttons when handlers are not provided
    expect(screen.queryByTitle('Edit period')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Delete period')).not.toBeInTheDocument();
  });

  it('groups periods correctly by recurring series', () => {
    // Create two separate recurring series
    const secondParentPeriod: Period = {
      ...mockParentPeriod,
      id: 5,
      start: new Date('2024-02-01T09:00:00'),
      end: new Date('2024-02-01T10:00:00'),
    };

    const secondChildPeriod: Period = {
      ...mockChildPeriods[0],
      id: 6,
      parentPeriodId: 5,
      start: new Date('2024-02-03T09:00:00'),
      end: new Date('2024-02-03T10:00:00'),
    };

    const periods = [
      mockParentPeriod, 
      ...mockChildPeriods, 
      secondParentPeriod, 
      secondChildPeriod,
      mockStandalonePeriod
    ];
    
    render(<RecurringPeriodManager periods={periods} />);
    
    expect(screen.getByText('Recurring Series (2)')).toBeInTheDocument();
    expect(screen.getByText('Individual Periods (1)')).toBeInTheDocument();
  });
});