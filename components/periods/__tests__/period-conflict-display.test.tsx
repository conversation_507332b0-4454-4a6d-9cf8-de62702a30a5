import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { 
  PeriodConflictDisplay, 
  ConflictCheckDisplay, 
  ConflictIndicator,
  ResourceUtilizationDisplay 
} from '../period-conflict-display';
import { PeriodConflict, ConflictCheckResult, BookingStatus } from '@/lib/types';

// Mock the UI components
vi.mock('@/components/ui/alert', () => ({
  Alert: ({ children, className, variant }: any) => (
    <div data-testid="alert" className={className} data-variant={variant}>
      {children}
    </div>
  ),
  AlertDescription: ({ children }: any) => (
    <div data-testid="alert-description">{children}</div>
  ),
  AlertTitle: ({ children }: any) => (
    <div data-testid="alert-title">{children}</div>
  ),
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant, className, onClick }: any) => (
    <span 
      data-testid="badge" 
      className={className} 
      data-variant={variant}
      onClick={onClick}
    >
      {children}
    </span>
  ),
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardContent: ({ children }: any) => (
    <div data-testid="card-content">{children}</div>
  ),
  CardDescription: ({ children }: any) => (
    <div data-testid="card-description">{children}</div>
  ),
  CardHeader: ({ children }: any) => (
    <div data-testid="card-header">{children}</div>
  ),
  CardTitle: ({ children }: any) => (
    <div data-testid="card-title">{children}</div>
  ),
}));

vi.mock('@/components/ui/separator', () => ({
  Separator: () => <hr data-testid="separator" />,
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  AlertTriangle: () => <div data-testid="alert-triangle-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  Users: () => <div data-testid="users-icon" />,
  MapPin: () => <div data-testid="map-pin-icon" />,
}));

// Mock data used across tests
const mockConflicts: PeriodConflict[] = [
  {
    periodId: 0,
    conflictingPeriodId: 1,
    conflictingResources: [
      { id: 1, name: 'Conference Room A' },
      { id: 2, name: 'Conference Room B' }
    ],
    overlapStart: new Date('2024-01-01T11:00:00Z'),
    overlapEnd: new Date('2024-01-01T12:00:00Z'),
    conflictingBooking: {
      id: 2,
      customerName: 'John Doe',
      status: 'CONFIRMED' as BookingStatus
    }
  }
];

describe('PeriodConflictDisplay', () => {
  it('should render nothing when no conflicts provided', () => {
    render(<PeriodConflictDisplay conflicts={[]} />);
    expect(screen.queryByTestId('card')).not.toBeInTheDocument();
  });

  it('should render conflict information correctly', () => {
    render(<PeriodConflictDisplay conflicts={mockConflicts} />);
    
    expect(screen.getByTestId('card')).toBeInTheDocument();
    expect(screen.getByText('Conflict with Booking #2')).toBeInTheDocument();
    expect(screen.getByText('Customer: John Doe')).toBeInTheDocument();
    expect(screen.getByText('Conference Room A')).toBeInTheDocument();
    expect(screen.getByText('Conference Room B')).toBeInTheDocument();
  });

  it('should call onResolveConflict when resolve button is clicked', () => {
    const mockResolveConflict = vi.fn();
    render(
      <PeriodConflictDisplay 
        conflicts={mockConflicts} 
        onResolveConflict={mockResolveConflict}
      />
    );
    
    const resolveButton = screen.getByText('View Conflicting Booking');
    fireEvent.click(resolveButton);
    
    expect(mockResolveConflict).toHaveBeenCalledWith(1);
  });
});

describe('ConflictCheckDisplay', () => {
  it('should show success message when no conflicts', () => {
    const noConflictResult: ConflictCheckResult = {
      hasConflicts: false,
      conflicts: [],
      affectedResources: [],
      message: 'No conflicts detected'
    };

    render(<ConflictCheckDisplay conflictResult={noConflictResult} />);
    
    expect(screen.getByTestId('alert')).toBeInTheDocument();
    expect(screen.getByText('No Conflicts Detected')).toBeInTheDocument();
    expect(screen.getByText('No conflicts detected')).toBeInTheDocument();
  });

  it('should show conflict details when conflicts exist', () => {
    const conflictResult: ConflictCheckResult = {
      hasConflicts: true,
      conflicts: mockConflicts,
      affectedResources: [
        { id: 1, name: 'Conference Room A' },
        { id: 2, name: 'Conference Room B' }
      ],
      message: 'Found 1 conflict(s) across 2 resource(s)'
    };

    render(<ConflictCheckDisplay conflictResult={conflictResult} />);
    
    expect(screen.getByText('Scheduling Conflicts Detected')).toBeInTheDocument();
    expect(screen.getByText('Found 1 conflict(s) across 2 resource(s)')).toBeInTheDocument();
  });
});

describe('ConflictIndicator', () => {
  it('should render nothing when conflict count is 0', () => {
    render(<ConflictIndicator conflictCount={0} />);
    expect(screen.queryByTestId('badge')).not.toBeInTheDocument();
  });

  it('should render conflict count correctly', () => {
    render(<ConflictIndicator conflictCount={3} />);
    
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(screen.getByText('3 conflicts')).toBeInTheDocument();
  });

  it('should handle singular conflict correctly', () => {
    render(<ConflictIndicator conflictCount={1} />);
    expect(screen.getByText('1 conflict')).toBeInTheDocument();
  });

  it('should call onClick when clicked', () => {
    const mockOnClick = vi.fn();
    render(<ConflictIndicator conflictCount={2} onClick={mockOnClick} />);
    
    const badge = screen.getByTestId('badge');
    fireEvent.click(badge);
    
    expect(mockOnClick).toHaveBeenCalled();
  });
});