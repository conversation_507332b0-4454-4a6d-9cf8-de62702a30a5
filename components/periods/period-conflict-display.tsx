'use client';

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { AlertTriangle, Clock, Users, MapPin } from 'lucide-react';
import { PeriodConflict, ConflictCheckResult } from '@/lib/types';
import { formatPeriodDuration } from '@/lib/types';

interface PeriodConflictDisplayProps {
  conflicts: PeriodConflict[];
  onResolveConflict?: (conflictId: number) => void;
  className?: string;
}

interface ConflictCheckDisplayProps {
  conflictResult: ConflictCheckResult;
  onResolveConflict?: (conflictId: number) => void;
  className?: string;
}

/**
 * Component to display individual period conflicts
 */
export const PeriodConflictDisplay: React.FC<PeriodConflictDisplayProps> = ({
  conflicts,
  onResolveConflict,
  className = ''
}) => {
  if (!conflicts || conflicts.length === 0) {
    return null;
  }

  // Group conflicts by conflicting booking for better organization
  const conflictsByBooking = conflicts.reduce((acc, conflict) => {
    if (!conflict || !conflict.conflictingBooking) {
      console.warn('PeriodConflictDisplay: Invalid conflict object', conflict);
      return acc;
    }

    const bookingId = conflict.conflictingBooking.id;
    if (!acc[bookingId]) {
      acc[bookingId] = {
        booking: conflict.conflictingBooking,
        conflicts: []
      };
    }
    acc[bookingId].conflicts.push(conflict);
    return acc;
  }, {} as Record<number, { booking: PeriodConflict['conflictingBooking']; conflicts: PeriodConflict[] }>);

  return (
    <div className={`space-y-4 ${className}`}>
      {Object.values(conflictsByBooking).map(({ booking, conflicts: bookingConflicts }) => (
        <Card key={booking.id} className="border-destructive/50 bg-destructive/5">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <CardTitle className="text-lg">
                Conflict with Booking #{booking.id}
              </CardTitle>
              <Badge 
                variant={booking.status === 'CONFIRMED' ? 'default' : 'secondary'}
                className="ml-auto"
              >
                {booking.status}
              </Badge>
            </div>
            <CardDescription className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Customer: {booking.customerName}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {bookingConflicts.map((conflict, index) => (
              <div key={`${conflict.conflictingPeriodId}-${index}`} className="space-y-2">
                {index > 0 && <Separator />}
                
                {/* Overlap Time Information */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>
                    {(() => {
                      const toDate = (v: any) => v instanceof Date ? v : (v ? new Date(v) : null);
                      const os = toDate(conflict.overlapStart);
                      const oe = toDate(conflict.overlapEnd);
                      const hasValidOs = os && !isNaN(os.getTime());
                      const hasValidOe = oe && !isNaN(oe.getTime());
                      return (
                        <>
                          Overlap: {hasValidOs ? os.toLocaleDateString() : 'Unknown'}{' '}
                          {hasValidOs ? os.toLocaleTimeString() : ''} -{' '}
                          {hasValidOe ? oe.toLocaleTimeString() : ''}{' '}
                          ({hasValidOs && hasValidOe ? formatPeriodDuration(os, oe) : 'Unknown duration'})
                        </>
                      );
                    })()}
                  </span>
                </div>

                {/* Conflicting Resources */}
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                  <div className="flex-1">
                    <span className="text-sm font-medium">Conflicting Resources:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {conflict.conflictingResources && conflict.conflictingResources.length > 0 ? (
                        conflict.conflictingResources.map((resource, index) => (
                          <Badge key={resource?.id || index} variant="outline" className="text-xs">
                            {resource?.name || 'Unknown Resource'}
                          </Badge>
                        ))
                      ) : (
                        <Badge variant="outline" className="text-xs">No resources specified</Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* Resolve Action */}
                {onResolveConflict && (
                  <div className="flex justify-end">
                    <button
                      onClick={() => onResolveConflict(conflict.conflictingPeriodId)}
                      className="text-xs text-primary hover:underline"
                    >
                      View Conflicting Booking
                    </button>
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

/**
 * Component to display complete conflict check results
 */
export const ConflictCheckDisplay: React.FC<ConflictCheckDisplayProps> = ({
  conflictResult,
  onResolveConflict,
  className = ''
}) => {
  console.log('ConflictCheckDisplay received:', conflictResult);

  // Safety check for undefined or incomplete conflictResult
  if (!conflictResult) {
    console.warn('ConflictCheckDisplay: conflictResult is undefined');
    return (
      <Alert className={`border-gray-200 bg-gray-50 ${className}`}>
        <AlertTitle className="text-gray-800">Checking Conflicts...</AlertTitle>
        <AlertDescription className="text-gray-700">
          Please wait while we check for scheduling conflicts.
        </AlertDescription>
      </Alert>
    );
  }

  // Safety check for required properties
  if (typeof conflictResult.hasConflicts === 'undefined') {
    console.warn('ConflictCheckDisplay: conflictResult.hasConflicts is undefined', conflictResult);
    return (
      <Alert className={`border-orange-200 bg-orange-50 ${className}`}>
        <AlertTitle className="text-orange-800">Conflict Check In Progress</AlertTitle>
        <AlertDescription className="text-orange-700">
          Verifying your booking schedule...
        </AlertDescription>
      </Alert>
    );
  }

  // Additional safety check for the message property that might be causing the "undefined" display
  const safeMessage = conflictResult.message || 'Conflict check completed';
  
  console.log('ConflictCheckDisplay safe message:', safeMessage);

  if (!conflictResult.hasConflicts) {
    return (
      <Alert className={`border-green-200 bg-green-50 ${className}`}>
        <AlertTitle className="text-green-800">No Conflicts Detected</AlertTitle>
        <AlertDescription className="text-green-700">
          {safeMessage === 'Conflict check completed' ? 'Your booking schedule is available.' : safeMessage}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Summary Alert */}
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Scheduling Conflicts Detected</AlertTitle>
        <AlertDescription>
          {safeMessage === 'Conflict check completed' ? 'Scheduling conflicts were found with your booking.' : safeMessage}
          {conflictResult.affectedResources && conflictResult.affectedResources.length > 0 && (
            <div className="mt-2">
              <span className="font-medium">Affected Resources: </span>
              {conflictResult.affectedResources.map((resource, index) => (
                <span key={resource?.id || index}>
                  {resource?.name || 'Unknown Resource'}
                  {index < conflictResult.affectedResources.length - 1 ? ', ' : ''}
                </span>
              ))}
            </div>
          )}
        </AlertDescription>
      </Alert>

      {/* Detailed Conflicts */}
      <PeriodConflictDisplay
        conflicts={conflictResult.conflicts || []}
        onResolveConflict={onResolveConflict}
      />
    </div>
  );
};

/**
 * Compact conflict indicator for inline display
 */
export const ConflictIndicator: React.FC<{
  conflictCount: number;
  onClick?: () => void;
  className?: string;
}> = ({ conflictCount, onClick, className = '' }) => {
  if (conflictCount === 0) {
    return null;
  }

  return (
    <Badge
      variant="destructive"
      className={`cursor-pointer hover:bg-destructive/90 ${className}`}
      onClick={onClick}
    >
      <AlertTriangle className="h-3 w-3 mr-1" />
      {conflictCount} conflict{conflictCount !== 1 ? 's' : ''}
    </Badge>
  );
};

/**
 * Resource utilization display component
 */
export const ResourceUtilizationDisplay: React.FC<{
  utilization: {
    resourceId: number;
    resourceName: string;
    totalBookedTime: number;
    totalAvailableTime: number;
    utilizationPercentage: number;
    conflictingBookings: {
      bookingId: number;
      customerName: string;
      periods: { start: Date; end: Date }[];
    }[];
  }[];
  className?: string;
}> = ({ utilization, className = '' }) => {
  if (utilization.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <h3 className="text-lg font-semibold">Resource Utilization</h3>
      {utilization.map((resource) => (
        <Card key={resource.resourceId}>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">{resource.resourceName}</CardTitle>
            <CardDescription>
              Utilization: {resource.utilizationPercentage.toFixed(1)}%
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Total Booked Time:</span>
                <span>{Math.round(resource.totalBookedTime / (1000 * 60 * 60))} hours</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Total Available Time:</span>
                <span>{Math.round(resource.totalAvailableTime / (1000 * 60 * 60))} hours</span>
              </div>
              
              {/* Progress bar */}
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{ width: `${Math.min(resource.utilizationPercentage, 100)}%` }}
                />
              </div>

              {/* Conflicting bookings */}
              {resource.conflictingBookings.length > 0 && (
                <div className="mt-3">
                  <span className="text-sm font-medium">Existing Bookings:</span>
                  <div className="mt-1 space-y-1">
                    {resource.conflictingBookings.map((booking) => (
                      <div key={booking.bookingId} className="text-xs text-muted-foreground">
                        Booking #{booking.bookingId} - {booking.customerName} 
                        ({booking.periods.length} period{booking.periods.length !== 1 ? 's' : ''})
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default PeriodConflictDisplay;