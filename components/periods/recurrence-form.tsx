'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Calendar, Clock, Eye, AlertCircle } from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';

import { RecurrenceRule, PeriodFormData } from '@/lib/types';
import { recurrenceRuleSchema } from '@/lib/validations/period';
import { formatPeriodDuration } from '@/lib/types';
import { PeriodPreview } from './period-preview';

interface RecurrenceFormProps {
  recurrenceRule?: RecurrenceRule | null;
  onRecurrenceChange: (rule: RecurrenceRule | null) => void;
  startDate: Date;
  endDate: Date;
  className?: string;
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday', short: 'Sun' },
  { value: 1, label: 'Monday', short: 'Mon' },
  { value: 2, label: 'Tuesday', short: 'Tue' },
  { value: 3, label: 'Wednesday', short: 'Wed' },
  { value: 4, label: 'Thursday', short: 'Thu' },
  { value: 5, label: 'Friday', short: 'Fri' },
  { value: 6, label: 'Saturday', short: 'Sat' }
];

const MONTHS = [
  { value: 1, label: 'January' },
  { value: 2, label: 'February' },
  { value: 3, label: 'March' },
  { value: 4, label: 'April' },
  { value: 5, label: 'May' },
  { value: 6, label: 'June' },
  { value: 7, label: 'July' },
  { value: 8, label: 'August' },
  { value: 9, label: 'September' },
  { value: 10, label: 'October' },
  { value: 11, label: 'November' },
  { value: 12, label: 'December' }
];

const WEEK_OF_MONTH_OPTIONS = [
  { value: 1, label: 'First' },
  { value: 2, label: 'Second' },
  { value: 3, label: 'Third' },
  { value: 4, label: 'Fourth' },
  { value: -1, label: 'Last' }
];

export const RecurrenceForm: React.FC<RecurrenceFormProps> = ({
  recurrenceRule,
  onRecurrenceChange,
  startDate,
  endDate,
  className = ''
}) => {
  const [showPreview, setShowPreview] = useState(false);

  const form = useForm<RecurrenceRule>({
    resolver: zodResolver(recurrenceRuleSchema),
    defaultValues: recurrenceRule || {
      type: 'daily',
      interval: 1,
      count: 10
    }
  });

  const watchedRule = form.watch();
  const recurrenceType = form.watch('type');

  // Update parent when form changes
  useEffect(() => {
    const subscription = form.watch((value) => {
      if (form.formState.isValid) {
        onRecurrenceChange(value as RecurrenceRule);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, onRecurrenceChange]);

  const formatDateTime = (date: Date): string => {
    return date.toISOString().slice(0, 10);
  };

  const parseDate = (dateString: string): Date => {
    return new Date(dateString);
  };

  const getRecurrenceDescription = (rule: RecurrenceRule): string => {
    switch (rule.type) {
      case 'daily':
        return rule.interval === 1 ? 'Every day' : `Every ${rule.interval} days`;
      case 'weekly':
        const days = rule.daysOfWeek?.map(day => DAYS_OF_WEEK[day].short).join(', ') || '';
        const weekInterval = rule.interval === 1 ? 'Every week' : `Every ${rule.interval} weeks`;
        return `${weekInterval} on ${days}`;
      case 'monthly':
        const monthInterval = rule.interval === 1 ? 'Every month' : `Every ${rule.interval} months`;
        if (rule.dayOfMonth) {
          return `${monthInterval} on day ${rule.dayOfMonth}`;
        } else if (rule.weekOfMonth && rule.dayOfWeek !== undefined) {
          const weekLabel = WEEK_OF_MONTH_OPTIONS.find(w => w.value === rule.weekOfMonth)?.label;
          const dayLabel = DAYS_OF_WEEK[rule.dayOfWeek].label;
          return `${monthInterval} on the ${weekLabel} ${dayLabel}`;
        }
        return monthInterval;
      case 'yearly':
        const yearInterval = rule.interval === 1 ? 'Every year' : `Every ${rule.interval} years`;
        const monthLabel = rule.month ? MONTHS[rule.month - 1].label : '';
        return `${yearInterval} in ${monthLabel}`;
      default:
        return 'Custom recurrence';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-semibold">Recurrence Pattern</h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setShowPreview(!showPreview)}
          className="flex items-center gap-2"
        >
          <Eye className="h-4 w-4" />
          {showPreview ? 'Hide' : 'Show'} Preview
        </Button>
      </div>

      <Form {...form}>
        <div className="space-y-4">
          {/* Recurrence Type */}
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Repeat</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select recurrence type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Interval */}
          <FormField
            control={form.control}
            name="interval"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Every {recurrenceType === 'daily' ? 'day(s)' : 
                         recurrenceType === 'weekly' ? 'week(s)' :
                         recurrenceType === 'monthly' ? 'month(s)' : 'year(s)'}
                </FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    max="365"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                  />
                </FormControl>
                <FormDescription>
                  How often the period should repeat
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Weekly specific options */}
          {recurrenceType === 'weekly' && (
            <FormField
              control={form.control}
              name="daysOfWeek"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Days of the Week</FormLabel>
                  <FormDescription>
                    Select which days of the week to repeat on
                  </FormDescription>
                  <div className="grid grid-cols-7 gap-2">
                    {DAYS_OF_WEEK.map((day) => (
                      <div key={day.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`day-${day.value}`}
                          checked={field.value?.includes(day.value) || false}
                          onCheckedChange={(checked) => {
                            const currentDays = field.value || [];
                            if (checked) {
                              field.onChange([...currentDays, day.value].sort());
                            } else {
                              field.onChange(currentDays.filter(d => d !== day.value));
                            }
                          }}
                        />
                        <label
                          htmlFor={`day-${day.value}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {day.short}
                        </label>
                      </div>
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* Monthly specific options */}
          {recurrenceType === 'monthly' && (
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="dayOfMonth"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Day of Month (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="31"
                        placeholder="e.g., 15 for the 15th of each month"
                        value={field.value || ''}
                        onChange={(e) => {
                          const value = e.target.value ? parseInt(e.target.value) : undefined;
                          field.onChange(value);
                          // Clear relative day options when day of month is set
                          if (value) {
                            form.setValue('weekOfMonth', undefined);
                            form.setValue('dayOfWeek', undefined);
                          }
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Specific day of the month (1-31)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="text-center text-sm text-muted-foreground">
                — OR —
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="weekOfMonth"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Week of Month</FormLabel>
                      <Select 
                        onValueChange={(value) => {
                          field.onChange(parseInt(value));
                          // Clear day of month when relative day is set
                          if (value) {
                            form.setValue('dayOfMonth', undefined);
                          }
                        }} 
                        value={field.value?.toString() || ''}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select week" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {WEEK_OF_MONTH_OPTIONS.map((week) => (
                            <SelectItem key={week.value} value={week.value.toString()}>
                              {week.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dayOfWeek"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Day of Week</FormLabel>
                      <Select 
                        onValueChange={(value) => {
                          field.onChange(parseInt(value));
                          // Clear day of month when relative day is set
                          if (value) {
                            form.setValue('dayOfMonth', undefined);
                          }
                        }} 
                        value={field.value?.toString() || ''}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select day" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {DAYS_OF_WEEK.map((day) => (
                            <SelectItem key={day.value} value={day.value.toString()}>
                              {day.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}

          {/* Yearly specific options */}
          {recurrenceType === 'yearly' && (
            <FormField
              control={form.control}
              name="month"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Month</FormLabel>
                  <Select 
                    onValueChange={(value) => field.onChange(parseInt(value))} 
                    value={field.value?.toString() || ''}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select month" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {MONTHS.map((month) => (
                        <SelectItem key={month.value} value={month.value.toString()}>
                          {month.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <Separator />

          {/* End Conditions */}
          <div className="space-y-4">
            <h5 className="text-sm font-semibold">End Conditions</h5>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="count"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Number of Occurrences</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="1000"
                        placeholder="e.g., 10"
                        value={field.value || ''}
                        onChange={(e) => {
                          const value = e.target.value ? parseInt(e.target.value) : undefined;
                          field.onChange(value);
                          // Clear end date when count is set
                          if (value) {
                            form.setValue('endDate', undefined);
                          }
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Maximum number of periods to create
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        value={field.value ? formatDateTime(field.value) : ''}
                        onChange={(e) => {
                          const date = e.target.value ? parseDate(e.target.value) : undefined;
                          field.onChange(date);
                          // Clear count when end date is set
                          if (date) {
                            form.setValue('count', undefined);
                          }
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Stop creating periods after this date
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Recurrence Summary */}
          {form.formState.isValid && (
            <Alert className="border-primary/20 bg-primary/5">
              <Calendar className="h-4 w-4 text-primary" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="font-medium text-primary">Recurrence Pattern:</p>
                  <p className="text-primary/80">{getRecurrenceDescription(watchedRule)}</p>
                  {watchedRule.count && (
                    <p className="text-sm text-primary/70">
                      Will create {watchedRule.count} periods
                    </p>
                  )}
                  {watchedRule.endDate && (
                    <p className="text-sm text-primary/70">
                      Until {watchedRule.endDate.toLocaleDateString()}
                    </p>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Form Errors */}
          {Object.keys(form.formState.errors).length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please fix the errors above to configure recurrence.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </Form>

      {/* Period Preview */}
      {showPreview && form.formState.isValid && (
        <PeriodPreview
          basePeriod={{ start: startDate, end: endDate }}
          recurrenceRule={watchedRule}
          maxPreview={10}
        />
      )}
    </div>
  );
};

export default RecurrenceForm;