# Period Form Components

This directory contains React components for managing booking periods in the booking system refactor. These components support multiple time periods per booking, recurring patterns, and conflict detection.

## Components

### PeriodForm
**File:** `period-form.tsx`

Main component for managing multiple periods that apply to all selected resources in a booking.

**Features:**
- Add/remove multiple time periods
- Real-time conflict checking
- Visual indication that periods apply to all selected resources
- Integration with recurring period configuration
- Form validation with error display
- Period duplication functionality

**Props:**
- `bookingId?: number` - Optional booking ID for existing bookings
- `selectedResources: Resource[]` - Resources that periods will apply to
- `existingPeriods?: PeriodFormData[]` - Existing periods for editing
- `onPeriodsChange: (periods: PeriodFormData[]) => void` - Callback when periods change
- `onConflictCheck?: (periods: PeriodFormData[]) => Promise<ConflictCheckResult>` - Optional conflict checking
- `className?: string` - Additional CSS classes

### RecurrenceForm
**File:** `recurrence-form.tsx`

Component for configuring recurring period patterns (daily, weekly, monthly, yearly).

**Features:**
- Support for all recurrence types (daily, weekly, monthly, yearly)
- Flexible end conditions (count or end date)
- Weekly: day-of-week selection
- Monthly: by day of month or relative day (e.g., first Monday)
- Yearly: month selection
- Real-time pattern description
- Integration with period preview

**Props:**
- `recurrenceRule?: RecurrenceRule | null` - Current recurrence configuration
- `onRecurrenceChange: (rule: RecurrenceRule | null) => void` - Callback when rule changes
- `startDate: Date` - Base period start date
- `endDate: Date` - Base period end date
- `className?: string` - Additional CSS classes

### PeriodPreview
**File:** `period-preview.tsx`

Component for previewing generated recurring periods before creation.

**Features:**
- Visual preview of generated periods
- Summary statistics (total periods, duration, date range)
- Expandable list view
- Warning for large numbers of periods
- Duration calculations and formatting

**Props:**
- `basePeriod: { start: Date; end: Date }` - Base period for generation
- `recurrenceRule: RecurrenceRule` - Recurrence configuration
- `maxPreview?: number` - Maximum periods to preview (default: 10)
- `className?: string` - Additional CSS classes

### PeriodConflictDisplay
**File:** `period-conflict-display.tsx` (existing, enhanced)

Components for displaying period conflicts and resource utilization.

**Components:**
- `PeriodConflictDisplay` - Shows individual period conflicts
- `ConflictCheckDisplay` - Shows complete conflict check results
- `ConflictIndicator` - Compact conflict indicator badge
- `ResourceUtilizationDisplay` - Shows resource utilization metrics

## Usage Example

```tsx
import { PeriodForm } from '@/components/periods';

function BookingForm() {
  const [periods, setPeriods] = useState<PeriodFormData[]>([]);
  const [selectedResources, setSelectedResources] = useState<Resource[]>([]);

  const handleConflictCheck = async (periods: PeriodFormData[]) => {
    // Implement conflict checking logic
    return await checkPeriodConflicts(periods, selectedResources.map(r => r.id));
  };

  return (
    <PeriodForm
      selectedResources={selectedResources}
      existingPeriods={periods}
      onPeriodsChange={setPeriods}
      onConflictCheck={handleConflictCheck}
    />
  );
}
```

## Dependencies

These components depend on:
- `react-hook-form` for form management
- `@hookform/resolvers/zod` for validation
- `@/components/ui/*` for UI components
- `@/lib/types` for type definitions
- `@/lib/validations/period` for validation schemas
- `@/lib/services/recurrence-engine` for period generation

## Validation

All components use Zod schemas for validation:
- Period dates must be valid and end after start
- Minimum period duration of 15 minutes
- Recurrence rules validated based on type
- No overlapping periods within the same booking

## Accessibility

Components follow accessibility best practices:
- Proper ARIA labels and descriptions
- Keyboard navigation support
- Screen reader friendly
- Error messages associated with form fields

## Testing

Basic test structure is provided in `__tests__/period-form.test.tsx`. Tests should cover:
- Component rendering
- Form validation
- Period management (add/remove/edit)
- Recurrence configuration
- Conflict display
- User interactions