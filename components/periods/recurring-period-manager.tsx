'use client';

import React, { useState } from 'react';
import { Calendar, Clock, Edit3, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>cw, <PERSON> } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

import { Period, RecurrenceRule } from '@/lib/types';
import { formatPeriodDuration } from '@/lib/types';

interface RecurringPeriodManagerProps {
  periods: Period[];
  onEditPeriod?: (periodId: number, editType: 'single' | 'series' | 'future') => void;
  onDeletePeriod?: (periodId: number, deleteType: 'single' | 'series' | 'future') => void;
  className?: string;
}

interface RecurringSeriesGroup {
  parentPeriodId: number;
  parentPeriod: Period;
  childPeriods: Period[];
  recurrenceRule: RecurrenceRule;
}

type ActionType = 'edit' | 'delete';
type EditDeleteType = 'single' | 'series' | 'future';

export const RecurringPeriodManager: React.FC<RecurringPeriodManagerProps> = ({
  periods,
  onEditPeriod,
  onDeletePeriod,
  className = ''
}) => {
  const [actionDialog, setActionDialog] = useState<{
    isOpen: boolean;
    periodId: number;
    actionType: ActionType;
    selectedType: EditDeleteType;
    seriesInfo?: {
      totalPeriods: number;
      futurePeriods: number;
      recurrenceDescription: string;
    };
  }>({
    isOpen: false,
    periodId: 0,
    actionType: 'edit',
    selectedType: 'single'
  });

  // Group periods by recurring series
  const recurringGroups = React.useMemo(() => {
    const groups: RecurringSeriesGroup[] = [];
    const processedPeriods = new Set<number>();

    periods.forEach(period => {
      if (processedPeriods.has(period.id)) return;

      if (period.isRecurring && period.recurrenceRule && !period.parentPeriodId) {
        // This is a parent period of a recurring series
        const childPeriods = periods.filter(p => p.parentPeriodId === period.id);
        
        groups.push({
          parentPeriodId: period.id,
          parentPeriod: period,
          childPeriods,
          recurrenceRule: period.recurrenceRule
        });

        // Mark all periods in this series as processed
        processedPeriods.add(period.id);
        childPeriods.forEach(child => processedPeriods.add(child.id));
      }
    });

    return groups;
  }, [periods]);

  // Get standalone (non-recurring) periods
  const standalonePeriods = React.useMemo(() => {
    const recurringPeriodIds = new Set<number>();
    recurringGroups.forEach(group => {
      recurringPeriodIds.add(group.parentPeriodId);
      group.childPeriods.forEach(child => recurringPeriodIds.add(child.id));
    });

    return periods.filter(period => !recurringPeriodIds.has(period.id));
  }, [periods, recurringGroups]);

  const getRecurrenceDescription = (rule: RecurrenceRule): string => {
    switch (rule.type) {
      case 'daily':
        return rule.interval === 1 ? 'Daily' : `Every ${rule.interval} days`;
      case 'weekly':
        const days = rule.daysOfWeek?.length || 0;
        const weekInterval = rule.interval === 1 ? 'Weekly' : `Every ${rule.interval} weeks`;
        return `${weekInterval} (${days} day${days !== 1 ? 's' : ''})`;
      case 'monthly':
        const monthInterval = rule.interval === 1 ? 'Monthly' : `Every ${rule.interval} months`;
        return monthInterval;
      case 'yearly':
        const yearInterval = rule.interval === 1 ? 'Yearly' : `Every ${rule.interval} years`;
        return yearInterval;
      default:
        return 'Custom recurrence';
    }
  };

  const handleActionClick = (periodId: number, actionType: ActionType) => {
    const period = periods.find(p => p.id === periodId);
    if (!period) return;

    let seriesInfo;
    if (period.isRecurring) {
      const isParent = !period.parentPeriodId;
      const parentId = isParent ? period.id : period.parentPeriodId;
      const allSeriesPeriods = periods.filter(p => 
        p.id === parentId || p.parentPeriodId === parentId
      );
      
      const currentPeriodIndex = allSeriesPeriods.findIndex(p => p.id === periodId);
      const futurePeriods = allSeriesPeriods.length - currentPeriodIndex - 1;

      seriesInfo = {
        totalPeriods: allSeriesPeriods.length,
        futurePeriods,
        recurrenceDescription: getRecurrenceDescription(period.recurrenceRule!)
      };
    }

    setActionDialog({
      isOpen: true,
      periodId,
      actionType,
      selectedType: 'single',
      seriesInfo
    });
  };

  const handleActionConfirm = () => {
    const { periodId, actionType, selectedType } = actionDialog;
    
    if (actionType === 'edit' && onEditPeriod) {
      onEditPeriod(periodId, selectedType);
    } else if (actionType === 'delete' && onDeletePeriod) {
      onDeletePeriod(periodId, selectedType);
    }

    setActionDialog(prev => ({ ...prev, isOpen: false }));
  };

  const formatPeriodTime = (period: Period): string => {
    const sameDay = period.start.toDateString() === period.end.toDateString();
    
    if (sameDay) {
      return `${period.start.toLocaleDateString()} ${period.start.toLocaleTimeString()} - ${period.end.toLocaleTimeString()}`;
    } else {
      return `${period.start.toLocaleDateString()} ${period.start.toLocaleTimeString()} - ${period.end.toLocaleDateString()} ${period.end.toLocaleTimeString()}`;
    }
  };

  if (periods.length === 0) {
    return (
      <Alert className={className}>
        <Calendar className="h-4 w-4" />
        <AlertDescription>
          No periods have been created yet. Add periods using the form above.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Recurring Series */}
      {recurringGroups.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <RotateCcw className="h-5 w-5" />
            Recurring Series ({recurringGroups.length})
          </h3>
          
          {recurringGroups.map((group) => (
            <Card key={group.parentPeriodId} className="border-primary/20 bg-primary/5">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-base">
                      Recurring Series
                    </CardTitle>
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Link className="h-3 w-3" />
                      {group.childPeriods.length + 1} periods
                    </Badge>
                  </div>
                  <Badge variant="secondary">
                    {getRecurrenceDescription(group.recurrenceRule)}
                  </Badge>
                </div>
                <CardDescription>
                  Pattern: {getRecurrenceDescription(group.recurrenceRule)}
                  {group.recurrenceRule.endDate && (
                    <span> • Until {group.recurrenceRule.endDate.toLocaleDateString()}</span>
                  )}
                  {group.recurrenceRule.count && (
                    <span> • {group.recurrenceRule.count} occurrences</span>
                  )}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-3">
                <ScrollArea className="h-48">
                  <div className="space-y-2 pr-4">
                    {/* Parent Period */}
                    <div className="flex items-center justify-between p-3 border rounded-lg bg-background">
                      <div className="flex items-center gap-3">
                        <Badge variant="default" className="w-8 h-8 rounded-full flex items-center justify-center p-0">
                          1
                        </Badge>
                        <div>
                          <div className="font-medium text-sm">
                            {formatPeriodTime(group.parentPeriod)}
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatPeriodDuration(group.parentPeriod.start, group.parentPeriod.end)}
                            <Badge variant="outline" className="ml-2 text-xs">
                              Series Origin
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        {onEditPeriod && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleActionClick(group.parentPeriod.id, 'edit')}
                            className="h-8 w-8 p-0"
                            title="Edit period"
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>
                        )}
                        {onDeletePeriod && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleActionClick(group.parentPeriod.id, 'delete')}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                            title="Delete period"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Child Periods */}
                    {group.childPeriods.map((period, index) => (
                      <div key={period.id} className="flex items-center justify-between p-3 border rounded-lg bg-background">
                        <div className="flex items-center gap-3">
                          <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center p-0">
                            {index + 2}
                          </Badge>
                          <div>
                            <div className="font-medium text-sm">
                              {formatPeriodTime(period)}
                            </div>
                            <div className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatPeriodDuration(period.start, period.end)}
                              <Badge variant="outline" className="ml-2 text-xs">
                                Recurring
                              </Badge>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          {onEditPeriod && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleActionClick(period.id, 'edit')}
                              className="h-8 w-8 p-0"
                              title="Edit period"
                            >
                              <Edit3 className="h-4 w-4" />
                            </Button>
                          )}
                          {onDeletePeriod && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleActionClick(period.id, 'delete')}
                              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                              title="Delete period"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Standalone Periods */}
      {standalonePeriods.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Individual Periods ({standalonePeriods.length})
          </h3>
          
          <div className="space-y-2">
            {standalonePeriods.map((period, index) => (
              <div key={period.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-3">
                  <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center p-0">
                    {index + 1}
                  </Badge>
                  <div>
                    <div className="font-medium text-sm">
                      {formatPeriodTime(period)}
                    </div>
                    <div className="text-xs text-muted-foreground flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatPeriodDuration(period.start, period.end)}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-1">
                  {onEditPeriod && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleActionClick(period.id, 'edit')}
                      className="h-8 w-8 p-0"
                      title="Edit period"
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                  )}
                  {onDeletePeriod && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleActionClick(period.id, 'delete')}
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      title="Delete period"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action Dialog */}
      <Dialog open={actionDialog.isOpen} onOpenChange={(open) => setActionDialog(prev => ({ ...prev, isOpen: open }))}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {actionDialog.actionType === 'edit' ? (
                <>
                  <Edit3 className="h-5 w-5" />
                  Edit Recurring Period
                </>
              ) : (
                <>
                  <Trash2 className="h-5 w-5" />
                  Delete Recurring Period
                </>
              )}
            </DialogTitle>
            <DialogDescription>
              This period is part of a recurring series. Choose how you want to {actionDialog.actionType} it:
            </DialogDescription>
          </DialogHeader>

          {actionDialog.seriesInfo && (
            <div className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">Series Information:</p>
                    <p className="text-sm">
                      Pattern: {actionDialog.seriesInfo.recurrenceDescription}
                    </p>
                    <p className="text-sm">
                      Total periods: {actionDialog.seriesInfo.totalPeriods}
                    </p>
                    <p className="text-sm">
                      Future periods: {actionDialog.seriesInfo.futurePeriods}
                    </p>
                  </div>
                </AlertDescription>
              </Alert>

              <RadioGroup
                value={actionDialog.selectedType}
                onValueChange={(value) => setActionDialog(prev => ({ ...prev, selectedType: value as EditDeleteType }))}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="single" id="single" />
                  <Label htmlFor="single" className="flex-1">
                    <div className="font-medium">This period only</div>
                    <div className="text-sm text-muted-foreground">
                      {actionDialog.actionType === 'edit' ? 'Edit' : 'Delete'} only this specific occurrence
                    </div>
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="future" id="future" />
                  <Label htmlFor="future" className="flex-1">
                    <div className="font-medium">This and future periods</div>
                    <div className="text-sm text-muted-foreground">
                      {actionDialog.actionType === 'edit' ? 'Edit' : 'Delete'} this period and all {actionDialog.seriesInfo.futurePeriods} future occurrences
                    </div>
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="series" id="series" />
                  <Label htmlFor="series" className="flex-1">
                    <div className="font-medium">Entire series</div>
                    <div className="text-sm text-muted-foreground">
                      {actionDialog.actionType === 'edit' ? 'Edit' : 'Delete'} all {actionDialog.seriesInfo.totalPeriods} periods in this recurring series
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setActionDialog(prev => ({ ...prev, isOpen: false }))}
            >
              Cancel
            </Button>
            <Button
              variant={actionDialog.actionType === 'delete' ? 'destructive' : 'default'}
              onClick={handleActionConfirm}
            >
              {actionDialog.actionType === 'edit' ? 'Edit' : 'Delete'} {actionDialog.selectedType === 'single' ? 'Period' : actionDialog.selectedType === 'future' ? 'Future Periods' : 'Series'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RecurringPeriodManager;