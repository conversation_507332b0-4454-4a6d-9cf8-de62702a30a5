'use client';

import React from 'react';
import { RecurringPeriodManager } from './recurring-period-manager';
import { Period, RecurrenceRule } from '@/lib/types';

// Demo component to showcase the recurring period management functionality
export const DemoRecurringPeriods: React.FC = () => {
  const mockRecurrenceRule: RecurrenceRule = {
    type: 'weekly',
    interval: 1,
    daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
    count: 5
  };

  const mockParentPeriod: Period = {
    id: 1,
    bookingId: 1,
    start: new Date('2024-01-01T09:00:00'),
    end: new Date('2024-01-01T10:00:00'),
    isRecurring: true,
    recurrenceRule: mockRecurrenceRule,
    parentPeriodId: null,
    booking: {} as any,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockChildPeriods: Period[] = [
    {
      id: 2,
      bookingId: 1,
      start: new Date('2024-01-03T09:00:00'),
      end: new Date('2024-01-03T10:00:00'),
      isRecurring: true,
      recurrenceRule: mockRecurrenceRule,
      parentPeriodId: 1,
      booking: {} as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 3,
      bookingId: 1,
      start: new Date('2024-01-05T09:00:00'),
      end: new Date('2024-01-05T10:00:00'),
      isRecurring: true,
      recurrenceRule: mockRecurrenceRule,
      parentPeriodId: 1,
      booking: {} as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 4,
      bookingId: 1,
      start: new Date('2024-01-08T09:00:00'),
      end: new Date('2024-01-08T10:00:00'),
      isRecurring: true,
      recurrenceRule: mockRecurrenceRule,
      parentPeriodId: 1,
      booking: {} as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  const mockStandalonePeriod: Period = {
    id: 5,
    bookingId: 1,
    start: new Date('2024-01-10T14:00:00'),
    end: new Date('2024-01-10T15:00:00'),
    isRecurring: false,
    parentPeriodId: null,
    booking: {} as any,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const periods = [mockParentPeriod, ...mockChildPeriods, mockStandalonePeriod];

  const handleEditPeriod = (periodId: number, editType: 'single' | 'series' | 'future') => {
    console.log(`Edit period ${periodId} with type: ${editType}`);
    alert(`Would edit period ${periodId} with type: ${editType}`);
  };

  const handleDeletePeriod = (periodId: number, deleteType: 'single' | 'series' | 'future') => {
    console.log(`Delete period ${periodId} with type: ${deleteType}`);
    alert(`Would delete period ${periodId} with type: ${deleteType}`);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Recurring Period Management Demo</h1>
      
      <div className="space-y-4 mb-6">
        <p className="text-muted-foreground">
          This demo shows the recurring period management UI with:
        </p>
        <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
          <li>1 recurring series with 4 periods (weekly pattern on Mon/Wed/Fri)</li>
          <li>1 standalone period</li>
          <li>Edit and delete options with single/series/future choices</li>
          <li>Clear visual distinction between recurring and standalone periods</li>
        </ul>
      </div>

      <RecurringPeriodManager
        periods={periods}
        onEditPeriod={handleEditPeriod}
        onDeletePeriod={handleDeletePeriod}
      />
    </div>
  );
};

export default DemoRecurringPeriods;