'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Plus, Trash2, Clock, Calendar, AlertTriangle, Copy } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';

import { PeriodFormData, Resource, ConflictCheckResult, Period } from '@/lib/types';
import { periodFormDataSchema, periodUpdateSchema } from '@/lib/validations/period';
import { formatPeriodDuration } from '@/lib/types';
import { RecurrenceForm } from './recurrence-form';
import { ConflictCheckDisplay } from './period-conflict-display';
import { RecurringPeriodManager } from './recurring-period-manager';

// Form schema for multiple periods (supports create/update modes)
const buildPeriodsFormSchema = (mode: 'create' | 'update') =>
  z.object({
    periods: z
      .array(mode === 'update' ? periodUpdateSchema : periodFormDataSchema)
      .min(1, 'At least one period is required'),
  });

type PeriodsFormData = { periods: PeriodFormData[] };

interface PeriodFormProps {
  bookingId?: number;
  selectedResources: Resource[];
  existingPeriods?: PeriodFormData[];
  existingPeriodsWithIds?: Period[]; // For managing existing recurring periods
  onPeriodsChange: (periods: PeriodFormData[]) => void;
  onConflictCheck?: (periods: PeriodFormData[]) => Promise<ConflictCheckResult>;
  onRecurringPeriodEdit?: (periodId: number, editType: 'single' | 'series' | 'future') => void;
  onRecurringPeriodDelete?: (periodId: number, deleteType: 'single' | 'series' | 'future') => void;
  className?: string;
  mode?: 'create' | 'update';
}

export const PeriodForm: React.FC<PeriodFormProps> = ({
  bookingId,
  selectedResources,
  existingPeriods = [],
  existingPeriodsWithIds = [],
  onPeriodsChange,
  onConflictCheck,
  onRecurringPeriodEdit,
  onRecurringPeriodDelete,
  className = '',
  mode = 'create'
}) => {
  const [conflicts, setConflicts] = useState<ConflictCheckResult | null>(null);
  const [isCheckingConflicts, setIsCheckingConflicts] = useState(false);

  const schema = React.useMemo(() => buildPeriodsFormSchema(mode), [mode]);

  const form = useForm<PeriodsFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      periods: existingPeriods.length > 0 ? existingPeriods : [{
        // Start 1 hour from now by default to satisfy "future" validation
        start: new Date(Date.now() + 60 * 60 * 1000),
        end: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
        isRecurring: false
      }]
    }
  });

  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: 'periods'
  });

  const watchedPeriods = form.watch('periods');

  // Initialize periods only when editing a specific booking (avoid resetting during user edits)
  const initializedBookingRef = React.useRef<number | null>(null);
  useEffect(() => {
    if (bookingId && initializedBookingRef.current !== bookingId) {
      if (existingPeriods && existingPeriods.length > 0) {
        form.reset({ periods: existingPeriods });
      }
      initializedBookingRef.current = bookingId;
    }
  }, [bookingId, existingPeriods, form]);

  // Notify parent of changes with guard to avoid loops (send full list, not only valid)
  useEffect(() => {
    onPeriodsChange(watchedPeriods || []);
  }, [JSON.stringify(watchedPeriods), onPeriodsChange]);

  // Check for conflicts when periods change
  const checkConflicts = useCallback(async () => {
    // If no resources selected, we can't meaningfully check external conflicts,
    // but we can still surface internal overlaps between entered periods.
    const validPeriods = watchedPeriods.filter((period: PeriodFormData) => 
      period.start && period.end && period.end > period.start
    );

    if (validPeriods.length === 0) {
      setConflicts({ hasConflicts: false, conflicts: [], affectedResources: [], message: 'Select a valid period to check' });
      return;
    }

    // Client-side internal overlap detection (same booking)
    const internalOverlaps: { i: number; j: number }[] = [];
    for (let i = 0; i < validPeriods.length; i++) {
      for (let j = i + 1; j < validPeriods.length; j++) {
        const a = validPeriods[i];
        const b = validPeriods[j];
        if (a.start < b.end && b.start < a.end) {
          internalOverlaps.push({ i, j });
        }
      }
    }

    if (internalOverlaps.length > 0) {
      const pairs = internalOverlaps.map(p => `(${p.i + 1} ↔ ${p.j + 1})`).join(', ');
      setConflicts({
        hasConflicts: true,
        conflicts: [], // UI summary is sufficient; these are internal to the booking
        affectedResources: [],
        message: `Periods within this booking overlap: ${pairs}. Please adjust times so they do not overlap.`
      });
      return; // Do not call server if the form itself is invalid due to overlaps
    }

    // If we have resources and a conflict check handler, check external conflicts on server
    if (!onConflictCheck || selectedResources.length === 0) {
      setConflicts({ hasConflicts: false, conflicts: [], affectedResources: [], message: 'No resources selected' });
      return;
    }

    setIsCheckingConflicts(true);
    try {
      const result = await onConflictCheck(validPeriods);
      // Ensure result has required fields
      setConflicts({
        hasConflicts: !!result?.hasConflicts,
        conflicts: result?.conflicts || [],
        affectedResources: result?.affectedResources || [],
        message: result?.message || (result?.hasConflicts ? 'Conflicts detected' : 'No conflicts detected')
      });
    } catch (error: any) {
      console.error('Error checking conflicts:', error);
      setConflicts({
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: error?.message ? `Unable to check for conflicts: ${error.message}` : 'Unable to check for conflicts'
      });
    } finally {
      setIsCheckingConflicts(false);
    }
  }, [watchedPeriods, onConflictCheck, selectedResources]);

  // Debounced conflict checking
  useEffect(() => {
    const timer = setTimeout(checkConflicts, 500);
    return () => clearTimeout(timer);
  }, [checkConflicts]);

  const addPeriod = () => {
    const lastPeriod = watchedPeriods[watchedPeriods.length - 1];
    const newStart = lastPeriod ? new Date(lastPeriod.end.getTime() + 30 * 60 * 1000) : new Date();
    const newEnd = new Date(newStart.getTime() + 60 * 60 * 1000);

    append({
      start: newStart,
      end: newEnd,
      isRecurring: false
    });
  };

  const duplicatePeriod = (index: number) => {
    const period = watchedPeriods[index];
    const duration = period.end.getTime() - period.start.getTime();
    const newStart = new Date(period.end.getTime() + 30 * 60 * 1000);
    const newEnd = new Date(newStart.getTime() + duration);

    append({
      start: newStart,
      end: newEnd,
      isRecurring: period.isRecurring,
      recurrenceRule: period.recurrenceRule
    });
  };

  const formatDateTime = (date: Date): string => {
    // Format date in local timezone for datetime-local input
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  const parseDateTime = (dateString: string): Date => {
    // Parse datetime-local input value as local time and ensure seconds
    if (!dateString) return new Date(NaN);
    const normalized = dateString.length === 16 ? `${dateString}:00` : dateString;
    // Treat as local time (no timezone suffix)
    const [datePart, timePart] = normalized.split('T');
    const [year, month, day] = datePart.split('-').map(Number);
    const [hours, minutes, seconds] = timePart.split(':').map(Number);
    const d = new Date();
    d.setFullYear(year, month - 1, day);
    d.setHours(hours, minutes, seconds || 0, 0);
    return d;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Resource Information */}
      {selectedResources.length > 0 && (
        <Alert>
          <Calendar className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">
                All periods will apply to the following {selectedResources.length} resource{selectedResources.length !== 1 ? 's' : ''}:
              </p>
              <div className="flex flex-wrap gap-1">
                {selectedResources.map((resource) => (
                  <Badge key={resource.id} variant="outline">
                    {resource.name}
                  </Badge>
                ))}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <div className="space-y-4">
          {/* Periods List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Time Periods</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addPeriod}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Period
              </Button>
            </div>

            {fields.map((field, index) => (
              <Card key={field.id} className="relative">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">
                        Period {index + 1}
                      </CardTitle>
                      {watchedPeriods[index]?.isRecurring && (
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          Recurring
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => duplicatePeriod(index)}
                        className="h-8 w-8 p-0"
                        title="Duplicate period"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => remove(index)}
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          title="Remove period"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                  {watchedPeriods[index]?.start && watchedPeriods[index]?.end && (
                    <CardDescription className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Duration: {formatPeriodDuration(watchedPeriods[index].start, watchedPeriods[index].end)}
                      {watchedPeriods[index]?.isRecurring && watchedPeriods[index]?.recurrenceRule && (
                        <Badge variant="secondary" className="ml-2">
                          {watchedPeriods[index].recurrenceRule?.type} pattern
                        </Badge>
                      )}
                    </CardDescription>
                  )}
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Start and End Time */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name={`periods.${index}.start`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Start Time</FormLabel>
                          <FormControl>
                            <Input
                              type="datetime-local"
                              value={field.value ? formatDateTime(field.value) : ''}
                              onChange={(e) => {
                                const date = parseDateTime(e.currentTarget.value);
                                field.onChange(date);
                              }}
                              min={mode === 'update' ? undefined : formatDateTime(new Date())}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`periods.${index}.end`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>End Time</FormLabel>
                          <FormControl>
                            <Input
                              type="datetime-local"
                              value={field.value ? formatDateTime(field.value) : ''}
                              onChange={(e) => {
                                const date = parseDateTime(e.currentTarget.value);
                                field.onChange(date);
                              }}
                              min={watchedPeriods[index]?.start ? formatDateTime(watchedPeriods[index].start) : undefined}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Recurring Period Toggle */}
                  <FormField
                    control={form.control}
                    name={`periods.${index}.isRecurring`}
                    render={({ field }) => (
                      <FormItem className={`flex flex-row items-center justify-between rounded-lg border p-3 ${field.value ? 'border-primary/20 bg-primary/5' : ''}`}>
                        <div className="space-y-0.5">
                          <FormLabel className={`text-base ${field.value ? 'text-primary' : ''}`}>
                            Recurring Period
                          </FormLabel>
                          <FormDescription className={field.value ? 'text-primary/70' : ''}>
                            Create multiple periods based on a recurring pattern
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {/* Recurrence Configuration */}
                  {watchedPeriods[index]?.isRecurring && (
                    <div className="border-t pt-4">
                      <RecurrenceForm
                        recurrenceRule={watchedPeriods[index].recurrenceRule}
                        onRecurrenceChange={(rule) => {
                          const currentPeriod = watchedPeriods[index];
                          update(index, {
                            ...currentPeriod,
                            recurrenceRule: rule || undefined
                          });
                        }}
                        startDate={watchedPeriods[index].start}
                        endDate={watchedPeriods[index].end}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Form Errors */}
          {form.formState.errors.periods && form.formState.errors.periods.message && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {form.formState.errors.periods.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Conflict Display */}
          {isCheckingConflicts && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Checking for scheduling conflicts...
              </AlertDescription>
            </Alert>
          )}

          {conflicts && (
            <ConflictCheckDisplay
              conflictResult={conflicts}
              onResolveConflict={(conflictId) => {
                // Handle conflict resolution - could navigate to conflicting booking
                console.log('Resolve conflict:', conflictId);
              }}
            />
          )}
        </div>
      </Form>

      {/* Existing Recurring Periods Management */}
      {existingPeriodsWithIds && existingPeriodsWithIds.length > 0 && (
        <div className="space-y-4">
          <Separator />
          <div>
            <h3 className="text-lg font-semibold mb-4">Existing Periods</h3>
            <RecurringPeriodManager
              periods={existingPeriodsWithIds}
              onEditPeriod={onRecurringPeriodEdit}
              onDeletePeriod={onRecurringPeriodDelete}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default PeriodForm;