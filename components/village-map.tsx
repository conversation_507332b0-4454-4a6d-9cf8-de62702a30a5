
'use client'
import React, { useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { motion } from 'framer-motion'
import { useLanguage } from './language-provider'
import { MapPin, Users, GraduationCap, ShoppingBag, Sprout, Scale, Hammer, Compass, Building, Network, Home, Wallet, Coffee } from 'lucide-react'
import Image from 'next/image'
import type { DistrictType } from '@/app/page'
import { debounce } from '../lib/utils'

interface VillageMapProps {
  onDistrictSelect: (district: DistrictType) => void
  discoveredDistricts: Set<DistrictType>
}

const districtData = {
  square: { icon: Users, position: { top: '45%', left: '50%' }, color: 'bg-blue-500' },
  school: { icon: GraduationCap, position: { top: '25%', left: '65%' }, color: 'bg-green-500' },
  market: { icon: ShoppingBag, position: { top: '35%', left: '25%' }, color: 'bg-purple-500' },
  field: { icon: Sprout, position: { top: '70%', left: '40%' }, color: 'bg-emerald-500' },
  council: { icon: Scale, position: { top: '20%', left: '35%' }, color: 'bg-amber-500' },
  workshop: { icon: Hammer, position: { top: '55%', left: '75%' }, color: 'bg-orange-500' },
  guide: { icon: Compass, position: { top: '30%', left: '80%' }, color: 'bg-cyan-500' },
  bureau: { icon: Building, position: { top: '60%', left: '20%' }, color: 'bg-indigo-500' },
  gateway: { icon: Network, position: { top: '75%', left: '70%' }, color: 'bg-rose-500' },
  portfolio: { icon: Wallet, position: { top: '40%', left: '70%' }, color: 'bg-violet-500' },
  spot: { icon: Coffee, position: { top: '65%', left: '55%' }, color: 'bg-yellow-500' },
  business: { icon: Home, position: { top: '50%', left: '30%' }, color: 'bg-teal-500' }
}

function VillageMap({ onDistrictSelect, discoveredDistricts }: VillageMapProps) {
  const { t } = useLanguage();
  const [hoveredDistrict, setHoveredDistrict] = useState<DistrictType | null>(null);
  const [tooltipPos, setTooltipPos] = useState<{ x: number; y: number } | null>(null);
  const markerRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const debouncedSetHoveredDistrict = React.useMemo(
    () => debounce((district: DistrictType | null) => {
      setHoveredDistrict(district);
      if (district && markerRefs.current[district]) {
        const rect = markerRefs.current[district]!.getBoundingClientRect();
        setTooltipPos({ x: rect.left + rect.width / 2, y: rect.top });
      } else {
        setTooltipPos(null);
      }
    }, 40),
    []
  );

  const getDistrictName = (district: DistrictType) => {
    const nameMap = {
      square: 'district_1_name',
      school: 'district_2_name', 
      market: 'district_3_name',
      field: 'district_4_name',
      council: 'district_5_name',
      workshop: 'district_6_name',
      guide: 'district_7_name',
      bureau: 'district_8_name',
      gateway: 'district_9_name',
      portfolio: 'district_10_name',
      spot: 'district_11_name',
      business: 'district_12_name'
    }
    return t(nameMap[district])
  }

  const getDistrictDesc = (district: DistrictType) => {
    const descMap = {
      square: 'district_1_desc',
      school: 'district_2_desc', 
      market: 'district_3_desc',
      field: 'district_4_desc',
      council: 'district_5_desc',
      workshop: 'district_6_desc',
      guide: 'district_7_desc',
      bureau: 'district_8_desc',
      gateway: 'district_9_desc',
      portfolio: 'district_10_desc',
      spot: 'district_11_desc',
      business: 'district_12_desc'
    }
    return t(descMap[district])
  }

  return (
    <section className="min-h-screen bg-gradient-to-b from-amber-50 to-orange-100 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          // @ts-ignore
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-4">
            {t('map_title')}
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            {t('map_subtitle')}
          </p>
          <p className="text-gray-500">
            {t('map_instruction')}
          </p>
        </motion.div>

        {/* Village Map Container */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          // @ts-ignore
          className="relative w-full max-w-6xl mx-auto bg-white rounded-3xl shadow-2xl overflow-hidden"
        >
          {/* Background Map */}
          <div className="relative aspect-video">
            <Image
              src="https://cdn.abacus.ai/images/5a2e0d43-fa87-48a5-b10b-34fdc572360d.png"
              alt="Netaj Village Map"
              fill
              className="object-cover"
            />
            
            {/* Overlay for better contrast */}
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/10" />

            {/* District Markers */}
            {Object.entries(districtData).map(([district, data]) => {
              const districtKey = district as DistrictType;
              const IconComponent = data.icon;
              const isDiscovered = discoveredDistricts.has(districtKey);
              const isHovered = hoveredDistrict === districtKey;

              return (
                <motion.div
                  key={district}
                  // @ts-ignore
                  ref={el => (markerRefs.current[districtKey] = el)}
                  className="absolute -translate-x-1/2 -translate-y-1/2 cursor-pointer transform-gpu overflow-visible"
                  style={data.position}
                  onMouseEnter={() => debouncedSetHoveredDistrict(districtKey)}
                  onMouseLeave={() => debouncedSetHoveredDistrict(null)}
                  onClick={() => onDistrictSelect(districtKey)}
                >
                  {/* Pulse Animation for Undiscovered Districts */}
                  {!isDiscovered && (
                    <motion.div
                      // @ts-ignore
                      className={`absolute inset-0 rounded-full ${data.color} opacity-30`}
                      animate={{ scale: [1, 1.5, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  )}

                  {/* District Icon */}
                  <motion.div
                    // @ts-ignore
                    className={`
                      relative w-12 h-12 md:w-16 md:h-16 ${data.color} rounded-full flex items-center justify-center
                      shadow-lg border-4 border-white transition-all duration-300
                      ${isDiscovered ? 'opacity-100' : 'opacity-80'}
                      ${isHovered ? 'shadow-2xl' : ''}
                    `}
                    whileHover={{ scale: 1.2, zIndex: 50 }}
                  >
                    <IconComponent className="w-6 h-6 md:w-8 md:h-8 text-white" />
                    {isDiscovered && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white" />
                    )}
                  </motion.div>
                </motion.div>
              );
            })}

            {/* Tooltip Portal */}
            {hoveredDistrict && tooltipPos && ReactDOM.createPortal(
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                // @ts-ignore
                className="fixed z-[1000] pointer-events-none transform-gpu"
                style={{
                  left: tooltipPos.x - 100,
                  top: tooltipPos.y - 100, // 16px above the marker
                  transform: 'translate(-50%, -100%)',
                  willChange: 'transform, opacity',
                }}
              >
                <div className="bg-white rounded-lg shadow-xl p-4 min-w-48 border border-gray-200">
                  <h3 className="font-bold text-gray-900 mb-1">
                    {getDistrictName(hoveredDistrict)}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {getDistrictDesc(hoveredDistrict)}
                  </p>
                  <div className="mt-2 flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {discoveredDistricts.has(hoveredDistrict) ? 'Discovered' : 'Undiscovered'}
                    </span>
                    <span className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded">
                      Click to explore
                    </span>
                  </div>
                </div>
                {/* Arrow */}
                <div className="absolute left-1/2 top-full transform -translate-x-1/2 w-4 h-4 bg-white border-l border-t border-gray-200 rotate-45"></div>
              </motion.div>,
              document.body
            )}

            {/* Pathways (simple lines connecting districts) */}
            <svg className="absolute inset-0 w-full h-full pointer-events-none">
              <defs>
                <pattern id="pathway" patternUnits="userSpaceOnUse" width="8" height="8">
                  <circle cx="4" cy="4" r="1" fill="#d97706" opacity="0.3" />
                </pattern>
              </defs>
              
              {/* Central hub connections */}
              <g stroke="url(#pathway)" strokeWidth="3" fill="none" opacity="0.6">
                <path d="M 50% 45% Q 65% 25% 65% 25%" />
                <path d="M 50% 45% Q 25% 35% 25% 35%" />
                <path d="M 50% 45% Q 40% 70% 40% 70%" />
                <path d="M 50% 45% Q 35% 20% 35% 20%" />
                <path d="M 50% 45% Q 75% 55% 75% 55%" />
                <path d="M 50% 45% Q 80% 30% 80% 30%" />
                <path d="M 50% 45% Q 20% 60% 20% 60%" />
                <path d="M 50% 45% Q 70% 75% 70% 75%" />
                <path d="M 50% 45% Q 30% 50% 30% 50%" />
              </g>
            </svg>
          </div>

          {/* Progress Bar */}
          <div className="absolute bottom-4 left-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                {t('exploration_progress')}
              </span>
              <span className="text-sm font-bold text-amber-600">
                {discoveredDistricts.size}/11
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <motion.div 
                // @ts-ignore
                className="bg-gradient-to-r from-amber-500 to-orange-500 h-3 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(discoveredDistricts.size / 11) * 100}%` }}
                transition={{ duration: 0.8 }}
              />
            </div>
          </div>
        </motion.div>

        {/* District Grid (for mobile/alternative view) */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          // @ts-ignore
          className="mt-12 md:hidden grid grid-cols-3 gap-4"
        >
          {Object.entries(districtData).map(([district, data]) => {
            const districtKey = district as DistrictType
            const IconComponent = data.icon
            const isDiscovered = discoveredDistricts.has(districtKey)

            return (
              <motion.button
                key={district}
                // @ts-ignore
                className={`
                  p-4 rounded-xl border-2 transition-all duration-300 transform-gpu
                  ${isDiscovered 
                    ? 'bg-white border-amber-300 shadow-md' 
                    : 'bg-gray-50 border-gray-200'
                  }
                `}
                whileTap={{ scale: 0.95 }}
                onClick={() => onDistrictSelect(districtKey)}
                style={{ willChange: 'transform, opacity' }}
              >
                <div className={`w-8 h-8 ${data.color} rounded-full flex items-center justify-center mx-auto mb-2`}>
                  <IconComponent className="w-4 h-4 text-white" />
                </div>
                <div className="text-xs font-medium text-gray-700 text-center">
                  {getDistrictName(districtKey)}
                </div>
                {isDiscovered && (
                  <div className="text-xs text-green-600 text-center mt-1">✓</div>
                )}
              </motion.button>
            )
          })}
        </motion.div>
      </div>
    </section>
  )
}

export const MemoizedVillageMap = React.memo(VillageMap);
export { VillageMap };
