
'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { useLanguage } from './language-provider'
import { ServiceModal } from './service-modal'
import Image from 'next/image'
import { 
  Users, 
  GraduationCap, 
  ShoppingBag, 
  Sprout, 
  Shield, 
  Hammer, 
  Compass, 
  Building, 
  Waypoints 
} from 'lucide-react'

const serviceIcons = [
  Users,      // Grand Square - Co-working
  GraduationCap, // Village School - Training
  ShoppingBag,   // Market - Business Development
  Sprout,        // Field - VC Studio
  Shield,        // Council - Consulting
  Hammer,        // Carpenters - Team Building
  Compass,       // Guide - Mentorship
  Building,      // Bureau - Operations
  Waypoints      // Gateway - Networking
]

const serviceImages = [
  '/coworking-space.jpg',    // Grand Square - Co-working
  '/training.jpg',           // Village School - Training
  '/innovation.jpg',         // Market - Business Development
  '/teambuilding.jpg',       // Field - VC Studio
  '/consulting.jpg',         // Council - Consulting
  '/teambuilding.jpg',       // Carpenters - Team Building
  '/mentorship.jpg',         // Guide - Mentorship
  '/innovation.jpg',         // Bureau - Operations
  '/village-collaboration.jpg' // Gateway - Networking
]

export function ServicesSection() {
  const { t } = useLanguage()
  const [selectedService, setSelectedService] = useState<number | null>(null)
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  const services = Array.from({ length: 9 }, (_, i) => ({
    id: i + 1,
    name: t(`service_${i + 1}_name`),
    description: t(`service_${i + 1}_desc`),
    details: t(`service_${i + 1}_details`),
    icon: serviceIcons[i],
    image: serviceImages[i]
  }))

  return (
    <section id="services" className="py-24 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
          viewport={{ once: true }}
          // @ts-ignore
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6">
            {t('services_title')}
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            {t('services_subtitle')}
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services?.map((service, index) => {
            const IconComponent = service.icon
            return (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2, delay: index * 0.1 }}
                viewport={{ once: true }}
                // @ts-ignore
                className="group cursor-pointer"
                onClick={() => setSelectedService(service.id)}
              >
                <div className="bg-white rounded-2xl overflow-hidden h-full hover:shadow-xl transition-all duration-300 border border-stone-200 hover:border-amber-200">
                  <div className="relative aspect-[4/3]">
                    <Image
                      src={service.image}
                      alt={service.name}
                      fill
                      placeholder="blur"
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-slate-900/20 group-hover:bg-slate-900/10 transition-colors duration-300" />
                    <div className="absolute top-4 right-4 w-12 h-12 bg-white/90 rounded-xl flex items-center justify-center group-hover:bg-amber-600 group-hover:scale-110 transition-all duration-300">
                      <IconComponent className="h-6 w-6 text-amber-600 group-hover:text-white transition-colors duration-300" />
                    </div>
                  </div>
                  
                  <div className="p-6 space-y-3">
                    <h3 className="text-lg font-bold text-slate-800 group-hover:text-amber-600 transition-colors duration-300">
                      {service.name}
                    </h3>
                    
                    <p className="text-slate-600 text-sm leading-relaxed line-clamp-3">
                      {service.description}
                    </p>
                    
                    <motion.div
                      // @ts-ignore
                      className="text-amber-600 font-medium text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      whileHover={{ scale: 1.05 }}
                    >
                      {t('learn_more')} →
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Service Modal */}
      {selectedService && (
        <ServiceModal
          service={services.find(s => s.id === selectedService)!}
          isOpen={selectedService !== null}
          onClose={() => setSelectedService(null)}
        />
      )}
    </section>
  )
}
