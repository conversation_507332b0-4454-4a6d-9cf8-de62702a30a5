
'use client'
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useT } from './language-provider';
import { ArrowRight, Mail, Phone, MapPin, Users } from 'lucide-react'
import { Button } from './ui/button'
import { ContactModal } from './contact-modal'

export function CTASection() {
  const t = useT();
  const [isContactModalOpen, setIsContactModalOpen] = useState(false)

  const stats = [
    { value: '9', label: 'Village Districts', icon: MapPin },
    { value: '100+', label: 'Community Members', icon: Users },
    { value: '∞', label: 'Possibilities', icon: ArrowRight }
  ]

  return (
    <section id="cta" className="relative py-24 bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-white/10 opacity-20" />
      
      {/* Floating Elements */}

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-8">
          {/* Main CTA Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
            viewport={{ once: true }}
            // @ts-ignore
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
              {t('cta_title')}
            </h2>
            <p className="text-xl md:text-2xl text-orange-100 leading-relaxed mb-8">
              {t('cta_subtitle')}
            </p>
          </motion.div>

          {/* Stats Row */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.2 }}
            viewport={{ once: true }}
            // @ts-ignore
            className="grid md:grid-cols-3 gap-8 max-w-3xl mx-auto mb-12"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2, delay: 0.1 * index }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
                // @ts-ignore
                className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-white/20 rounded-full mx-auto mb-4">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-orange-200 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.4 }}
            viewport={{ once: true }}
            // @ts-ignore
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <div>
              <Button
                size="lg"
                onClick={() => setIsContactModalOpen(true)}
                className="bg-white text-orange-600 hover:bg-orange-50 px-8 py-4 text-lg font-semibold shadow-xl cta-scale-effect"
              >
                {t('cta_button')}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>

            <div>
              <Button
                variant="outline"
                size="lg"
                onClick={() => setIsContactModalOpen(true)}
                className="border-white/30 text-foreground-primary hover:bg-white/10 px-8 py-4 text-lg cta-scale-effect"
              >
                {t('cta_secondary')}
                <Mail className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </motion.div>

          {/* Village Promise */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.6 }}
            viewport={{ once: true }}
            // @ts-ignore
            className="max-w-2xl mx-auto"
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">N</span>
                </div>
                <span className="text-white font-semibold">Netaj Promise</span>
              </div>
              <p className="text-orange-100 text-sm leading-relaxed">
                "Join our village where every idea has the potential to grow, every entrepreneur finds their community, and every dream gets the support it needs to flourish."
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      <ContactModal 
        isOpen={isContactModalOpen}
        onClose={() => setIsContactModalOpen(false)}
      />
    </section>
  )
}
