
'use client'
import React from 'react'
import { motion } from 'framer-motion'
import { useT } from './language-provider';
import { Heart, MapPin, Mail, Phone } from 'lucide-react'

export function Footer() {
  const t = useT();

  return (
    <footer className="bg-gradient-to-b from-gray-900 to-black text-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            // @ts-ignore
            className="space-y-6"
            // @ts-ignore
          >
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-amber-600 to-orange-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">N</span>
              </div>
              <div>
                <h3 className="text-2xl font-bold">Netaj</h3>
                <p className="text-amber-400 text-sm">The Business Village</p>
              </div>
            </div>
            
            <p className="text-gray-300 leading-relaxed">
              {t('footer_tagline')}
            </p>

            <div className="flex items-center space-x-2 text-amber-400">
              <Heart className="h-4 w-4" />
              <span className="text-sm">Building dreams, one idea at a time</span>
            </div>
          </motion.div>

          {/* Village Districts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            // @ts-ignore
            className="space-y-6"
          >
            <h4 className="text-lg font-semibold text-amber-400">Village Districts</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              {[
                'The Grand Square',
                'Village School', 
                'The Market',
                'The Field - Seed',
                'The Council',
                'Carpenters Workshop',
                'The Guide',
                'The Bureau',
                'The Gateway'
              ].map((district, index) => (
                <motion.div
                  key={district}
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.05 * index }}
                  viewport={{ once: true }}
                  // @ts-ignore
                  className="text-gray-400 hover:text-amber-400 transition-colors cursor-pointer"
                >
                  {district}
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            // @ts-ignore
            className="space-y-6"
          >
            <h4 className="text-lg font-semibold text-amber-400">Visit Our Village</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-white font-medium">Village Location</p>
                  <p className="text-gray-400 text-sm">Where innovation meets community</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Mail className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-white font-medium"><EMAIL></p>
                  <p className="text-gray-400 text-sm">Your ideas are welcome</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Phone className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-white font-medium">+1 (555) VILLAGE</p>
                  <p className="text-gray-400 text-sm">Let's start the conversation</p>
                </div>
              </div>
            </div>

            {/* Village Stats */}
            <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-xl font-bold text-amber-400">9</div>
                  <div className="text-xs text-gray-400">Districts</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-green-400">∞</div>
                  <div className="text-xs text-gray-400">Ideas</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-blue-400">1</div>
                  <div className="text-xs text-gray-400">Community</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          // @ts-ignore
          className="mt-12 pt-8 border-t border-gray-800 flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0"
        >
          <p className="text-gray-400 text-sm">
            {t('footer_copyright')}
          </p>
          
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <span>Made with</span>
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <Heart className="h-4 w-4 text-red-500" />
            </motion.div>
            <span>for entrepreneurs worldwide</span>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}
