import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { OfflineStateHandler, useOfflineQueue } from '../offline-state-handler'
import { useToast } from '@/hooks/use-toast'

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: vi.fn(() => ({
    toast: vi.fn()
  }))
}))

// Mock fetch for health check
global.fetch = vi.fn()

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true,
})

describe('OfflineStateHandler', () => {
  const mockToast = vi.fn()
  const mockOnConnectionRestored = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useToast as any).mockReturnValue({ toast: mockToast })
    localStorageMock.getItem.mockReturnValue(null)
    navigator.onLine = true
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  it('renders children when online', () => {
    render(
      <OfflineStateHandler>
        <div data-testid="child-content">Test Content</div>
      </OfflineStateHandler>
    )

    expect(screen.getByTestId('child-content')).toBeInTheDocument()
    expect(screen.queryByText("You're Offline")).not.toBeInTheDocument()
  })

  it('shows offline indicator when offline', () => {
    navigator.onLine = false

    render(
      <OfflineStateHandler>
        <div data-testid="child-content">Test Content</div>
      </OfflineStateHandler>
    )

    expect(screen.getByText("You're Offline")).toBeInTheDocument()
    expect(screen.getByText('No Connection')).toBeInTheDocument()
    expect(screen.getByTestId('child-content')).toBeInTheDocument()
  })

  it('hides offline indicator when showOfflineIndicator is false', () => {
    navigator.onLine = false

    render(
      <OfflineStateHandler showOfflineIndicator={false}>
        <div data-testid="child-content">Test Content</div>
      </OfflineStateHandler>
    )

    expect(screen.queryByText("You're Offline")).not.toBeInTheDocument()
    expect(screen.getByTestId('child-content')).toBeInTheDocument()
  })

  it('handles online event and calls onConnectionRestored', async () => {
    navigator.onLine = false

    render(
      <OfflineStateHandler onConnectionRestored={mockOnConnectionRestored}>
        <div>Test Content</div>
      </OfflineStateHandler>
    )

    expect(screen.getByText("You're Offline")).toBeInTheDocument()

    // Simulate going online
    navigator.onLine = true
    act(() => {
      window.dispatchEvent(new Event('online'))
    })

    await waitFor(() => {
      expect(mockOnConnectionRestored).toHaveBeenCalled()
      expect(mockToast).toHaveBeenCalledWith({
        title: "Connection Restored",
        description: "You're back online! Syncing any pending changes...",
        className: "border-green-200 bg-green-50 text-green-900"
      })
    })
  })

  it('handles offline event and shows toast', async () => {
    navigator.onLine = true

    render(
      <OfflineStateHandler>
        <div>Test Content</div>
      </OfflineStateHandler>
    )

    // Simulate going offline
    navigator.onLine = false
    act(() => {
      window.dispatchEvent(new Event('offline'))
    })

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        variant: "destructive",
        title: "Connection Lost",
        description: "You're currently offline. Some features may be limited.",
        duration: 6000
      })
    })
  })

  it('handles retry connection successfully', async () => {
    navigator.onLine = false
    ;(global.fetch as any).mockResolvedValueOnce({ ok: true })

    render(
      <OfflineStateHandler onConnectionRestored={mockOnConnectionRestored}>
        <div>Test Content</div>
      </OfflineStateHandler>
    )

    const retryButton = screen.getByText('Retry Connection')
    fireEvent.click(retryButton)

    expect(screen.getByText('Checking...')).toBeInTheDocument()

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/health', {
        signal: expect.any(AbortSignal),
        cache: 'no-cache'
      })
      expect(mockOnConnectionRestored).toHaveBeenCalled()
      expect(mockToast).toHaveBeenCalledWith({
        title: "Connection Restored",
        description: "Successfully reconnected to the server.",
        className: "border-green-200 bg-green-50 text-green-900"
      })
    })
  })

  it('handles retry connection failure', async () => {
    navigator.onLine = false
    ;(global.fetch as any).mockRejectedValueOnce(new Error('Network error'))

    render(
      <OfflineStateHandler>
        <div>Test Content</div>
      </OfflineStateHandler>
    )

    const retryButton = screen.getByText('Retry Connection')
    fireEvent.click(retryButton)

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        variant: "destructive",
        title: "Connection Failed",
        description: "Still unable to connect. Please check your internet connection.",
      })
    })
  })

  it('uses custom health check endpoint', async () => {
    navigator.onLine = false
    ;(global.fetch as any).mockResolvedValueOnce({ ok: true })

    render(
      <OfflineStateHandler healthCheckEndpoint="/custom/health">
        <div>Test Content</div>
      </OfflineStateHandler>
    )

    const retryButton = screen.getByText('Retry Connection')
    fireEvent.click(retryButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/custom/health', {
        signal: expect.any(AbortSignal),
        cache: 'no-cache'
      })
    })
  })

  it('shows retry count after multiple attempts', async () => {
    navigator.onLine = false
    ;(global.fetch as any).mockRejectedValue(new Error('Network error'))

    render(
      <OfflineStateHandler>
        <div>Test Content</div>
      </OfflineStateHandler>
    )

    const retryButton = screen.getByText('Retry Connection')
    
    // First retry
    fireEvent.click(retryButton)
    await waitFor(() => {
      expect(screen.getByText('1 attempt')).toBeInTheDocument()
    })

    // Second retry - the button text stays the same, but retry count increases
    const retryButtonAfterFirst = screen.getByText('Retry Connection')
    fireEvent.click(retryButtonAfterFirst)
    await waitFor(() => {
      expect(screen.getByText('2 attempts')).toBeInTheDocument()
    })
  })

  it('loads and displays queued actions from localStorage', () => {
    navigator.onLine = false
    const mockQueue = [
      {
        id: 'test1',
        type: 'create',
        data: { name: 'Test' },
        description: 'Create test item',
        timestamp: new Date().toISOString()
      }
    ]
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockQueue))

    render(
      <OfflineStateHandler enablePersistence={true} storageKey="test_queue">
        <div>Test Content</div>
      </OfflineStateHandler>
    )

    expect(screen.getByText('1 pending action')).toBeInTheDocument()
    expect(screen.getByText('• Create test item')).toBeInTheDocument()
  })

  it('disables persistence when enablePersistence is false', () => {
    navigator.onLine = false

    render(
      <OfflineStateHandler enablePersistence={false}>
        <div>Test Content</div>
      </OfflineStateHandler>
    )

    expect(screen.queryByText('pending action')).not.toBeInTheDocument()
  })
})

describe('useOfflineQueue', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    navigator.onLine = true
  })

  it('returns correct online status', () => {
    let result: any

    function TestComponent() {
      result = useOfflineQueue()
      return null
    }

    render(<TestComponent />)
    expect(result.isOnline).toBe(true)

    navigator.onLine = false
    render(<TestComponent />)
    expect(result.isOnline).toBe(false)
  })

  it('adds action to queue when offline', () => {
    navigator.onLine = false
    let result: any

    function TestComponent() {
      result = useOfflineQueue(true, 'test_queue')
      return null
    }

    render(<TestComponent />)
    
    act(() => {
      result.addOfflineAction('test_type', { data: 'test' }, 'Test action')
    })

    expect(localStorageMock.setItem).toHaveBeenCalled()
  })

  it('does not add action when online', () => {
    navigator.onLine = true
    let result: any

    function TestComponent() {
      result = useOfflineQueue(true, 'test_queue')
      return null
    }

    render(<TestComponent />)
    
    act(() => {
      result.addOfflineAction('test_type', { data: 'test' }, 'Test action')
    })

    expect(localStorageMock.setItem).not.toHaveBeenCalled()
  })

  it('returns correct queue size', () => {
    const mockQueue = [
      { id: '1', type: 'test', data: {}, description: 'Test 1', timestamp: new Date().toISOString() },
      { id: '2', type: 'test', data: {}, description: 'Test 2', timestamp: new Date().toISOString() }
    ]
    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockQueue))

    let result: any

    function TestComponent() {
      result = useOfflineQueue(true, 'test_queue')
      return null
    }

    render(<TestComponent />)
    expect(result.queueSize).toBe(2)
  })

  it('returns 0 queue size when disabled', () => {
    let result: any

    function TestComponent() {
      result = useOfflineQueue(false)
      return null
    }

    render(<TestComponent />)
    expect(result.queueSize).toBe(0)
  })
})