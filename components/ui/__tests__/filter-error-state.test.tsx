import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { FilterErrorState, useErrorState } from '../filter-error-state'

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true,
})

describe('FilterErrorState', () => {
  const mockOnRetry = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    navigator.onLine = true
  })

  it('renders error message and retry button', () => {
    render(
      <FilterErrorState
        error="Test error message"
        filterType="status"
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByText('Unexpected Error')).toBeInTheDocument()
    expect(screen.getByText('Something went wrong while loading status data.')).toBeInTheDocument()
    expect(screen.getByText('status filter')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
  })

  it('categorizes network errors correctly', () => {
    render(
      <FilterErrorState
        error="Network connection failed"
        filterType="resource"
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByText('Connection Problem')).toBeInTheDocument()
    expect(screen.getByText('Unable to load resource data due to a network issue.')).toBeInTheDocument()
  })

  it('categorizes server errors correctly', () => {
    render(
      <FilterErrorState
        error="500 Internal Server Error"
        filterType="date"
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByText('Server Error')).toBeInTheDocument()
    expect(screen.getByText('The server is having trouble loading date data.')).toBeInTheDocument()
  })

  it('categorizes authentication errors correctly', () => {
    render(
      <FilterErrorState
        error="401 Unauthorized"
        filterType="search"
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByText('Authentication Required')).toBeInTheDocument()
    expect(screen.getByText("You don't have permission to access search data.")).toBeInTheDocument()
  })

  it('categorizes validation errors correctly', () => {
    render(
      <FilterErrorState
        error="400 Bad Request - Invalid validation"
        filterType="general"
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByText('Invalid Request')).toBeInTheDocument()
    expect(screen.getByText('There was a problem with the general filter request.')).toBeInTheDocument()
  })

  it('handles offline state correctly', () => {
    navigator.onLine = false

    render(
      <FilterErrorState
        error="Network error"
        filterType="status"
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByText('Connection Problem')).toBeInTheDocument()
    expect(screen.getByText('You appear to be offline.')).toBeInTheDocument()
  })

  it('calls onRetry when retry button is clicked', async () => {
    render(
      <FilterErrorState
        error="Test error"
        filterType="status"
        onRetry={mockOnRetry}
      />
    )

    const retryButton = screen.getByRole('button', { name: /retry/i })
    fireEvent.click(retryButton)

    await waitFor(() => {
      expect(mockOnRetry).toHaveBeenCalledTimes(1)
    })
  })

  it('shows loading state when loading prop is true', () => {
    render(
      <FilterErrorState
        error="Test error"
        filterType="status"
        onRetry={mockOnRetry}
        loading={true}
      />
    )

    const retryButton = screen.getByRole('button', { name: /retry/i })
    expect(retryButton).toBeDisabled()
  })

  it('shows retry count after multiple attempts', async () => {
    render(
      <FilterErrorState
        error="Test error"
        filterType="status"
        onRetry={mockOnRetry}
      />
    )

    const retryButton = screen.getByRole('button', { name: /retry/i })
    
    // First retry
    fireEvent.click(retryButton)
    await waitFor(() => {
      expect(screen.getByText('1 attempt')).toBeInTheDocument()
    })

    // Second retry
    const retryButton2 = screen.getByRole('button', { name: /retry \(2\)/i })
    fireEvent.click(retryButton2)
    await waitFor(() => {
      expect(screen.getByText('2 attempts')).toBeInTheDocument()
    })
  })

  it('shows expanded details when showDetails is true', () => {
    const error = new Error('Test error with stack')
    error.stack = 'Error: Test error\n    at test.js:1:1'

    render(
      <FilterErrorState
        error={error}
        filterType="status"
        onRetry={mockOnRetry}
        showDetails={true}
      />
    )

    const showDetailsButton = screen.getByRole('button', { name: /show details/i })
    fireEvent.click(showDetailsButton)

    expect(screen.getByText('Error Type:')).toBeInTheDocument()
    expect(screen.getByText('Message:')).toBeInTheDocument()
    expect(screen.getByText('Time:')).toBeInTheDocument()
  })

  it('uses custom context in error messages', () => {
    render(
      <FilterErrorState
        error="Test error"
        filterType="status"
        onRetry={mockOnRetry}
        context="bookings"
      />
    )

    expect(screen.getByText('Something went wrong while loading status bookings.')).toBeInTheDocument()
  })

  it('uses custom retry button text', () => {
    render(
      <FilterErrorState
        error="Test error"
        filterType="status"
        onRetry={mockOnRetry}
        retryButtonText="Try Again"
      />
    )

    expect(screen.getByRole('button', { name: 'Try Again' })).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(
      <FilterErrorState
        error="Test error"
        filterType="status"
        onRetry={mockOnRetry}
        className="custom-class"
      />
    )

    expect(container.querySelector('.custom-class')).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(
      <FilterErrorState
        error="Test error"
        filterType="status"
        onRetry={mockOnRetry}
        announceErrors={true}
      />
    )

    const errorCard = screen.getByRole('alert')
    expect(errorCard).toHaveAttribute('aria-live', 'polite')
    expect(errorCard).toHaveAttribute('aria-atomic', 'true')

    const badge = screen.getByText('status filter')
    expect(badge).toHaveAttribute('aria-label', 'status filter error')
  })

  it('disables accessibility announcements when announceErrors is false', () => {
    render(
      <FilterErrorState
        error="Test error"
        filterType="status"
        onRetry={mockOnRetry}
        announceErrors={false}
      />
    )

    const errorCard = screen.getByRole('alert')
    expect(errorCard).not.toHaveAttribute('aria-live')
  })

  it('handles Error objects correctly', () => {
    const error = new Error('Custom error message')
    
    render(
      <FilterErrorState
        error={error}
        filterType="status"
        onRetry={mockOnRetry}
        showDetails={true}
      />
    )

    // The error message appears in the expanded details section
    const showDetailsButton = screen.getByRole('button', { name: /show details/i })
    fireEvent.click(showDetailsButton)

    expect(screen.getByText('Custom error message')).toBeInTheDocument()
  })

  it('shows retrying state during retry operation', async () => {
    const slowRetry = vi.fn(() => new Promise(resolve => setTimeout(resolve, 100)))

    render(
      <FilterErrorState
        error="Test error"
        filterType="status"
        onRetry={slowRetry}
      />
    )

    const retryButton = screen.getByRole('button', { name: /retry/i })
    fireEvent.click(retryButton)

    expect(screen.getByText('Retrying...')).toBeInTheDocument()
    expect(retryButton).toBeDisabled()

    await waitFor(() => {
      expect(screen.queryByText('Retrying...')).not.toBeInTheDocument()
    })
  })
})

describe('useErrorState', () => {
  it('initializes with no error', () => {
    let result: any

    function TestComponent() {
      result = useErrorState()
      return null
    }

    render(<TestComponent />)
    
    expect(result.error).toBeNull()
    expect(result.retryCount).toBe(0)
    expect(result.isRetrying).toBe(false)
  })

  it('initializes with provided error', () => {
    let result: any

    function TestComponent() {
      result = useErrorState('Initial error')
      return null
    }

    render(<TestComponent />)
    
    expect(result.error).toBe('Initial error')
    expect(result.retryCount).toBe(0)
    expect(result.isRetrying).toBe(false)
  })

  it('sets and clears errors correctly', () => {
    let result: any

    function TestComponent() {
      result = useErrorState()
      return null
    }

    render(<TestComponent />)
    
    act(() => {
      result.setError('New error')
    })
    expect(result.error).toBe('New error')

    act(() => {
      result.clearError()
    })
    expect(result.error).toBeNull()
    expect(result.retryCount).toBe(0)
    expect(result.isRetrying).toBe(false)
  })

  it('increments retry count', () => {
    let result: any

    function TestComponent() {
      result = useErrorState()
      return null
    }

    render(<TestComponent />)
    
    act(() => {
      result.incrementRetry()
    })
    expect(result.retryCount).toBe(1)

    act(() => {
      result.incrementRetry()
    })
    expect(result.retryCount).toBe(2)
  })

  it('resets retry count when setting new error', () => {
    let result: any

    function TestComponent() {
      result = useErrorState()
      return null
    }

    render(<TestComponent />)
    
    act(() => {
      result.incrementRetry()
      result.incrementRetry()
    })
    expect(result.retryCount).toBe(2)

    act(() => {
      result.setError('New error')
    })
    expect(result.retryCount).toBe(0)
    expect(result.isRetrying).toBe(false)
  })

  it('manages retrying state', () => {
    let result: any

    function TestComponent() {
      result = useErrorState()
      return null
    }

    render(<TestComponent />)
    
    act(() => {
      result.setIsRetrying(true)
    })
    expect(result.isRetrying).toBe(true)

    act(() => {
      result.setIsRetrying(false)
    })
    expect(result.isRetrying).toBe(false)
  })
})