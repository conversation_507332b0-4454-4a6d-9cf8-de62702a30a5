"use client"

import Re<PERSON>, { useEffect, useState, use<PERSON><PERSON><PERSON>, memo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  WifiOff,
  RefreshCw,
  Clock,
  AlertCircle,
  Download
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

/**
 * Props interface for the OfflineStateHandler component
 */
export interface OfflineStateHandlerProps {
  /** Child components to render when online or with offline overlay */
  children: React.ReactNode
  /** Callback fired when connection is restored */
  onConnectionRestored?: () => void
  /** Whether to show the offline indicator UI */
  showOfflineIndicator?: boolean
  /** Whether to enable offline action persistence */
  enablePersistence?: boolean
  /** Custom storage key for offline queue (defaults to generic key) */
  storageKey?: string
  /** Custom health check endpoint (defaults to /api/health) */
  healthCheckEndpoint?: string
  /** Timeout for connection retry attempts in milliseconds */
  connectionTimeout?: number
}

/**
 * Internal connection state interface
 */
interface ConnectionState {
  isOnline: boolean
  lastOnlineTime: Date | null
  retryCount: number
  isRetrying: boolean
}

/**
 * Interface for offline actions that can be queued
 */
export interface OfflineAction {
  id: string
  type: string
  timestamp: Date
  data: any
  description: string
}

/**
 * Utility class for managing offline actions queue
 * Provides persistence and queue management for offline operations
 */
class OfflineQueue {
  private static instances: Map<string, OfflineQueue> = new Map()
  private queue: OfflineAction[] = []
  private storageKey: string

  /**
   * Get or create an OfflineQueue instance for a specific storage key
   */
  public static getInstance(storageKey: string = 'app_offline_queue'): OfflineQueue {
    if (!OfflineQueue.instances.has(storageKey)) {
      OfflineQueue.instances.set(storageKey, new OfflineQueue(storageKey))
    }
    return OfflineQueue.instances.get(storageKey)!
  }

  constructor(storageKey: string) {
    this.storageKey = storageKey
    this.loadFromStorage()
  }

  /**
   * Add an action to the offline queue
   */
  public addAction(action: Omit<OfflineAction, 'id' | 'timestamp'>): void {
    const offlineAction: OfflineAction = {
      ...action,
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
      timestamp: new Date()
    }
    
    this.queue.push(offlineAction)
    this.saveToStorage()
  }

  /**
   * Get a copy of the current queue
   */
  public getQueue(): OfflineAction[] {
    return [...this.queue]
  }

  /**
   * Remove an action from the queue by ID
   */
  public removeAction(id: string): void {
    this.queue = this.queue.filter(action => action.id !== id)
    this.saveToStorage()
  }

  /**
   * Clear all actions from the queue
   */
  public clearQueue(): void {
    this.queue = []
    this.saveToStorage()
  }

  /**
   * Load queue from localStorage
   */
  private loadFromStorage(): void {
    if (typeof window === 'undefined') return
    
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const parsed = JSON.parse(stored)
        this.queue = parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }))
      }
    } catch (error) {
      console.warn('Failed to load offline queue from storage:', error)
    }
  }

  /**
   * Save queue to localStorage
   */
  private saveToStorage(): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.queue))
    } catch (error) {
      console.warn('Failed to save offline queue to storage:', error)
    }
  }
}

/**
 * OfflineStateHandler Component
 * 
 * Provides comprehensive offline state management for table components.
 * Handles connection detection, retry logic, and optional action queuing.
 * 
 * @example
 * ```tsx
 * <OfflineStateHandler 
 *   onConnectionRestored={() => refetchData()}
 *   enablePersistence={true}
 *   storageKey="bookings_offline"
 * >
 *   <YourTableComponent />
 * </OfflineStateHandler>
 * ```
 */
export const OfflineStateHandler = memo(({
  children,
  onConnectionRestored,
  showOfflineIndicator = true,
  enablePersistence = true,
  storageKey = 'app_offline_queue',
  healthCheckEndpoint = '/api/health',
  connectionTimeout = 5000
}: OfflineStateHandlerProps) => {
  const { toast } = useToast()
  const offlineQueue = enablePersistence ? OfflineQueue.getInstance(storageKey) : null
  
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    isOnline: typeof window !== 'undefined' ? navigator.onLine : true,
    lastOnlineTime: typeof window !== 'undefined' && !navigator.onLine ? new Date() : null,
    retryCount: 0,
    isRetrying: false
  })
  
  const [queuedActions, setQueuedActions] = useState<OfflineAction[]>([])

  // Update queued actions when offline queue changes
  useEffect(() => {
    if (offlineQueue) {
      setQueuedActions(offlineQueue.getQueue())
    }
  }, [offlineQueue])

  /**
   * Process queued actions when connection is restored
   */
  const processQueuedActions = useCallback(async () => {
    if (!offlineQueue || queuedActions.length === 0) return

    toast({
      title: "Syncing Changes",
      description: `Processing ${queuedActions.length} pending action${queuedActions.length !== 1 ? 's' : ''}...`,
      className: "border-blue-200 bg-blue-50 text-blue-900"
    })

    let successCount = 0
    let failureCount = 0

    for (const action of queuedActions) {
      try {
        // Simulate processing - in real implementation, this would handle actual sync
        await new Promise(resolve => setTimeout(resolve, 500))
        
        offlineQueue.removeAction(action.id)
        successCount++
      } catch (error) {
        failureCount++
        console.error('Failed to process offline action:', action, error)
      }
    }

    // Update local state
    setQueuedActions(offlineQueue.getQueue())

    // Show completion message
    if (successCount > 0) {
      toast({
        title: "Sync Complete",
        description: `Successfully synced ${successCount} action${successCount !== 1 ? 's' : ''}${
          failureCount > 0 ? `. ${failureCount} failed and will retry later.` : '.'
        }`,
        className: "border-green-200 bg-green-50 text-green-900"
      })
    }
  }, [offlineQueue, queuedActions, toast])

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      const wasOffline = !connectionState.isOnline
      
      setConnectionState(prev => ({
        ...prev,
        isOnline: true,
        retryCount: 0,
        isRetrying: false
      }))

      if (wasOffline) {
        toast({
          title: "Connection Restored",
          description: "You're back online! Syncing any pending changes...",
          className: "border-green-200 bg-green-50 text-green-900"
        })
        
        onConnectionRestored?.()
        
        // Process queued actions when coming back online
        if (offlineQueue && queuedActions.length > 0) {
          processQueuedActions()
        }
      }
    }

    const handleOffline = () => {
      setConnectionState(prev => ({
        ...prev,
        isOnline: false,
        lastOnlineTime: new Date(),
        retryCount: 0,
        isRetrying: false
      }))

      toast({
        variant: "destructive",
        title: "Connection Lost",
        description: "You're currently offline. Some features may be limited.",
        duration: 6000
      })
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [connectionState.isOnline, onConnectionRestored, toast, offlineQueue, queuedActions.length, processQueuedActions])

  /**
   * Manually retry connection with health check
   */
  const handleRetryConnection = useCallback(async () => {
    setConnectionState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1
    }))

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), connectionTimeout)
      
      const response = await fetch(healthCheckEndpoint, {
        signal: controller.signal,
        cache: 'no-cache'
      })
      
      clearTimeout(timeoutId)
      
      if (response.ok) {
        // Connection successful
        setConnectionState(prev => ({
          ...prev,
          isOnline: true,
          isRetrying: false,
          retryCount: 0
        }))
        
        toast({
          title: "Connection Restored",
          description: "Successfully reconnected to the server.",
          className: "border-green-200 bg-green-50 text-green-900"
        })
        
        onConnectionRestored?.()
      } else {
        throw new Error('Server not responding')
      }
    } catch (error) {
      setConnectionState(prev => ({
        ...prev,
        isRetrying: false
      }))
      
      toast({
        variant: "destructive",
        title: "Connection Failed",
        description: "Still unable to connect. Please check your internet connection.",
      })
    }
  }, [onConnectionRestored, toast, connectionTimeout, healthCheckEndpoint])

  /**
   * Calculate and format offline duration
   */
  const getOfflineDuration = useCallback(() => {
    if (!connectionState.lastOnlineTime) return null
    
    const now = new Date()
    const diff = now.getTime() - connectionState.lastOnlineTime.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    } else if (minutes > 0) {
      return `${minutes}m`
    } else {
      return 'Just now'
    }
  }, [connectionState.lastOnlineTime])

  // Don't show offline indicator if online or if disabled
  if (connectionState.isOnline || !showOfflineIndicator) {
    return <>{children}</>
  }

  return (
    <div className="space-y-4">
      {/* Offline Indicator */}
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <WifiOff className="h-5 w-5 text-red-600" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h4 className="font-medium text-sm text-red-800">You're Offline</h4>
                <Badge variant="outline" className="text-xs border-red-300 text-red-700">
                  No Connection
                </Badge>
              </div>
              
              <p className="text-sm text-red-700 mb-2">
                Some features may be limited while you're offline.
              </p>
              
              {connectionState.lastOnlineTime && (
                <p className="text-xs text-red-600 mb-3">
                  Last connected: {getOfflineDuration()} ago
                </p>
              )}
              
              <div className="flex items-center gap-2 flex-wrap">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRetryConnection}
                        disabled={connectionState.isRetrying}
                        className="h-7 border-red-300 text-red-700 hover:bg-red-100"
                      >
                        <RefreshCw className={`h-3 w-3 mr-1 ${connectionState.isRetrying ? 'animate-spin' : ''}`} />
                        {connectionState.isRetrying ? 'Checking...' : 'Retry Connection'}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Test connection to server</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                {connectionState.retryCount > 0 && (
                  <div className="flex items-center gap-1 text-xs text-red-600">
                    <Clock className="h-3 w-3" />
                    <span>{connectionState.retryCount} attempt{connectionState.retryCount !== 1 ? 's' : ''}</span>
                  </div>
                )}
                
                {enablePersistence && queuedActions.length > 0 && (
                  <div className="flex items-center gap-1 text-xs text-red-600">
                    <Download className="h-3 w-3" />
                    <span>{queuedActions.length} pending action{queuedActions.length !== 1 ? 's' : ''}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Queued Actions Info */}
      {enablePersistence && queuedActions.length > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            <strong>Offline Queue:</strong> {queuedActions.length} action{queuedActions.length !== 1 ? 's' : ''} will be synced when connection is restored.
            {queuedActions.slice(0, 3).map((action, index) => (
              <div key={action.id} className="text-xs text-muted-foreground mt-1">
                • {action.description}
              </div>
            ))}
            {queuedActions.length > 3 && (
              <div className="text-xs text-muted-foreground mt-1">
                • ...and {queuedActions.length - 3} more
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Content with reduced functionality */}
      <div className="opacity-75 pointer-events-auto">
        {children}
      </div>
    </div>
  )
})

OfflineStateHandler.displayName = "OfflineStateHandler"

/**
 * Hook for components to interact with the offline queue
 * 
 * @param enabled - Whether offline queue functionality is enabled
 * @param storageKey - Custom storage key for the offline queue
 * @returns Object with offline queue utilities
 * 
 * @example
 * ```tsx
 * const { addOfflineAction, isOnline, queueSize } = useOfflineQueue(true, 'bookings_offline')
 * 
 * const handleCreateBooking = (data) => {
 *   if (!isOnline) {
 *     addOfflineAction('create_booking', data, 'Create new booking')
 *   } else {
 *     // Handle online creation
 *   }
 * }
 * ```
 */
export const useOfflineQueue = (enabled: boolean = true, storageKey: string = 'app_offline_queue') => {
  const offlineQueue = enabled ? OfflineQueue.getInstance(storageKey) : null
  
  const addOfflineAction = useCallback((
    type: string,
    data: any,
    description: string
  ) => {
    if (offlineQueue && typeof window !== 'undefined' && !navigator.onLine) {
      offlineQueue.addAction({ type, data, description })
    }
  }, [offlineQueue])
  
  return {
    addOfflineAction,
    isOnline: typeof window !== 'undefined' ? navigator.onLine : true,
    queueSize: offlineQueue?.getQueue().length || 0
  }
}