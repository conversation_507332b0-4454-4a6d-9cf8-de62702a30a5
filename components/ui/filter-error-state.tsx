"use client"

import React, { memo, use<PERSON>allback, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  AlertTriangle,
  RefreshCw,
  Wifi,
  WifiOff,
  Clock,
  Server,
  Shield,
  ChevronDown,
  ChevronUp
} from "lucide-react"

/**
 * Props interface for the FilterErrorState component
 */
export interface FilterErrorStateProps {
  /** The error object or message to display */
  error: Error | string
  /** The type of filter that encountered the error */
  filterType: 'status' | 'resource' | 'date' | 'search' | 'general' | 'customer' | 'invoice' | 'user' | 'amenity' | 'catering' | string
  /** Callback function to retry the failed operation */
  onRetry: () => void
  /** Whether the retry operation is currently loading */
  loading?: boolean
  /** Additional CSS classes to apply */
  className?: string
  /** Whether to show detailed error information */
  showDetails?: boolean
  /** Custom context for error messages (e.g., "table", "form", "filter") */
  context?: string
  /** Custom retry button text */
  retryButtonText?: string
  /** Whether to show accessibility announcements */
  announceErrors?: boolean
}

/**
 * Internal interface for categorized error details
 */
interface ErrorDetails {
  type: 'network' | 'server' | 'auth' | 'validation' | 'unknown'
  title: string
  description: string
  actionable: string
  icon: React.ComponentType<any>
  severity: 'low' | 'medium' | 'high'
}

/**
 * Categorize an error based on its message and type
 * @param error - The error to categorize
 * @param filterType - The type of filter that failed
 * @param context - Additional context for error messages
 * @returns Categorized error details
 */
const categorizeError = (error: Error | string, filterType: string, context: string = 'data'): ErrorDetails => {
  const errorMessage = typeof error === 'string' ? error : error.message
  const lowerMessage = errorMessage.toLowerCase()

  // Network errors
  if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || 
      lowerMessage.includes('connection') || lowerMessage.includes('timeout') ||
      (typeof window !== 'undefined' && !navigator.onLine)) {
    return {
      type: 'network',
      title: 'Connection Problem',
      description: typeof window !== 'undefined' && navigator.onLine 
        ? `Unable to load ${filterType} ${context} due to a network issue.`
        : 'You appear to be offline.',
      actionable: typeof window !== 'undefined' && navigator.onLine 
        ? 'Check your internet connection and try again.'
        : 'Please check your internet connection.',
      icon: typeof window !== 'undefined' && navigator.onLine ? Wifi : WifiOff,
      severity: 'high'
    }
  }

  // Server errors
  if (lowerMessage.includes('500') || lowerMessage.includes('502') || 
      lowerMessage.includes('503') || lowerMessage.includes('server')) {
    return {
      type: 'server',
      title: 'Server Error',
      description: `The server is having trouble loading ${filterType} ${context}.`,
      actionable: 'Our team has been notified. Please try again in a moment.',
      icon: Server,
      severity: 'high'
    }
  }

  // Authentication errors
  if (lowerMessage.includes('401') || lowerMessage.includes('403') || 
      lowerMessage.includes('unauthorized') || lowerMessage.includes('forbidden')) {
    return {
      type: 'auth',
      title: 'Authentication Required',
      description: `You don't have permission to access ${filterType} ${context}.`,
      actionable: 'Please refresh the page and try again.',
      icon: Shield,
      severity: 'medium'
    }
  }

  // Validation errors
  if (lowerMessage.includes('validation') || lowerMessage.includes('invalid') ||
      lowerMessage.includes('400')) {
    return {
      type: 'validation',
      title: 'Invalid Request',
      description: `There was a problem with the ${filterType} filter request.`,
      actionable: 'Please reset your filters and try again.',
      icon: AlertTriangle,
      severity: 'medium'
    }
  }

  // Unknown errors
  return {
    type: 'unknown',
    title: 'Unexpected Error',
    description: `Something went wrong while loading ${filterType} ${context}.`,
    actionable: 'Please try again. If the problem persists, contact support.',
    icon: AlertTriangle,
    severity: 'low'
  }
}

/**
 * Get CSS classes for error severity styling
 * @param severity - The error severity level
 * @returns CSS classes for styling
 */
const getSeverityColor = (severity: ErrorDetails['severity']) => {
  switch (severity) {
    case 'high':
      return 'border-red-200 bg-red-50 text-red-800'
    case 'medium':
      return 'border-yellow-200 bg-yellow-50 text-yellow-800'
    case 'low':
      return 'border-blue-200 bg-blue-50 text-blue-800'
    default:
      return 'border-gray-200 bg-gray-50 text-gray-800'
  }
}

/**
 * Calculate retry delay using exponential backoff
 * @param attempt - The retry attempt number
 * @returns Delay in milliseconds
 */
const getRetryDelay = (attempt: number): number => {
  // Exponential backoff: 1s, 2s, 4s, 8s, max 30s
  return Math.min(1000 * Math.pow(2, attempt), 30000)
}

/**
 * FilterErrorState Component
 * 
 * Provides consistent error state display and retry functionality for table filters.
 * Categorizes errors, provides appropriate messaging, and handles retry logic with exponential backoff.
 * 
 * @example
 * ```tsx
 * <FilterErrorState
 *   error={error}
 *   filterType="status"
 *   onRetry={() => refetchData()}
 *   loading={isRetrying}
 *   showDetails={true}
 *   context="bookings"
 * />
 * ```
 */
export const FilterErrorState = memo(({
  error,
  filterType,
  onRetry,
  loading = false,
  className = "",
  showDetails = false,
  context = "data",
  retryButtonText,
  announceErrors = true
}: FilterErrorStateProps) => {
  const [retryCount, setRetryCount] = useState(0)
  const [showExpandedDetails, setShowExpandedDetails] = useState(false)
  const [isRetrying, setIsRetrying] = useState(false)
  
  const errorDetails = categorizeError(error, filterType, context)
  const Icon = errorDetails.icon
  
  /**
   * Handle retry with exponential backoff
   */
  const handleRetry = useCallback(async () => {
    setIsRetrying(true)
    setRetryCount(prev => prev + 1)
    
    // Add exponential backoff delay for retries
    if (retryCount > 0) {
      const delay = getRetryDelay(retryCount - 1)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
    
    try {
      await onRetry()
    } finally {
      setIsRetrying(false)
    }
  }, [onRetry, retryCount])

  /**
   * Generate a simple error ID for debugging
   */
  const getErrorId = useCallback(() => {
    if (typeof error === 'object' && error.stack) {
      // Generate a simple error ID from stack trace hash
      const stackHash = error.stack.slice(0, 100).split('').reduce(
        (hash, char) => hash + char.charCodeAt(0), 0
      ).toString(36)
      return `err_${Date.now().toString(36)}_${stackHash.slice(-6)}`
    }
    return null
  }, [error])

  const isDisabled = loading || isRetrying
  const defaultRetryText = isRetrying ? 'Retrying...' : retryCount > 0 ? `Retry (${retryCount + 1})` : 'Retry'
  const buttonText = retryButtonText || defaultRetryText

  return (
    <TooltipProvider>
      <Card 
        className={`border ${getSeverityColor(errorDetails.severity)} ${className}`}
        role="alert"
        aria-live={announceErrors ? "polite" : undefined}
        aria-atomic="true"
      >
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <Icon className="h-5 w-5" aria-hidden="true" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h4 className="font-medium text-sm" id="error-title">
                  {errorDetails.title}
                </h4>
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getSeverityColor(errorDetails.severity)}`}
                  aria-label={`${filterType} filter error`}
                >
                  {filterType} filter
                </Badge>
              </div>
              
              <p className="text-sm text-muted-foreground mb-2" id="error-description">
                {errorDetails.description}
              </p>
              
              <p className="text-xs text-muted-foreground mb-3" id="error-action">
                {errorDetails.actionable}
              </p>
              
              <div className="flex items-center gap-2 flex-wrap">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRetry}
                      disabled={isDisabled}
                      className="h-7"
                      aria-describedby="error-title error-description error-action"
                    >
                      <RefreshCw 
                        className={`h-3 w-3 mr-1 ${isRetrying ? 'animate-spin' : ''}`} 
                        aria-hidden="true"
                      />
                      {buttonText}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isDisabled ? 'Please wait...' : `Retry loading ${filterType} ${context}`}</p>
                  </TooltipContent>
                </Tooltip>
                
                {retryCount > 0 && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" aria-hidden="true" />
                    <span aria-label={`${retryCount} retry attempts made`}>
                      {retryCount} attempt{retryCount !== 1 ? 's' : ''}
                    </span>
                  </div>
                )}
                
                {showDetails && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowExpandedDetails(!showExpandedDetails)}
                    className="h-7 text-xs"
                    aria-expanded={showExpandedDetails}
                    aria-controls="error-details"
                  >
                    {showExpandedDetails ? (
                      <>
                        <ChevronUp className="h-3 w-3 mr-1" aria-hidden="true" />
                        Hide Details
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-3 w-3 mr-1" aria-hidden="true" />
                        Show Details
                      </>
                    )}
                  </Button>
                )}
              </div>
              
              {showExpandedDetails && showDetails && (
                <Alert className="mt-3" id="error-details">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    <div className="space-y-1">
                      <p><strong>Error Type:</strong> {errorDetails.type}</p>
                      <p><strong>Message:</strong> {typeof error === 'string' ? error : error.message}</p>
                      {getErrorId() && (
                        <p><strong>Error ID:</strong> {getErrorId()}</p>
                      )}
                      <p><strong>Time:</strong> {new Date().toLocaleString()}</p>
                      <p><strong>Filter:</strong> {filterType}</p>
                      <p><strong>Context:</strong> {context}</p>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  )
})

FilterErrorState.displayName = "FilterErrorState"

/**
 * Hook for managing error state with retry logic
 * @param initialError - Initial error state
 * @returns Error state management utilities
 */
export const useErrorState = (initialError: Error | string | null = null) => {
  const [error, setError] = useState<Error | string | null>(initialError)
  const [retryCount, setRetryCount] = useState(0)
  const [isRetrying, setIsRetrying] = useState(false)

  const clearError = useCallback(() => {
    setError(null)
    setRetryCount(0)
    setIsRetrying(false)
  }, [])

  const setErrorWithReset = useCallback((newError: Error | string | null) => {
    setError(newError)
    if (newError) {
      setRetryCount(0)
      setIsRetrying(false)
    }
  }, [])

  const incrementRetry = useCallback(() => {
    setRetryCount(prev => prev + 1)
  }, [])

  return {
    error,
    retryCount,
    isRetrying,
    setError: setErrorWithReset,
    clearError,
    incrementRetry,
    setIsRetrying
  }
}