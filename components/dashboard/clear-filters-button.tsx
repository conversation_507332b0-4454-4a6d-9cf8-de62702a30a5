"use client";

import React, { memo, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Filter } from "lucide-react";
import { cn } from "@/lib/utils";

interface ClearFiltersButtonProps {
  // Filter states - generic approach
  filters: Record<string, any>;
  
  // Actions
  onClearFilters: () => void;
  
  // UI props
  disabled?: boolean;
  className?: string;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "default" | "lg";
  showFilterCount?: boolean;
  showFilterBadges?: boolean;
  "aria-label"?: string;
}

export const ClearFiltersButton = memo(({
  filters,
  onClearFilters,
  disabled = false,
  className,
  variant = "outline",
  size = "default",
  showFilterCount = true,
  showFilterBadges = false,
  "aria-label": ariaLabel = "Clear all applied filters",
}: ClearFiltersButtonProps) => {
  
  // Calculate active filters
  const activeFilters = useMemo(() => {
    const filterList: string[] = [];
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '' && value !== 'ALL') {
        // Handle different filter types
        switch (key) {
          case 'searchQuery':
            if (typeof value === 'string' && value.trim().length >= 3) {
              filterList.push('Search');
            }
            break;
          case 'selectedStatus':
          case 'status':
            if (value !== 'ALL') {
              filterList.push('Status');
            }
            break;
          case 'selectedResource':
          case 'resource':
            if (value !== 'ALL') {
              filterList.push('Resource');
            }
            break;
          case 'selectedType':
          case 'type':
            if (value !== 'ALL') {
              filterList.push('Type');
            }
            break;
          case 'selectedInvoiceStatus':
          case 'invoiceStatus':
            if (value !== 'ALL') {
              filterList.push('Invoice Status');
            }
            break;
          case 'selectedCustomerId':
          case 'customerId':
            if (value !== 'ALL') {
              filterList.push('Customer');
            }
            break;
          case 'startDate':
            if (value && !filters.endDate) {
              filterList.push('Start Date');
            }
            break;
          case 'endDate':
            if (value && !filters.startDate) {
              filterList.push('End Date');
            }
            break;
          default:
            // Handle date range
            if ((key === 'startDate' || key === 'endDate') && value && filters.startDate && filters.endDate) {
              if (!filterList.includes('Date Range')) {
                filterList.push('Date Range');
              }
            }
            break;
        }
      }
    });
    
    // Handle date range specifically
    if (filters.startDate && filters.endDate && !filterList.includes('Date Range')) {
      filterList.push('Date Range');
    }
    
    return filterList;
  }, [filters]);

  const hasActiveFilters = activeFilters.length > 0;

  // Don't render if no filters are active
  if (!hasActiveFilters) {
    return null;
  }

  const handleClearFilters = () => {
    onClearFilters();
  };

  const getButtonText = () => {
    if (showFilterCount && activeFilters.length > 0) {
      return `Clear Filters (${activeFilters.length})`;
    }
    return "Clear Filters";
  };

  const getFilterSummary = () => {
    if (activeFilters.length === 0) return "";
    if (activeFilters.length === 1) return activeFilters[0];
    if (activeFilters.length === 2) return activeFilters.join(" and ");
    return `${activeFilters.slice(0, -1).join(", ")}, and ${activeFilters[activeFilters.length - 1]}`;
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Button
        variant={variant}
        size={size}
        onClick={handleClearFilters}
        disabled={disabled}
        className={cn(
          "transition-all duration-200",
          variant === "outline" && "border-destructive/50 text-destructive hover:bg-destructive/10 hover:border-destructive",
          variant === "ghost" && "text-destructive hover:bg-destructive/10",
          variant === "default" && "bg-destructive hover:bg-destructive/90"
        )}
        aria-label={ariaLabel}
        aria-describedby="clear-filters-description"
      >
        <X className="h-4 w-4 mr-2" aria-hidden="true" />
        <span>{getButtonText()}</span>
      </Button>

      {/* Active filters indicator */}
      {showFilterBadges && activeFilters.length > 0 && (
        <div className="flex items-center space-x-1">
          <Filter className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
          <div className="flex flex-wrap gap-1">
            {activeFilters.map((filter, index) => (
              <Badge 
                key={index}
                variant="secondary" 
                className="text-xs bg-primary/10 text-primary border-primary/20"
              >
                {filter}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Screen reader description */}
      <span 
        id="clear-filters-description" 
        className="sr-only"
      >
        {activeFilters.length > 0 
          ? `Currently filtering by: ${getFilterSummary()}. Click to clear all filters and show all items.`
          : "No filters are currently applied."
        }
      </span>
    </div>
  );
});

ClearFiltersButton.displayName = "ClearFiltersButton";