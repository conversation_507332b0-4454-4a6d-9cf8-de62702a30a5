"use client";

import React, { memo, useMemo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { FileText } from "lucide-react";
import { InvoiceStatus } from "@/lib/types";
import { cn } from "@/lib/utils";

interface InvoiceStatusFilterProps {
  selectedStatus: InvoiceStatus | 'ALL';
  onStatusChange: (status: InvoiceStatus | 'ALL') => void;
  disabled?: boolean;
  className?: string;
  "aria-label"?: string;
}

// Invoice status labels
const INVOICE_STATUS_LABELS: Record<InvoiceStatus, string> = {
  PENDING: 'Pending',
  PARTIALLY_PAID: 'Partially Paid',
  PAID: 'Paid',
  CANCELLED: 'Cancelled'
};

// Status colors for visual distinction
const STATUS_COLORS: Record<InvoiceStatus, string> = {
  PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
  PARTIALLY_PAID: "bg-blue-100 text-blue-800 border-blue-200",
  PAID: "bg-green-100 text-green-800 border-green-200",
  CANCELLED: "bg-red-100 text-red-800 border-red-200",
};

// Status indicator dots
const STATUS_DOTS: Record<InvoiceStatus, string> = {
  PENDING: "bg-yellow-500",
  PARTIALLY_PAID: "bg-blue-500",
  PAID: "bg-green-500",
  CANCELLED: "bg-red-500",
};

export const InvoiceStatusFilter = memo(({
  selectedStatus,
  onStatusChange,
  disabled = false,
  className,
  "aria-label": ariaLabel = "Filter invoices by status",
}: InvoiceStatusFilterProps) => {
  
  // Status options with proper labeling
  const statusOptions = useMemo(() => [
    { value: 'ALL', label: 'All Status', count: null },
    { value: 'PENDING', label: INVOICE_STATUS_LABELS.PENDING, count: null },
    { value: 'PARTIALLY_PAID', label: INVOICE_STATUS_LABELS.PARTIALLY_PAID, count: null },
    { value: 'PAID', label: INVOICE_STATUS_LABELS.PAID, count: null },
    { value: 'CANCELLED', label: INVOICE_STATUS_LABELS.CANCELLED, count: null },
  ], []);

  const handleValueChange = (value: string) => {
    const newStatus = value === 'ALL' ? 'ALL' : value as InvoiceStatus;
    onStatusChange(newStatus);
  };

  const getDisplayValue = () => {
    if (selectedStatus === 'ALL') {
      return 'All Status';
    }
    return INVOICE_STATUS_LABELS[selectedStatus];
  };

  const getStatusBadge = (status: InvoiceStatus | 'ALL') => {
    if (status === 'ALL') {
      return null;
    }
    
    return (
      <Badge 
        variant="outline" 
        className={cn(
          "ml-2 text-xs font-medium border",
          STATUS_COLORS[status]
        )}
      >
        {INVOICE_STATUS_LABELS[status]}
      </Badge>
    );
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <div className="flex items-center space-x-1">
        <FileText className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
      </div>
      
      <Select 
        value={selectedStatus} 
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger 
          className={cn(
            "w-full h-9 text-sm",
            selectedStatus !== 'ALL' && "border-primary/50 bg-primary/5"
          )}
          aria-label={ariaLabel}
          aria-describedby={selectedStatus !== 'ALL' ? "invoice-status-filter-description" : undefined}
        >
          <SelectValue>
            <div className="flex items-center">
              <span>{getDisplayValue()}</span>
              {selectedStatus !== 'ALL' && getStatusBadge(selectedStatus)}
            </div>
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent>
          {statusOptions.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value}
              className="cursor-pointer"
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center">
                  {option.value !== 'ALL' && (
                    <div className={cn(
                      "w-2 h-2 rounded-full mr-2",
                      STATUS_DOTS[option.value as InvoiceStatus]
                    )} />
                  )}
                  <span>{option.label}</span>
                </div>
                {option.value !== 'ALL' && (
                  <Badge 
                    variant="outline" 
                    className={cn(
                      "ml-2 text-xs font-medium border",
                      STATUS_COLORS[option.value as InvoiceStatus]
                    )}
                  >
                    {option.value}
                  </Badge>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {/* Screen reader description for active filter */}
      {selectedStatus !== 'ALL' && (
        <span 
          id="invoice-status-filter-description" 
          className="sr-only"
        >
          Currently filtering by {INVOICE_STATUS_LABELS[selectedStatus]} invoices
        </span>
      )}
    </div>
  );
});

InvoiceStatusFilter.displayName = "InvoiceStatusFilter";