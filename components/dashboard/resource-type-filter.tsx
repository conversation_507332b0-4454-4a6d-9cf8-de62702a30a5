"use client";

import React, { memo, useMemo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Building2 } from "lucide-react";
import { ResourceType, getResourceTypeDisplayName } from "@/lib/types";
import { cn } from "@/lib/utils";

interface ResourceTypeFilterProps {
  selectedType: ResourceType | 'ALL';
  onTypeChange: (type: ResourceType | 'ALL') => void;
  disabled?: boolean;
  className?: string;
  "aria-label"?: string;
}

export const ResourceTypeFilter = memo(({
  selectedType,
  onTypeChange,
  disabled = false,
  className,
  "aria-label": ariaLabel = "Filter resources by type",
}: ResourceTypeFilterProps) => {
  
  // Type options with proper labeling
  const typeOptions = useMemo(() => [
    { value: 'ALL', label: 'All Types', icon: null },
    { value: 'INDOOR_EVENT_HALL', label: getResourceTypeDisplayName('INDOOR_EVENT_HALL'), icon: null },
    { value: 'OUTDOOR_EVENT_HALL', label: getResourceTypeDisplayName('OUTDOOR_EVENT_HALL'), icon: null },
    { value: 'TRAINING_ROOM', label: getResourceTypeDisplayName('TRAINING_ROOM'), icon: null },
    { value: 'MEETING_ROOM', label: getResourceTypeDisplayName('MEETING_ROOM'), icon: null },
    { value: 'DESK', label: getResourceTypeDisplayName('DESK'), icon: null },
    { value: 'PRIVATE_OFFICE', label: getResourceTypeDisplayName('PRIVATE_OFFICE'), icon: null },
  ], []);

  const handleValueChange = (value: string) => {
    const newType = value === 'ALL' ? 'ALL' : value as ResourceType;
    onTypeChange(newType);
  };

  const getDisplayValue = () => {
    if (selectedType === 'ALL' || !selectedType) {
      return 'All Types';
    }
    return getResourceTypeDisplayName(selectedType);
  };

  const getTypeBadge = (type: ResourceType | 'ALL') => {
    if (type === 'ALL') {
      return null;
    }
    
    return (
      <Badge 
        variant="outline" 
        className={cn(
          "ml-2 text-xs font-medium border",
          "bg-blue-100 text-blue-800 border-blue-200"
        )}
      >
        {getResourceTypeDisplayName(type)}
      </Badge>
    );
  };

  const getTypeIcon = (type: ResourceType | 'ALL') => {
    if (type === 'ALL') return null;
    // Use Building2 icon for all resource types for now
    return <Building2 className="h-3 w-3 mr-2" />;
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <div className="flex items-center space-x-1">
        <Building2 className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
      </div>
      
      <Select 
        value={selectedType} 
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger 
          className={cn(
            "w-full h-9 text-sm",
            selectedType !== 'ALL' && "border-primary/50 bg-primary/5"
          )}
          aria-label={ariaLabel}
          aria-describedby={selectedType !== 'ALL' ? "resource-type-filter-description" : undefined}
        >
          <SelectValue>
            <div className="flex items-center">
              {selectedType !== 'ALL' && getTypeIcon(selectedType)}
              <span>{getDisplayValue()}</span>
              {selectedType !== 'ALL' && getTypeBadge(selectedType)}
            </div>
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent>
          {typeOptions.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value}
              className="cursor-pointer"
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center">
                  {option.value !== 'ALL' && getTypeIcon(option.value as ResourceType)}
                  <span>{option.label}</span>
                </div>
                {option.value !== 'ALL' && getTypeBadge(option.value as ResourceType)}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {/* Screen reader description for active filter */}
      {selectedType !== 'ALL' && (
        <span 
          id="resource-type-filter-description" 
          className="sr-only"
        >
          Currently filtering by {getResourceTypeDisplayName(selectedType)} resources
        </span>
      )}
    </div>
  );
});

ResourceTypeFilter.displayName = "ResourceTypeFilter";