model Customer {
  id             Int       @id @default(autoincrement())
  name           String
  email          String?
  phoneNumber    String?
  companyName    String?
  specialization String?
  industry       String?
  website        String?
  linkedIn       String?
  socialMedia    String?
  notes          String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  createdById    Int?
  updatedById    Int?
  bookings       Booking[]
  updatedBy      User?     @relation("CustomerUpdatedBy", fields: [updatedById], references: [id])
  createdBy      User?     @relation("CustomerCreatedBy", fields: [createdById], references: [id])
}
