model Period {
  id          Int      @id @default(autoincrement())
  bookingId   Int
  start       DateTime
  end         DateTime
  
  // Recurring period support
  isRecurring <PERSON><PERSON><PERSON>  @default(false)
  recurrenceRule String? // JSON string containing recurrence configuration
  parentPeriodId Int?   // Reference to the original period for recurring instances
  
  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById Int?
  updatedById Int?
  
  // Relations
  booking     Booking  @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  parentPeriod Period? @relation("PeriodRecurrence", fields: [parentPeriodId], references: [id])
  childPeriods Period[] @relation("PeriodRecurrence")
  updatedBy   User?    @relation("PeriodUpdatedBy", fields: [updatedById], references: [id])
  createdBy   User?    @relation("PeriodCreatedBy", fields: [createdById], references: [id])
  
  // Constraints and indexes for performance optimization
  @@unique([bookingId, start, end], name: "unique_period_booking_time")
  @@index([start, end], name: "period_time_idx")
  @@index([bookingId], name: "period_booking_idx")
  @@index([parentPeriodId], name: "period_parent_idx")
}