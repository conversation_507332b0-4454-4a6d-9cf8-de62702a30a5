{"buildCommand": "prisma generate --schema=prisma/schema && next build", "outputDirectory": ".next", "framework": "nextjs", "functions": {"app/api/**/*.ts": {"maxDuration": 10}}, "regions": ["iad1"], "env": {"NODE_ENV": "production"}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "build": {"env": {"NODE_ENV": "production"}}}