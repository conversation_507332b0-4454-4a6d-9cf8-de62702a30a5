# Design Document

## Overview

This document outlines the technical design for standardizing quantity field naming across the entire application. The refactoring will eliminate the inconsistency between "qty" (database schema) and "quantity" (TypeScript interfaces, validation schemas, and UI components) by standardizing everything to use "quantity".

The design follows a systematic approach: database migration first, followed by API layer simplification, and finally test updates. This ensures data integrity while eliminating the complex field mapping logic currently required throughout the application.

## Architecture

### Current State Analysis

The application currently has a split architecture where:

1. **Database Layer**: Uses `qty` field in the `LineItem` model
2. **Application Layer**: Uses `quantity` in TypeScript interfaces, validation schemas, and UI components
3. **API Layer**: Contains mapping logic to bridge the gap between `qty` and `quantity`

### Target State Architecture

The refactored architecture will have:

1. **Database Layer**: Uses `quantity` field in the `LineItem` model (consistent with `CateringOnBooking`)
2. **Application Layer**: Continues using `quantity` (no changes needed)
3. **API Layer**: Direct field usage without mapping logic

## Components and Interfaces

### 1. Database Schema Changes

#### Primary Change: LineItem Model
```prisma
// Current (prisma/schema/invoice.prisma)
model LineItem {
  // ... other fields
  qty         Int       @default(1)  // ← Will be renamed
  // ... other fields
}

// Target
model LineItem {
  // ... other fields
  quantity    Int       @default(1)  // ← Renamed from qty
  // ... other fields
}
```

#### Migration Strategy
- Create a Prisma migration that renames the `qty` column to `quantity`
- Ensure data preservation during the migration
- Handle any constraints or indexes that reference the old column name

### 2. API Layer Simplification

#### Current Mapping Logic (to be removed)
```typescript
// In app/api/invoices/route.ts, app/api/invoices/[id]/route.ts, etc.
const lineItemsData = validatedData.lineItems.map(item => ({
  // ... other fields
  qty: item.quantity,  // ← This mapping will be removed
  // ... other fields
}));
```

#### Target Direct Usage
```typescript
// After refactoring
const lineItemsData = validatedData.lineItems.map(item => ({
  // ... other fields
  quantity: item.quantity,  // ← Direct usage, no mapping
  // ... other fields
}));
```

#### Affected API Routes
1. `app/api/invoices/route.ts` - Invoice creation
2. `app/api/invoices/[id]/route.ts` - Invoice updates and fetching
3. `app/api/bookings/[id]/invoice/route.ts` - Invoice generation from bookings
4. `app/api/bookings/[id]/route.ts` - Booking details with invoice data
5. `app/api/invoices/[id]/recalculate/route.ts` - Invoice recalculation

### 3. Prisma Query Updates

#### Current Prisma Selects (to be updated)
```typescript
// Current
select: {
  qty: true,  // ← Will be changed to quantity
  // ... other fields
}
```

#### Target Prisma Selects
```typescript
// Target
select: {
  quantity: true,  // ← Updated field name
  // ... other fields
}
```

### 4. TypeScript Interfaces (No Changes Required)

The `LineItem` interface in `lib/types.ts` already uses `quantity`, so no changes are needed:

```typescript
export interface LineItem {
  // ... other fields
  quantity: number;  // ← Already correct
  // ... other fields
}
```

### 5. Validation Schemas (No Changes Required)

The validation schemas in `lib/validations/invoice.ts` and `lib/validations/booking.ts` already use `quantity`, so no changes are needed.

## Data Models

### Database Migration Design

#### Migration File Structure
```sql
-- Migration: Rename qty to quantity in LineItem table
ALTER TABLE "LineItem" RENAME COLUMN "qty" TO "quantity";
```

#### Migration Safety Considerations
1. **Simple Column Rename**: Since this is a new project with no production data, the migration is straightforward
2. **Atomic Operation**: Column rename is an atomic operation in PostgreSQL
3. **Index Handling**: Any indexes on the `qty` column will be automatically updated
4. **Constraint Handling**: Default value and NOT NULL constraints will be preserved

### Data Flow After Refactoring

```mermaid
graph TD
    A[UI Component] -->|quantity| B[Validation Schema]
    B -->|quantity| C[API Route]
    C -->|quantity| D[Prisma Query]
    D -->|quantity| E[Database]
    E -->|quantity| F[Response]
    F -->|quantity| G[TypeScript Interface]
    G -->|quantity| H[UI Display]
```

## Error Handling

### Migration Error Scenarios

1. **Column Rename Failure**: If the migration fails, Prisma will provide detailed error messages
2. **Data Type Conflicts**: The migration should not encounter type conflicts since we're only renaming
3. **Constraint Violations**: Existing constraints will be preserved during rename

### Runtime Error Handling

After the refactoring, error handling will be simplified since there will be no field mapping logic to fail.

## Testing Strategy

### 1. Database Migration Testing

#### Migration Validation
- Confirm the new `quantity` column has the correct constraints
- Test that default values work correctly for new records
- Verify the migration applies cleanly without errors

### 2. API Integration Testing

#### Test Data Updates
All test files will need updates to use consistent field names:

1. `app/api/invoices/__tests__/route.test.ts`
2. `app/api/invoices/[id]/__tests__/route.test.ts`
3. `app/api/invoices/__tests__/integration.test.ts`
4. `app/api/bookings/[id]/invoice/__tests__/route.test.ts`

#### Test Scenarios
- Create invoices with line items
- Update existing invoices
- Generate invoices from bookings
- Recalculate invoice totals
- Fetch invoice details

### 3. UI Component Testing

#### Component Test Updates
UI components that display or manipulate quantity values will need test updates to ensure they work correctly with the standardized field names.

### 4. End-to-End Testing

#### Workflow Testing
- Complete booking-to-invoice workflow
- Invoice creation and payment processing
- Catering quantity calculations
- Revenue sharing calculations

## Implementation Phases

### Phase 1: Database Migration
1. Create and test the Prisma migration
2. Apply the migration to development environment
3. Verify data integrity post-migration

### Phase 2: API Layer Updates
1. Remove field mapping logic from all API routes
2. Update Prisma select statements
3. Update any direct database queries

### Phase 3: Test Suite Updates
1. Update all test data to use consistent field names
2. Update test expectations and assertions
3. Run full test suite to ensure no regressions

### Phase 4: Validation and Cleanup
1. Remove any remaining references to `qty`
2. Verify all calculations work correctly
3. Test UI components for proper data display

## Risk Mitigation

### Migration Safety
- The migration is a simple column rename with no data transformation
- Prisma migrations are transactional and can be rolled back
- Since this is a new project, no data preservation concerns

### Deployment Strategy
- Apply database migration first
- Deploy code changes immediately after migration
- Monitor for any runtime errors post-deployment

### Rollback Plan
- Prisma migration can be rolled back if issues occur
- Code changes can be reverted independently
- Since no production data exists, rollback is straightforward

## Performance Considerations

### Migration Performance
- Column rename is a metadata operation and should be fast
- No data copying or transformation required
- Minimal downtime expected

### Runtime Performance
- Elimination of field mapping logic will slightly improve performance
- Reduced cognitive overhead for developers
- Cleaner, more maintainable code

## Security Considerations

### Data Integrity
- Migration is safe for new project with no production data
- No sensitive data exposure during refactoring
- Validation schemas remain unchanged (already secure)

### Access Control
- No changes to authentication or authorization logic
- API endpoints maintain existing security measures
- Database permissions remain unchanged