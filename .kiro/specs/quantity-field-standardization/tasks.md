# Implementation Plan

- [ ] 1. Update database schema and create migration
  - Modify the LineItem model in prisma/schema/invoice.prisma to change `qty` field to `quantity`
  - Generate and apply Prisma migration to rename the database column
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [ ] 2. Remove field mapping logic from invoice creation API
  - Update app/api/invoices/route.ts to use `quantity` directly instead of mapping `qty: item.quantity`
  - Remove the field transformation logic in the lineItemsData mapping
  - _Requirements: 3.1, 3.2_

- [ ] 3. Remove field mapping logic from invoice update API
  - Update app/api/invoices/[id]/route.ts to use `quantity` directly in update operations
  - Update Prisma select statements to use `quantity` instead of `qty`
  - Remove field transformation logic in update operations
  - _Requirements: 3.2, 3.3_

- [ ] 4. Update invoice generation from booking API
  - Modify app/api/bookings/[id]/invoice/route.ts to use `quantity` directly
  - Remove the mapping comment and field transformation logic
  - _Requirements: 3.4_

- [ ] 5. Update booking details API for invoice data
  - Modify app/api/bookings/[id]/route.ts to select `quantity` instead of `qty` in Prisma queries
  - Update the lineItems select statement in the invoice relation
  - _Requirements: 3.3_

- [ ] 6. Update invoice recalculation API
  - Modify app/api/invoices/[id]/recalculate/route.ts to use `quantity` field consistently
  - Update any Prisma select statements that reference the old field name
  - _Requirements: 3.5_

- [ ] 7. Update invoice API route tests
  - Modify app/api/invoices/**tests**/route.test.ts to use `quantity` consistently in test data
  - Update test expectations to check for `quantity` instead of `qty`
  - Fix any test data that mixes `qty` and `quantity` usage
  - _Requirements: 4.1, 4.2_

- [ ] 8. Update invoice ID API route tests
  - Modify app/api/invoices/[id]/**tests**/route.test.ts to use `quantity` consistently
  - Update test data creation and expectations
  - Remove any references to `qty` in test assertions
  - _Requirements: 4.1, 4.2_

- [ ] 9. Update invoice integration tests
  - Modify app/api/invoices/**tests**/integration.test.ts to use `quantity` consistently
  - Update test data for both regular and catering line items
  - Fix test expectations to use the standardized field name
  - _Requirements: 4.3_

- [ ] 10. Update booking invoice generation tests
  - Modify app/api/bookings/[id]/invoice/**tests**/route.test.ts to use `quantity` consistently
  - Update test data and expectations for invoice generation from bookings
  - _Requirements: 4.1, 4.2_

- [ ] 11. Update UI components displaying line item quantities
  - Modify app/dashboard/bookings/components/booking-invoice-tab.tsx to use `quantity` instead of `qty`
  - Update quantity display and calculation logic
  - _Requirements: 5.1, 5.3_

- [ ] 12. Verify and test catering revenue breakdown component
  - Review app/dashboard/invoices/components/catering-revenue-breakdown.tsx to ensure it uses `quantity` consistently
  - Test that calculations work correctly with the standardized field name
  - _Requirements: 5.2, 5.4_

- [ ] 13. Run comprehensive test suite
  - Execute all tests to ensure no regressions were introduced
  - Verify that all quantity-related functionality works correctly
  - Test invoice creation, updates, and calculations end-to-end
  - _Requirements: 4.4, 8.1, 8.2, 8.3_
