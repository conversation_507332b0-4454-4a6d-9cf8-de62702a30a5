# Requirements Document

## Introduction

This document outlines the requirements for standardizing the inconsistent use of "qty" versus "quantity" field names across the entire application. The current codebase has significant inconsistencies between database schema, TypeScript interfaces, validation schemas, API layers, and UI components, creating technical debt and potential bugs. This refactoring will standardize all quantity-related fields to use "quantity" for improved readability, maintainability, and consistency.

## Requirements

### Requirement 1: Database Schema Standardization

**User Story:** As a developer, I want all quantity-related database fields to use consistent naming, so that I don't need to remember different field names across different models.

#### Acceptance Criteria

1. WH<PERSON> updating the invoice schema THEN the LineItem model SHALL use "quantity" instead of "qty"
2. WHEN creating database migrations THEN the system SHALL safely rename the "qty" column to "quantity" without data loss
3. WHEN querying LineItem records THEN all database operations SHALL use the "quantity" field name
4. WHEN the migration is complete THEN the CateringOnBooking model SHALL continue using "quantity" (already consistent)

### Requirement 2: TypeScript Interface Alignment

**User Story:** As a developer, I want TypeScript interfaces to accurately reflect the database schema, so that type safety is maintained and there's no confusion between interface definitions and actual data structure.

#### Acceptance Criteria

1. WHEN the database migration is complete THEN the LineItem interface SHALL already be using "quantity" (no changes needed)
2. WHEN working with LineItem objects THEN TypeScript SHALL provide accurate type checking without field name mismatches
3. WHEN serializing/deserializing data THEN the field names SHALL be consistent between database and TypeScript types

### Requirement 3: API Layer Simplification

**User Story:** As a developer, I want to eliminate field name mapping logic in API routes, so that the code is simpler and less error-prone.

#### Acceptance Criteria

1. WHEN creating invoices THEN the API SHALL directly use "quantity" without mapping to "qty"
2. WHEN updating invoices THEN the API SHALL directly use "quantity" without mapping to "qty"
3. WHEN fetching invoice data THEN Prisma queries SHALL use "quantity" field name consistently
4. WHEN generating invoices from bookings THEN the API SHALL use "quantity" without field name transformations
5. WHEN recalculating invoices THEN all quantity references SHALL use the standardized field name

### Requirement 4: Test Suite Updates

**User Story:** As a developer, I want all tests to use consistent field naming, so that tests accurately reflect the production code structure.

#### Acceptance Criteria

1. WHEN running invoice API tests THEN all test data SHALL use "quantity" instead of "qty"
2. WHEN testing line item creation THEN test expectations SHALL check for "quantity" field
3. WHEN running integration tests THEN all quantity-related assertions SHALL use the standardized field name
4. WHEN tests execute THEN there SHALL be no field name mismatches causing test failures

### Requirement 5: UI Component Consistency

**User Story:** As a developer, I want UI components to use consistent field names when working with quantity data, so that the frontend code is maintainable and predictable.

#### Acceptance Criteria

1. WHEN displaying line item quantities THEN components SHALL reference the "quantity" field consistently
2. WHEN calculating totals THEN all quantity-based calculations SHALL use the standardized field name
3. WHEN binding form inputs THEN quantity fields SHALL use consistent naming
4. WHEN components receive data from APIs THEN there SHALL be no field name transformations needed

### Requirement 6: Validation Schema Consistency

**User Story:** As a developer, I want validation schemas to match the actual data structure, so that validation works correctly without field name confusion.

#### Acceptance Criteria

1. WHEN validating invoice data THEN validation schemas SHALL continue using "quantity" (already consistent)
2. WHEN validating booking data THEN catering item validation SHALL continue using "quantity" (already consistent)
3. WHEN validation errors occur THEN error messages SHALL reference the correct field names

### Requirement 7: Data Migration Safety

**User Story:** As a system administrator, I want the database migration to be safe and reversible, so that existing data is preserved and the system can be rolled back if needed.

#### Acceptance Criteria

1. WHEN running the migration THEN all existing "qty" values SHALL be preserved in the new "quantity" column
2. WHEN the migration completes THEN data integrity SHALL be maintained with no data loss
3. WHEN a rollback is needed THEN the migration SHALL be reversible
4. WHEN the migration runs THEN it SHALL handle edge cases like null values appropriately

### Requirement 8: Code Quality and Maintainability

**User Story:** As a developer, I want the refactored code to be cleaner and more maintainable, so that future development is easier and less error-prone.

#### Acceptance Criteria

1. WHEN the refactoring is complete THEN there SHALL be no field name mapping logic in API routes
2. WHEN working with quantity fields THEN developers SHALL only need to remember one field name ("quantity")
3. WHEN onboarding new developers THEN the consistent naming SHALL reduce the learning curve
4. WHEN making future changes THEN the standardized naming SHALL reduce cognitive load

