# Design Document

## Overview

This design document outlines the refactoring of the bookings page from a single calendar view to a tabbed interface with a comprehensive table view as the primary tab and the existing calendar view as the secondary tab. The design follows established patterns from the resources management page while adapting them specifically for booking data and workflows.

## Architecture

### Component Structure

```
BookingsPage (refactored)
├── Tabs Component
│   ├── Table View Tab (default)
│   │   ├── BookingsTable Component
│   │   ├── BookingsTableSkeleton Component
│   │   ├── BookingCard Component (mobile)
│   │   └── Filter Controls
│   │       ├── Status Filter
│   │       ├── Resource Filter
│   │       ├── Date Range Picker
│   │       ├── Customer Search
│   │       └── Clear Filters Button
│   └── Calendar View Tab
│       └── BookingCalendar Component (existing)
├── BookingFormDialog Component (existing)
├── DeleteConfirmationDialog Component (existing)
└── BookingManagementErrorBoundary Component (existing)
```

### State Management

The design leverages the existing `useBookings` hook which already provides:
- Booking data fetching with pagination
- Search functionality
- Status filtering
- Date range filtering
- CRUD operations

Additional state will be managed for:
- Active tab selection
- Table sorting configuration
- Resource filter selection
- UI state (loading, errors, dialogs)

## Components and Interfaces

### 1. BookingsPage Component (Refactored)

**Purpose:** Main page component that orchestrates the tabbed interface

**Key Changes:**
- Wrap existing content in a Tabs component
- Add tab switching logic
- Maintain URL state for active tab
- Preserve existing error handling and loading states

**Props Interface:**
```typescript
// No props - page component
```

**State Interface:**
```typescript
interface BookingsPageState {
  activeTab: 'table' | 'calendar'
  // Existing state from useBookings hook
}
```

### 2. BookingsTable Component (New)

**Purpose:** Display bookings in a sortable, filterable table format

**Design Pattern:** Based on ResourcesTable component with booking-specific adaptations

**Props Interface:**
```typescript
interface BookingsTableProps {
  bookings: Booking[]
  loading: boolean
  onEdit: (booking: Booking) => void
  onDelete: (bookingId: number) => void
  onGenerateInvoice: (bookingId: number) => void
  onRefresh: () => void
  onStatusFilter: (status: BookingStatus | 'ALL') => void
  onResourceFilter: (resourceId: number | 'ALL') => void
  selectedStatus: BookingStatus | 'ALL'
  selectedResource: number | 'ALL'
}
```

**Table Columns:**
1. Customer Name (sortable)
2. Resources (with badges for multiple resources)
3. Date & Time (sortable, formatted range)
4. Duration (calculated from start/end)
5. Status (with colored badges)
6. Invoice Status (if invoice exists)
7. Created Date (sortable, hidden on mobile)
8. Actions (dropdown menu)

**Sorting Fields:**
- Customer name (alphabetical)
- Booking start date (chronological)
- Creation date (chronological)
- Status (by enum order)

### 3. BookingCard Component (New)

**Purpose:** Mobile-responsive card view for individual bookings

**Design Pattern:** Based on ResourceCard component

**Props Interface:**
```typescript
interface BookingCardProps {
  booking: Booking
  onEdit: (booking: Booking) => void
  onDelete: (bookingId: number) => void
  onGenerateInvoice: (bookingId: number) => void
}
```

**Card Layout:**
```
┌─────────────────────────────────────┐
│ [Customer Icon] Customer Name    [⋮] │
│                 Status Badge         │
│                                     │
│ Resources: Resource1, Resource2     │
│ Date: Jan 15, 2024 10:00-12:00     │
│ Duration: 2h                        │
│ Invoice: [Paid/Pending/None]        │
│ Created: Jan 10, 2024               │
└─────────────────────────────────────┘
```

### 4. Filter Controls

#### Status Filter Component
```typescript
interface StatusFilterProps {
  selectedStatus: BookingStatus | 'ALL'
  onStatusChange: (status: BookingStatus | 'ALL') => void
}
```

**Options:**
- All Statuses
- Pending
- Confirmed
- Cancelled

#### Resource Filter Component
```typescript
interface ResourceFilterProps {
  selectedResource: number | 'ALL'
  onResourceChange: (resourceId: number | 'ALL') => void
  resources: Resource[]
  loading: boolean
}
```

**Behavior:**
- Fetch available resources on component mount
- Display resource names in dropdown
- Handle loading states

#### Date Range Filter Component
```typescript
interface DateRangeFilterProps {
  startDate?: Date
  endDate?: Date
  onDateRangeChange: (startDate?: Date, endDate?: Date) => void
}
```

**Implementation:**
- Use existing date picker components
- Support single date or range selection
- Clear individual dates or entire range

#### Customer Search Component
```typescript
interface CustomerSearchProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  placeholder?: string
}
```

**Features:**
- Debounced search input
- Minimum 3 character requirement
- Search by customer name
- Clear search functionality

### 5. BookingsTableSkeleton Component (New)

**Purpose:** Loading state for table view

**Design Pattern:** Based on ResourcesTableSkeleton

**Features:**
- Skeleton rows matching table structure
- Responsive design (table on desktop, cards on mobile)
- Shimmer animation effects

## Data Models

### Extended Booking Interface

The existing Booking interface from types.ts will be used as-is:

```typescript
interface Booking {
  id: number
  customer: Customer
  customerId: number
  resources: Resource[]
  status: BookingStatus
  start: Date
  end: Date
  invoice?: Invoice | null
  caterings: CateringOnBooking[]
  createdAt: Date
  updatedAt: Date
  createdById?: number | null
  updatedById?: number | null
}
```

### Filter State Interface

```typescript
interface BookingFilters {
  status: BookingStatus | 'ALL'
  resourceId: number | 'ALL'
  startDate?: Date
  endDate?: Date
  searchQuery: string
}
```

### Sort Configuration Interface

```typescript
interface BookingSortConfig {
  field: 'customerName' | 'start' | 'status' | 'createdAt'
  direction: 'asc' | 'desc'
}
```

## Error Handling

### Error Scenarios

1. **Network Errors:** Connection issues, timeouts
2. **Server Errors:** 500, 502, 503 responses
3. **Authentication Errors:** 401, 403 responses
4. **Validation Errors:** Invalid filter parameters
5. **Resource Not Found:** 404 responses

### Error Recovery Strategies

1. **Retry Mechanisms:** Automatic retry with exponential backoff
2. **Fallback States:** Graceful degradation when filters fail
3. **User Feedback:** Clear error messages with actionable suggestions
4. **Offline Support:** Cache last successful data when possible

### Error UI Components

- Alert components for persistent errors
- Toast notifications for transient errors
- Inline error messages for form validation
- Retry buttons with loading states

## Testing Strategy

### Unit Tests

1. **Component Tests:**
   - BookingsTable rendering with different data states
   - BookingCard display and interaction
   - Filter components behavior
   - Sort functionality

2. **Hook Tests:**
   - useBookings hook with new filtering parameters
   - State management for tab switching
   - URL parameter synchronization

3. **Utility Tests:**
   - Date formatting functions
   - Duration calculation
   - Filter parameter serialization

### Integration Tests

1. **Page Level Tests:**
   - Tab switching functionality
   - Filter combinations
   - Search and sort interactions
   - Responsive behavior

2. **API Integration Tests:**
   - Booking data fetching with filters
   - Error handling scenarios
   - Loading state management

### E2E Tests

1. **User Workflows:**
   - Navigate to bookings page
   - Switch between table and calendar views
   - Apply multiple filters
   - Sort bookings by different criteria
   - Perform booking actions (edit, delete, generate invoice)

2. **Responsive Tests:**
   - Mobile card view functionality
   - Desktop table view functionality
   - Touch interactions on mobile

### Accessibility Tests

1. **Keyboard Navigation:**
   - Tab order through filters and table
   - Keyboard shortcuts for common actions
   - Focus management in modals

2. **Screen Reader Tests:**
   - ARIA labels and descriptions
   - Table headers and data associations
   - Filter state announcements

3. **Visual Tests:**
   - Color contrast compliance
   - Focus indicators
   - Text scaling support

## Performance Considerations

### Optimization Strategies

1. **Component Memoization:**
   - Memo for BookingCard components
   - Memo for table rows
   - Callback memoization for event handlers

2. **Data Fetching:**
   - Debounced search queries
   - Cached filter options (resources)
   - Pagination for large datasets

3. **Rendering Optimization:**
   - Virtual scrolling for large tables (if needed)
   - Lazy loading of non-visible content
   - Skeleton loading states

### Bundle Size Considerations

- Reuse existing components where possible
- Tree-shake unused date utilities
- Lazy load calendar view when not active

## Migration Strategy

### Phase 1: Component Creation
- Create new table components
- Implement basic filtering
- Add responsive design

### Phase 2: Integration
- Integrate with existing page
- Add tab functionality
- Preserve existing calendar view

### Phase 3: Enhancement
- Add advanced filtering
- Implement sorting
- Add accessibility features

### Phase 4: Testing & Polish
- Comprehensive testing
- Performance optimization
- User feedback integration

## Browser Support

- Modern browsers (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- Mobile browsers (iOS Safari 14+, Chrome Mobile 90+)
- Responsive design for tablets and mobile devices
- Progressive enhancement for older browsers