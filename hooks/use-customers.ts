"use client";

import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { retryApiCall } from "@/lib/utils/retry";

import { 
  Customer, 
  CustomerFormData, 
  CustomerListResponse, 
  ApiResponse,
  CustomerSearchParams 
} from "@/lib/types";

interface UseCustomersState {
  customers: Customer[];
  loading: boolean;
  error: string | null;
  totalCustomers: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
}

interface UseCustomersActions {
  fetchCustomers: (showLoading?: boolean) => Promise<void>;
  createCustomer: (data: CustomerFormData) => Promise<Customer>;
  updateCustomer: (id: number, data: CustomerFormData) => Promise<Customer>;
  deleteCustomer: (id: number) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refresh: () => void;
  clearError: () => void;
}

interface UseCustomersReturn extends UseCustomersState, UseCustomersActions {}

export function useCustomers(initialParams?: Partial<CustomerSearchParams>): UseCustomersReturn {
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Enhanced toast notification system with better user feedback
  const showToast = useMemo(() => ({
    success: (title: string, description: string, duration?: number) => {
      toast({ 
        title, 
        description,
        duration: duration || 4000,
        className: "border-green-200 bg-green-50 text-green-900"
      });
    },
    error: (title: string, description: string, action?: { label: string; onClick: () => void }) => {
      toast({ 
        variant: "destructive", 
        title, 
        description,
        duration: 6000, // Longer duration for errors
      });
    },
    info: (title: string, description: string, duration?: number) => {
      toast({ 
        title, 
        description,
        duration: duration || 3000,
        className: "border-blue-200 bg-blue-50 text-blue-900"
      });
    },
    warning: (title: string, description: string) => {
      toast({
        title,
        description,
        duration: 5000,
        className: "border-yellow-200 bg-yellow-50 text-yellow-900"
      });
    },
    loading: (title: string, description: string) => {
      return toast({
        title,
        description,
        duration: Infinity, // Keep loading toast until dismissed
        className: "border-gray-200 bg-gray-50 text-gray-900"
      });
    }
  }), [toast]);

  const [state, setState] = useState<UseCustomersState>({
    customers: [],
    loading: true,
    error: null,
    totalCustomers: 0,
    totalPages: 0,
    currentPage: initialParams?.page || 1,
    pageSize: initialParams?.limit || 10,
    searchQuery: initialParams?.search || "",
  });

  // Abort any ongoing requests when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Fetch customers with proper error handling and loading states
  const fetchCustomers = useCallback(async (showLoading = true) => {
    // Abort any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    if (showLoading) {
      setState(prev => ({ ...prev, loading: true, error: null }));
    }

    try {
      const params = new URLSearchParams({
        page: state.currentPage.toString(),
        limit: state.pageSize.toString(),
      });

      if (state.searchQuery.trim()) {
        params.append("search", state.searchQuery.trim());
      }

      const response = await retryApiCall(
        () => fetch(`/api/customers?${params}`, {
          signal: abortControllerRef.current?.signal,
        }),
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.warn(`Retrying fetch customers (attempt ${attempt}):`, error.message);
            
            const isNetworkError = error.message.includes('fetch') || error.message.includes('Network');
            const isServerError = error.message.includes('500') || error.message.includes('502');
            
            let retryMessage = `Attempting to reconnect (${attempt}/3)`;
            if (isNetworkError) {
              retryMessage = `Network issue detected. Retrying connection (${attempt}/3)`;
            } else if (isServerError) {
              retryMessage = `Server error detected. Retrying request (${attempt}/3)`;
            }
            
            showToast.warning("Connection Issue", retryMessage);
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<CustomerListResponse> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch customers");
      }

      const { data: customers, pagination } = result.data;

      setState(prev => ({
        ...prev,
        customers,
        totalCustomers: pagination.total,
        totalPages: pagination.totalPages,
        loading: false,
        error: null,
      }));
    } catch (error) {
      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error("Error fetching customers:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch customers";
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      
      // Enhanced error categorization and user feedback
      const isNetworkError = errorMessage.includes('fetch') || errorMessage.includes('Network') || !navigator.onLine;
      const isServerError = errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503');
      const isAuthError = errorMessage.includes('401') || errorMessage.includes('403');
      
      let title = "Failed to Load Customers";
      let description = "Unable to fetch customer data. Please try again.";
      
      if (isNetworkError) {
        title = "Network Connection Error";
        description = navigator.onLine 
          ? "Unable to connect to the server. Please check your internet connection and try again."
          : "You appear to be offline. Please check your internet connection.";
      } else if (isServerError) {
        title = "Server Error";
        description = "The server is temporarily unavailable. Our team has been notified. Please try again in a few moments.";
      } else if (isAuthError) {
        title = "Authentication Error";
        description = "Your session may have expired. Please refresh the page and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => fetchCustomers(true)
      });
    }
  }, [state.currentPage, state.pageSize, state.searchQuery]); // Remove showToast dependency

  // Create customer with optimistic updates and enhanced feedback
  const createCustomer = useCallback(async (data: CustomerFormData): Promise<Customer> => {
    // Show loading toast
    const loadingToast = showToast.loading(
      "Creating Customer", 
      `Adding "${data.name}" to the system...`
    );

    try {
      const response = await retryApiCall(
        () => fetch("/api/customers", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2, // Less retries for create operations
          onRetry: (error, attempt) => {
            console.warn(`Retrying create customer (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Creation", 
              `Attempting to create "${data.name}" (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Customer> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to create customer");
      }

      const newCustomer = result.data;

      // No cache invalidation needed - data is always fresh

      // Optimistic update - add customer to current list if it would be visible
      setState(prev => {
        const updatedCustomers = [newCustomer, ...prev.customers];
        return {
          ...prev,
          customers: updatedCustomers.slice(0, prev.pageSize), // Keep only current page size
          totalCustomers: prev.totalCustomers + 1,
          totalPages: Math.ceil((prev.totalCustomers + 1) / prev.pageSize),
        };
      });

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast.success(
        "Customer Created Successfully", 
        `"${newCustomer.name}" has been added to the system and is now available for bookings.`,
        5000
      );

      return newCustomer;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      console.error("Error creating customer:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create customer";
      
      // Enhanced error handling with specific messages
      let title = "Failed to Create Customer";
      let description = errorMessage;
      
      if (errorMessage.includes('already exists') || errorMessage.includes('unique')) {
        title = "Duplicate Customer Email";
        description = `A customer with email "${data.email}" already exists. Please use a different email address.`;
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Customer Data";
        description = "Please check the customer information and try again.";
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => createCustomer(data)
      });
      
      throw error;
    }
  }, [showToast]);

  // Update customer with optimistic updates and enhanced feedback
  const updateCustomer = useCallback(async (id: number, data: CustomerFormData): Promise<Customer> => {
    // Store original customer for rollback
    const originalCustomer = state.customers.find(c => c.id === id);
    
    if (!originalCustomer) {
      throw new Error("Customer not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Updating Customer", 
      `Saving changes to "${originalCustomer.name}"...`
    );

    // Optimistic update
    setState(prev => ({
      ...prev,
      customers: prev.customers.map(customer => 
        customer.id === id 
          ? { ...customer, ...data, updatedAt: new Date() }
          : customer
      ),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/customers/${id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying update customer (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Update", 
              `Attempting to save changes to "${originalCustomer.name}" (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Customer> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to update customer");
      }

      const updatedCustomer = result.data;

      // No cache invalidation needed - data is always fresh

      // Update with actual server response
      setState(prev => ({
        ...prev,
        customers: prev.customers.map(customer => 
          customer.id === id ? updatedCustomer : customer
        ),
      }));

      // Dismiss loading toast
      loadingToast.dismiss();

      const nameChanged = originalCustomer.name !== updatedCustomer.name;
      const emailChanged = originalCustomer.email !== updatedCustomer.email;
      
      let changeDescription = "The customer information has been successfully updated.";
      if (nameChanged && emailChanged) {
        changeDescription = `Name changed to "${updatedCustomer.name}" and email updated to "${updatedCustomer.email}".`;
      } else if (nameChanged) {
        changeDescription = `Name changed from "${originalCustomer.name}" to "${updatedCustomer.name}".`;
      } else if (emailChanged) {
        changeDescription = `Email updated to "${updatedCustomer.email}".`;
      }

      showToast.success(
        "Customer Updated Successfully", 
        changeDescription,
        4000
      );

      return updatedCustomer;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      setState(prev => ({
        ...prev,
        customers: prev.customers.map(customer => 
          customer.id === id ? originalCustomer : customer
        ),
      }));

      console.error("Error updating customer:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update customer";
      
      // Enhanced error handling
      let title = "Failed to Update Customer";
      let description = errorMessage;
      
      if (errorMessage.includes('not found')) {
        title = "Customer Not Found";
        description = `The customer "${originalCustomer.name}" no longer exists. It may have been deleted by another user.`;
      } else if (errorMessage.includes('already exists') || errorMessage.includes('unique')) {
        title = "Duplicate Customer Email";
        description = `A customer with email "${data.email}" already exists. Please use a different email address.`;
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Customer Data";
        description = "Please check the customer information and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => updateCustomer(id, data)
      });
      
      throw error;
    }
  }, [state.customers, showToast]);

  // Delete customer with optimistic updates and enhanced feedback
  const deleteCustomer = useCallback(async (id: number): Promise<void> => {
    // Store original customer for rollback
    const originalCustomer = state.customers.find(c => c.id === id);
    const originalIndex = state.customers.findIndex(c => c.id === id);
    
    if (!originalCustomer) {
      throw new Error("Customer not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Deleting Customer", 
      `Removing "${originalCustomer.name}" from the system...`
    );

    // Optimistic update - remove customer from list
    setState(prev => ({
      ...prev,
      customers: prev.customers.filter(customer => customer.id !== id),
      totalCustomers: prev.totalCustomers - 1,
      totalPages: Math.ceil((prev.totalCustomers - 1) / prev.pageSize),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/customers/${id}`, {
          method: "DELETE",
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying delete customer (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Deletion", 
              `Attempting to delete "${originalCustomer.name}" (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to delete customer");
      }

      // No cache invalidation needed - data is always fresh

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast.success(
        "Customer Deleted Successfully", 
        `"${originalCustomer.name}" has been permanently removed from the system.`,
        4000
      );
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      if (originalIndex !== -1) {
        setState(prev => {
          const newCustomers = [...prev.customers];
          newCustomers.splice(originalIndex, 0, originalCustomer);
          return {
            ...prev,
            customers: newCustomers,
            totalCustomers: prev.totalCustomers + 1,
            totalPages: Math.ceil((prev.totalCustomers + 1) / prev.pageSize),
          };
        });
      }

      console.error("Error deleting customer:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete customer";
      
      // Enhanced error handling
      let title = "Failed to Delete Customer";
      let description = errorMessage;
      
      if (errorMessage.includes('associated') || errorMessage.includes('constraint') || errorMessage.includes('P2003')) {
        title = "Cannot Delete Customer";
        description = `"${originalCustomer.name}" cannot be deleted because they have existing bookings. Please remove these bookings first.`;
      } else if (errorMessage.includes('not found')) {
        title = "Customer Not Found";
        description = `The customer "${originalCustomer.name}" no longer exists. It may have already been deleted.`;
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => deleteCustomer(id)
      });
      
      throw error;
    }
  }, [state.customers, showToast]);

  // Enhanced search functionality with better user feedback
  const setSearchQuery = useCallback((query: string) => {
    const trimmedQuery = query.trim();
    
    setState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));

    // Enhanced search feedback
    if (trimmedQuery) {
      if (trimmedQuery.length >= 3) {
        showToast.info(
          "Searching Customers", 
          `Looking for customers matching "${trimmedQuery}"`,
          2000
        );
      } else if (trimmedQuery.length > 0) {
        showToast.info(
          "Search Query Too Short", 
          "Enter at least 3 characters to search",
          2000
        );
      }
    } else {
      // Query cleared
      showToast.info(
        "Search Cleared", 
        "Showing all customers",
        1500
      );
    }
  }, [showToast]);

  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
  }, []);

  const setPageSize = useCallback((size: number) => {
    setState(prev => ({
      ...prev,
      pageSize: size,
      currentPage: 1, // Reset to first page when changing page size
    }));
  }, []);

  // Utility functions
  const refresh = useCallback(() => {
    fetchCustomers(true);
  }, []); // Remove fetchCustomers dependency

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Auto-fetch when dependencies change
  useEffect(() => {
    fetchCustomers();
  }, [state.currentPage, state.pageSize, state.searchQuery]); // Use specific dependencies instead of function

  return {
    // State
    customers: state.customers,
    loading: state.loading,
    error: state.error,
    totalCustomers: state.totalCustomers,
    totalPages: state.totalPages,
    currentPage: state.currentPage,
    pageSize: state.pageSize,
    searchQuery: state.searchQuery,
    
    // Actions
    fetchCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  };
}