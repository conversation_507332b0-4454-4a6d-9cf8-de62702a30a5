"use client";

import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { useDebounce } from "@/hooks/use-debounce";
import { retryApiCall } from "@/lib/utils/retry";
import { 
  Catering, 
  CateringFormData, 
  CateringListResponse, 
  ApiResponse,
  CateringSearchParams,
  CateringWithBookingCount
} from "@/lib/types";

interface UseCateringState {
  catering: Catering[];
  loading: boolean;
  error: string | null;
  totalCatering: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
}

interface UseCateringActions {
  fetchCatering: (showLoading?: boolean) => Promise<void>;
  createCatering: (data: CateringFormData) => Promise<Catering>;
  updateCatering: (id: number, data: CateringFormData) => Promise<Catering>;
  deleteCatering: (id: number) => Promise<void>;
  checkBookingAssociations: (id: number) => Promise<number>;
  setSearchQuery: (query: string) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refresh: () => void;
  clearError: () => void;
}

interface UseCateringReturn extends UseCateringState, UseCateringActions {}

export function useCatering(initialParams?: Partial<CateringSearchParams>): UseCateringReturn {
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Enhanced toast notification system with better user feedback
  const showToast = useMemo(() => ({
    success: (title: string, description: string, duration?: number) => {
      toast({ 
        title, 
        description,
        duration: duration || 4000,
        className: "border-green-200 bg-green-50 text-green-900"
      });
    },
    error: (title: string, description: string, action?: { label: string; onClick: () => void }) => {
      toast({ 
        variant: "destructive", 
        title, 
        description,
        duration: 6000, // Longer duration for errors
      });
      
      // Note: Custom action buttons in toast are not supported in this toast implementation
      // The action parameter is kept for future enhancement
    },
    info: (title: string, description: string, duration?: number) => {
      toast({ 
        title, 
        description,
        duration: duration || 3000,
        className: "border-blue-200 bg-blue-50 text-blue-900"
      });
    },
    warning: (title: string, description: string) => {
      toast({
        title,
        description,
        duration: 5000,
        className: "border-yellow-200 bg-yellow-50 text-yellow-900"
      });
    },
    loading: (title: string, description: string) => {
      return toast({
        title,
        description,
        duration: Infinity, // Keep loading toast until dismissed
        className: "border-gray-200 bg-gray-50 text-gray-900"
      });
    }
  }), [toast]);

  const [state, setState] = useState<UseCateringState>({
    catering: [],
    loading: true,
    error: null,
    totalCatering: 0,
    totalPages: 0,
    currentPage: initialParams?.page || 1,
    pageSize: initialParams?.limit || 10,
    searchQuery: initialParams?.search || "",
  });

  // Debounce search query to avoid excessive API calls
  const debouncedSearchQuery = useDebounce(state.searchQuery, 300);

  // Abort any ongoing requests when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Fetch catering with proper error handling and loading states
  const fetchCatering = useCallback(async (showLoading = true) => {
    // Abort any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    if (showLoading) {
      setState(prev => ({ ...prev, loading: true, error: null }));
    }

    try {
      const params = new URLSearchParams({
        page: state.currentPage.toString(),
        limit: state.pageSize.toString(),
      });

      if (debouncedSearchQuery.trim()) {
        params.append("search", debouncedSearchQuery.trim());
      }

      const response = await retryApiCall(
        () => fetch(`/api/catering?${params}`, {
          signal: abortControllerRef.current?.signal,
        }),
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.warn(`Retrying fetch catering (attempt ${attempt}):`, error.message);
            
            const isNetworkError = error.message.includes('fetch') || error.message.includes('Network');
            const isServerError = error.message.includes('500') || error.message.includes('502');
            
            let retryMessage = `Attempting to reconnect (${attempt}/3)`;
            if (isNetworkError) {
              retryMessage = `Network issue detected. Retrying connection (${attempt}/3)`;
            } else if (isServerError) {
              retryMessage = `Server error detected. Retrying request (${attempt}/3)`;
            }
            
            showToast.warning("Connection Issue", retryMessage);
          },
        }
      );

      const result: ApiResponse<CateringListResponse> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch catering offers");
      }

      const { data: catering, pagination } = result.data;

      setState(prev => ({
        ...prev,
        catering,
        totalCatering: pagination.total,
        totalPages: pagination.totalPages,
        loading: false,
        error: null,
      }));
    } catch (error) {
      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error("Error fetching catering:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch catering offers";
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      
      // Enhanced error categorization and user feedback
      const isNetworkError = errorMessage.includes('fetch') || errorMessage.includes('Network') || !navigator.onLine;
      const isServerError = errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503');
      const isAuthError = errorMessage.includes('401') || errorMessage.includes('403');
      
      let title = "Failed to Load Catering Offers";
      let description = "Unable to fetch catering data. Please try again.";
      
      if (isNetworkError) {
        title = "Network Connection Error";
        description = navigator.onLine 
          ? "Unable to connect to the server. Please check your internet connection and try again."
          : "You appear to be offline. Please check your internet connection.";
      } else if (isServerError) {
        title = "Server Error";
        description = "The server is temporarily unavailable. Our team has been notified. Please try again in a few moments.";
      } else if (isAuthError) {
        title = "Authentication Error";
        description = "Your session may have expired. Please refresh the page and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => fetchCatering(true)
      });
    }
  }, [state.currentPage, state.pageSize, debouncedSearchQuery]); // Remove showToast dependency

  // Create catering with optimistic updates and enhanced feedback
  const createCatering = useCallback(async (data: CateringFormData): Promise<Catering> => {
    // Show loading toast
    const loadingToast = showToast.loading(
      "Creating Catering Offer", 
      `Adding "${data.offerName}" to the system...`
    );

    try {
      const response = await retryApiCall(
        () => fetch("/api/catering", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2, // Less retries for create operations
          onRetry: (error, attempt) => {
            console.warn(`Retrying create catering (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Creation", 
              `Attempting to create "${data.offerName}" (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Catering> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to create catering offer");
      }

      const newCatering = result.data;

      // Optimistic update - add catering to current list if it would be visible
      setState(prev => {
        const updatedCatering = [newCatering, ...prev.catering];
        return {
          ...prev,
          catering: updatedCatering.slice(0, prev.pageSize), // Keep only current page size
          totalCatering: prev.totalCatering + 1,
          totalPages: Math.ceil((prev.totalCatering + 1) / prev.pageSize),
        };
      });

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast.success(
        "Catering Offer Created Successfully", 
        `"${newCatering.offerName}" has been added to the system and is now available for bookings.`,
        5000
      );

      return newCatering;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      console.error("Error creating catering:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create catering offer";
      
      // Enhanced error handling with specific messages
      let title = "Failed to Create Catering Offer";
      let description = errorMessage;
      
      if (errorMessage.includes('already exists') || errorMessage.includes('unique')) {
        title = "Duplicate Catering Offer Name";
        description = `A catering offer named "${data.offerName}" already exists. Please choose a different name.`;
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Catering Data";
        description = "Please check the catering information and try again. Ensure revenue shares add up to 100%.";
      } else if (errorMessage.includes('revenue') || errorMessage.includes('share')) {
        title = "Revenue Sharing Error";
        description = "First party share and vendor share must add up to exactly 100%. Please adjust the values.";
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => createCatering(data)
      });
      
      throw error;
    }
  }, [showToast]);

  // Update catering with optimistic updates and enhanced feedback
  const updateCatering = useCallback(async (id: number, data: CateringFormData): Promise<Catering> => {
    // Store original catering for rollback
    const originalCatering = state.catering.find(c => c.id === id);
    
    if (!originalCatering) {
      throw new Error("Catering offer not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Updating Catering Offer", 
      `Saving changes to "${originalCatering.offerName}"...`
    );

    // Optimistic update
    setState(prev => ({
      ...prev,
      catering: prev.catering.map(catering => 
        catering.id === id 
          ? { ...catering, ...data, updatedAt: new Date() }
          : catering
      ),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/catering/${id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying update catering (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Update", 
              `Attempting to save changes to "${originalCatering.offerName}" (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Catering> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to update catering offer");
      }

      const updatedCatering = result.data;

      // Update with actual server response
      setState(prev => ({
        ...prev,
        catering: prev.catering.map(catering => 
          catering.id === id ? updatedCatering : catering
        ),
      }));

      // Dismiss loading toast
      loadingToast.dismiss();

      const nameChanged = originalCatering.offerName !== updatedCatering.offerName;
      const priceChanged = originalCatering.pricePerPerson !== updatedCatering.pricePerPerson;
      const sharingChanged = originalCatering.firstPartyShare !== updatedCatering.firstPartyShare || 
                            originalCatering.vendorShare !== updatedCatering.vendorShare;
      
      let changeDescription = "The catering offer has been successfully updated.";
      if (nameChanged && priceChanged && sharingChanged) {
        changeDescription = `Name, pricing, and revenue sharing updated for "${updatedCatering.offerName}".`;
      } else if (nameChanged) {
        changeDescription = `Name changed from "${originalCatering.offerName}" to "${updatedCatering.offerName}".`;
      } else if (priceChanged) {
        changeDescription = `Price per person updated for "${updatedCatering.offerName}".`;
      } else if (sharingChanged) {
        changeDescription = `Revenue sharing updated for "${updatedCatering.offerName}".`;
      }

      showToast.success(
        "Catering Offer Updated Successfully", 
        changeDescription,
        4000
      );

      return updatedCatering;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      setState(prev => ({
        ...prev,
        catering: prev.catering.map(catering => 
          catering.id === id ? originalCatering : catering
        ),
      }));

      console.error("Error updating catering:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update catering offer";
      
      // Enhanced error handling
      let title = "Failed to Update Catering Offer";
      let description = errorMessage;
      
      if (errorMessage.includes('not found')) {
        title = "Catering Offer Not Found";
        description = `The catering offer "${originalCatering.offerName}" no longer exists. It may have been deleted by another user.`;
      } else if (errorMessage.includes('already exists') || errorMessage.includes('unique')) {
        title = "Duplicate Catering Offer Name";
        description = `A catering offer named "${data.offerName}" already exists. Please choose a different name.`;
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Catering Data";
        description = "Please check the catering information and try again. Ensure revenue shares add up to 100%.";
      } else if (errorMessage.includes('revenue') || errorMessage.includes('share')) {
        title = "Revenue Sharing Error";
        description = "First party share and vendor share must add up to exactly 100%. Please adjust the values.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => updateCatering(id, data)
      });
      
      throw error;
    }
  }, [state.catering, showToast]);

  // Check booking associations for a catering offer
  const checkBookingAssociations = useCallback(async (id: number): Promise<number> => {
    try {
      const response = await fetch(`/api/catering/${id}/bookings`);
      const result: ApiResponse<{ count: number }> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to check booking associations");
      }

      return result.data.count;
    } catch (error) {
      console.error("Error checking booking associations:", error);
      // Return 0 if we can't check, allowing deletion attempt
      return 0;
    }
  }, []);

  // Delete catering with optimistic updates and enhanced feedback
  const deleteCatering = useCallback(async (id: number): Promise<void> => {
    // Store original catering for rollback
    const originalCatering = state.catering.find(c => c.id === id);
    const originalIndex = state.catering.findIndex(c => c.id === id);
    
    if (!originalCatering) {
      throw new Error("Catering offer not found in current list");
    }

    // Check for booking associations first
    const bookingCount = await checkBookingAssociations(id);
    if (bookingCount > 0) {
      showToast.error(
        "Cannot Delete Catering Offer",
        `"${originalCatering.offerName}" cannot be deleted because it is associated with ${bookingCount} booking(s). Please remove these associations first.`
      );
      throw new Error(`Catering offer has ${bookingCount} associated bookings`);
    }

    // Show loading toast
    const loadingToast = showToast.loading(
      "Deleting Catering Offer", 
      `Removing "${originalCatering.offerName}" from the system...`
    );

    // Optimistic update - remove catering from list
    setState(prev => ({
      ...prev,
      catering: prev.catering.filter(catering => catering.id !== id),
      totalCatering: prev.totalCatering - 1,
      totalPages: Math.ceil((prev.totalCatering - 1) / prev.pageSize),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/catering/${id}`, {
          method: "DELETE",
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying delete catering (attempt ${attempt}):`, error.message);
            showToast.warning(
              "Retrying Deletion", 
              `Attempting to delete "${originalCatering.offerName}" (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<void> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to delete catering offer");
      }

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast.success(
        "Catering Offer Deleted Successfully", 
        `"${originalCatering.offerName}" has been permanently removed from the system.`,
        4000
      );
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      if (originalIndex !== -1) {
        setState(prev => {
          const newCatering = [...prev.catering];
          newCatering.splice(originalIndex, 0, originalCatering);
          return {
            ...prev,
            catering: newCatering,
            totalCatering: prev.totalCatering + 1,
            totalPages: Math.ceil((prev.totalCatering + 1) / prev.pageSize),
          };
        });
      }

      console.error("Error deleting catering:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete catering offer";
      
      // Enhanced error handling
      let title = "Failed to Delete Catering Offer";
      let description = errorMessage;
      
      if (errorMessage.includes('associated') || errorMessage.includes('constraint') || errorMessage.includes('P2003')) {
        title = "Cannot Delete Catering Offer";
        description = `"${originalCatering.offerName}" cannot be deleted because it is currently associated with one or more bookings. Please remove these associations first.`;
      } else if (errorMessage.includes('not found')) {
        title = "Catering Offer Not Found";
        description = `The catering offer "${originalCatering.offerName}" no longer exists. It may have already been deleted.`;
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast.error(title, description, {
        label: "Retry",
        onClick: () => deleteCatering(id)
      });
      
      throw error;
    }
  }, [state.catering, showToast, checkBookingAssociations]);

  // Enhanced search functionality with better user feedback
  const setSearchQuery = useCallback((query: string) => {
    const trimmedQuery = query.trim();
    
    setState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));

    // Enhanced search feedback
    if (trimmedQuery) {
      if (trimmedQuery.length >= 3) {
        showToast.info(
          "Searching Catering Offers", 
          `Looking for catering offers matching "${trimmedQuery}"`,
          2000
        );
      } else if (trimmedQuery.length > 0) {
        showToast.info(
          "Search Query Too Short", 
          "Enter at least 3 characters to search",
          2000
        );
      }
    } else {
      // Query cleared
      showToast.info(
        "Search Cleared", 
        "Showing all catering offers",
        1500
      );
    }
  }, [showToast]);

  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
  }, []);

  const setPageSize = useCallback((size: number) => {
    setState(prev => ({
      ...prev,
      pageSize: size,
      currentPage: 1, // Reset to first page when changing page size
    }));
  }, []);

  // Utility functions
  const refresh = useCallback(() => {
    fetchCatering(true);
  }, [fetchCatering]); // Include fetchCatering dependency

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Auto-fetch when dependencies change
  useEffect(() => {
    fetchCatering();
  }, [state.currentPage, state.pageSize, debouncedSearchQuery]); // Use specific dependencies instead of function

  return {
    // State
    catering: state.catering,
    loading: state.loading,
    error: state.error,
    totalCatering: state.totalCatering,
    totalPages: state.totalPages,
    currentPage: state.currentPage,
    pageSize: state.pageSize,
    searchQuery: state.searchQuery,
    
    // Actions
    fetchCatering,
    createCatering,
    updateCatering,
    deleteCatering,
    checkBookingAssociations,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  };
}