"use client";

import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { retryApiCall } from "@/lib/utils/retry";
import { enhancedRetry, analyzeError, type ErrorAnalysis } from "@/lib/utils/enhanced-retry";

import { 
  Booking, 
  BookingFormData, 
  BookingListResponse, 
  CalendarEvent,
  ApiResponse,
  BookingSearchParams,
  BookingStatus 
} from "@/lib/types";

interface UseBookingsState {
  bookings: Booking[];
  calendarEvents: CalendarEvent[];
  loading: boolean;
  error: string | null;
  errorAnalysis: ErrorAnalysis | null;
  totalBookings: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  searchQuery: string;
  selectedCustomerId?: number;
  selectedStatus?: BookingStatus;
  selectedResourceId?: number;
  selectedInvoiceStatus?: string;
  startDate?: Date;
  endDate?: Date;
  retryCount: number;
  isRetrying: boolean;
}

interface UseBookingsActions {
  fetchBookings: (showLoading?: boolean) => Promise<void>;
  fetchCalendarEvents: (startDate?: Date, endDate?: Date) => Promise<void>;
  createBooking: (data: BookingFormData) => Promise<Booking>;
  updateBooking: (id: number, data: BookingFormData) => Promise<Booking>;
  deleteBooking: (id: number) => Promise<void>;
  generateInvoice: (bookingId: number) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setCustomerFilter: (customerId?: number) => void;
  setStatusFilter: (status?: BookingStatus) => void;
  setResourceFilter: (resourceId?: number) => void;
  setDateRange: (startDate?: Date, endDate?: Date) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refresh: () => void;
  refreshCalendar: () => void;
  clearError: () => void;
  clearFilters: () => void;
}

interface UseBookingsReturn extends UseBookingsState, UseBookingsActions {}

export function useBookings(
  initialParams?: Partial<BookingSearchParams>,
  externalFilters?: {
    searchQuery?: string;
    selectedStatus?: BookingStatus;
    selectedResourceId?: number;
    selectedInvoiceStatus?: string;
    startDate?: Date;
    endDate?: Date;
  }
): UseBookingsReturn {
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);
  const calendarAbortControllerRef = useRef<AbortController | null>(null);

  // Enhanced toast notification system with better user feedback
  const showToast = useMemo(() => {
    const api = {
      success: (title: string, description: string, duration?: number) => {
        toast({ 
          title, 
          description,
          duration: duration || 4000,
          className: "border-green-200 bg-green-50 text-green-900"
        });
      },
      error: (title: string, description: string, action?: { label: string; onClick: () => void }) => {
        toast({ 
          variant: "destructive", 
          title, 
          description,
          duration: 6000, // Longer duration for errors
        });
      },
      info: (title: string, description: string, duration?: number) => {
        toast({ 
          title, 
          description,
          duration: duration || 3000,
          className: "border-blue-200 bg-blue-50 text-blue-900"
        });
      },
      warning: (title: string, description: string) => {
        toast({
          title,
          description,
          duration: 5000,
          className: "border-yellow-200 bg-yellow-50 text-yellow-900"
        });
      },
      loading: (title: string, description: string) => {
        return toast({
          title,
          description,
          duration: Infinity, // Keep loading toast until dismissed
          className: "border-gray-200 bg-gray-50 text-gray-900"
        });
      }
    };
    const fn = () => api;
    return Object.assign(fn, api) as typeof api & (() => typeof api);
  }, [toast]);

  const [state, setState] = useState<UseBookingsState>({
    bookings: [],
    calendarEvents: [],
    loading: true,
    error: null,
    errorAnalysis: null,
    totalBookings: 0,
    totalPages: 0,
    currentPage: initialParams?.page || 1,
    pageSize: initialParams?.limit || 10,
    searchQuery: externalFilters?.searchQuery || initialParams?.search || "",
    selectedCustomerId: initialParams?.customerId,
    selectedStatus: externalFilters?.selectedStatus || initialParams?.status,
    selectedResourceId: externalFilters?.selectedResourceId || initialParams?.resourceId,
    selectedInvoiceStatus: externalFilters?.selectedInvoiceStatus,
    startDate: externalFilters?.startDate || initialParams?.startDate,
    endDate: externalFilters?.endDate || initialParams?.endDate,
    retryCount: 0,
    isRetrying: false,
  });

  // Sync external filters with internal state
  useEffect(() => {
    if (externalFilters) {
      setState(prev => ({
        ...prev,
        searchQuery: externalFilters.searchQuery ?? prev.searchQuery,
        selectedStatus: externalFilters.selectedStatus ?? prev.selectedStatus,
        selectedResourceId: externalFilters.selectedResourceId ?? prev.selectedResourceId,
        selectedInvoiceStatus: externalFilters.selectedInvoiceStatus ?? prev.selectedInvoiceStatus,
        startDate: externalFilters.startDate ?? prev.startDate,
        endDate: externalFilters.endDate ?? prev.endDate,
        currentPage: 1, // Reset pagination when filters change
      }));
    }
  }, [externalFilters?.searchQuery, externalFilters?.selectedStatus, externalFilters?.selectedResourceId, externalFilters?.selectedInvoiceStatus, externalFilters?.startDate, externalFilters?.endDate]);

  // Abort any ongoing requests when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (calendarAbortControllerRef.current) {
        calendarAbortControllerRef.current.abort();
      }
    };
  }, []);

  // Enhanced fetch bookings with comprehensive error handling and recovery
  const fetchBookings = useCallback(async (showLoading = true) => {
    // Abort any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    if (showLoading) {
      setState(prev => ({ 
        ...prev, 
        loading: true, 
        error: null, 
        errorAnalysis: null,
        isRetrying: state.retryCount > 0 
      }));
    }

    try {
      const params = new URLSearchParams({
        page: state.currentPage.toString(),
        limit: state.pageSize.toString(),
      });

      if (state.searchQuery.trim()) {
        params.append("search", state.searchQuery.trim());
      }

      if (state.selectedCustomerId) {
        params.append("customerId", state.selectedCustomerId.toString());
      }

      if (state.selectedStatus) {
        params.append("status", state.selectedStatus);
      }

      if (state.selectedResourceId) {
        params.append("resourceId", state.selectedResourceId.toString());
      }

      if (state.selectedInvoiceStatus) {
        params.append("invoiceStatus", state.selectedInvoiceStatus);
      }

      if (state.startDate) {
        params.append("startDate", state.startDate.toISOString());
      }

      if (state.endDate) {
        params.append("endDate", state.endDate.toISOString());
      }

      const result = await enhancedRetry(
        async () => {
          const response = await fetch(`/api/bookings?${params}`, {
            signal: abortControllerRef.current?.signal,
          });
          
          if (!response.ok) {
            const errorText = await response.text().catch(() => 'Unknown error');
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }
          
          return response;
        },
        {
          maxAttempts: 3,
          baseDelay: 1000,
          maxDelay: 10000,
          abortSignal: abortControllerRef.current?.signal,
          onRetry: (error, attempt, delay) => {
            setState(prev => ({ ...prev, retryCount: attempt }));
            
            const errorAnalysis = analyzeError(error);
            console.warn(`Retrying fetch bookings (attempt ${attempt}):`, error.message);
            
            showToast().warning(
              "Retrying Request", 
              `${errorAnalysis.userMessage} Attempting retry ${attempt}/3...`
            );
          },
        },
        'bookings-fetch' // Circuit breaker key
      );

      if (!result.success) {
        throw result.error || new Error('Failed to fetch bookings');
      }

      const response = result.data!;

      const apiResult: ApiResponse<BookingListResponse> = await response.json();
      
      if (!apiResult.success || !apiResult.data) {
        throw new Error(apiResult.error || "Failed to fetch bookings");
      }

      const { data: bookings, pagination } = apiResult.data;

      setState(prev => ({
        ...prev,
        bookings,
        totalBookings: pagination.total,
        totalPages: pagination.totalPages,
        loading: false,
        error: null,
        errorAnalysis: null,
        retryCount: 0,
        isRetrying: false,
      }));
    } catch (error) {
      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error("Error fetching bookings:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch bookings";
      const errorAnalysis = analyzeError(errorMessage);
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        errorAnalysis,
        isRetrying: false,
      }));
      
      // Show enhanced error notification with contextual information
      showToast().error(
        "Failed to Load Bookings", 
        errorAnalysis.userMessage,
        {
          label: errorAnalysis.retryable ? "Retry" : "Refresh",
          onClick: () => {
            setState(prev => ({ ...prev, retryCount: prev.retryCount + 1 }));
            fetchBookings(true);
          }
        }
      );
    }
  }, [state.currentPage, state.pageSize, state.searchQuery, state.selectedCustomerId, state.selectedStatus, state.selectedResourceId, state.selectedInvoiceStatus, state.startDate, state.endDate]); // Remove showToast dependency

  // Fetch calendar events with specific date range
  const fetchCalendarEvents = useCallback(async (startDate?: Date, endDate?: Date) => {
    // Abort any ongoing calendar request
    if (calendarAbortControllerRef.current) {
      calendarAbortControllerRef.current.abort();
    }

    calendarAbortControllerRef.current = new AbortController();

    try {
      const params = new URLSearchParams({
        format: 'calendar',
      });

      if (startDate) {
        params.append("startDate", startDate.toISOString());
      }

      if (endDate) {
        params.append("endDate", endDate.toISOString());
      }

      const response = await retryApiCall(
        () => fetch(`/api/bookings?${params}`, {
          signal: calendarAbortControllerRef.current?.signal,
        }),
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.warn(`Retrying fetch calendar events (attempt ${attempt}):`, error.message);
            showToast().warning("Loading Calendar", `Retrying calendar data (${attempt}/3)`);
          },
        }
      );

      const result: ApiResponse<CalendarEvent[]> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch calendar events");
      }

      setState(prev => ({
        ...prev,
        calendarEvents: result.data || [],
      }));
    } catch (error) {
      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error("Error fetching calendar events:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch calendar events";
      
      showToast().error(
        "Calendar Loading Failed", 
        "Unable to load calendar events. Please refresh the calendar.",
        {
          label: "Retry",
          onClick: () => fetchCalendarEvents(startDate, endDate)
        }
      );
    }
  }, []); // Remove showToast dependency

  // Create booking with optimistic updates and enhanced feedback
  const createBooking = useCallback(async (data: BookingFormData): Promise<Booking> => {
    // Show loading toast
    const loadingToast = showToast().loading(
      "Creating Booking", 
      "Setting up your booking..."
    );

    try {
      const response = await retryApiCall(
        () => fetch("/api/bookings", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2, // Less retries for create operations
          onRetry: (error, attempt) => {
            console.warn(`Retrying create booking (attempt ${attempt}):`, error.message);
            showToast().warning(
              "Retrying Creation", 
              `Attempting to create booking (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Booking> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to create booking");
      }

      const newBooking = result.data;

      // Optimistic update - add booking to current list if it would be visible
      setState(prev => {
        const updatedBookings = [newBooking, ...prev.bookings];
        return {
          ...prev,
          bookings: updatedBookings.slice(0, prev.pageSize), // Keep only current page size
          totalBookings: prev.totalBookings + 1,
          totalPages: Math.ceil((prev.totalBookings + 1) / prev.pageSize),
        };
      });

      // Refresh calendar events to show the new booking
      fetchCalendarEvents(state.startDate, state.endDate);

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast().success(
        "Booking Created Successfully", 
        `Booking for "${newBooking.customer.name}" has been created and is now visible on the calendar.`,
        5000
      );

      return newBooking;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      console.error("Error creating booking:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create booking";
      
      // Enhanced error handling with specific messages
      let title = "Failed to Create Booking";
      let description = errorMessage;
      
      if (errorMessage.includes('conflict') || errorMessage.includes('Resource conflict')) {
        title = "Resource Conflict";
        description = "One or more selected resources are already booked for the specified time. Please choose different resources or time.";
      } else if (errorMessage.includes('not found')) {
        title = "Invalid Selection";
        description = "The selected customer, resources, or catering items are no longer available. Please refresh and try again.";
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Booking Data";
        description = "Please check the booking information and try again.";
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast().error(title, description, {
        label: "Retry",
        onClick: () => createBooking(data)
      });
      
      throw error;
    }
  }, [state.startDate, state.endDate]); // Remove fetchCalendarEvents dependency

  // Update booking with optimistic updates and enhanced feedback
  const updateBooking = useCallback(async (id: number, data: BookingFormData): Promise<Booking> => {
    // Store original booking for rollback (if present in in-memory list)
    const originalBooking = state.bookings.find(b => b.id === id);

    // Show loading toast
    const loadingToast = showToast().loading(
      "Updating Booking", 
      originalBooking
        ? `Saving changes to booking for "${originalBooking.customer.name}"...`
        : "Saving changes to booking..."
    );

    // Optimistic update only if booking exists in memory
    if (originalBooking) {
      setState(prev => ({
        ...prev,
        bookings: prev.bookings.map(booking => 
          booking.id === id 
            ? { ...booking, updatedAt: new Date() }
            : booking
        ),
      }));
    }

    try {
      const response = await retryApiCall(
        () => fetch(`/api/bookings/${id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying update booking (attempt ${attempt}):`, error.message);
            showToast().warning(
              "Retrying Update", 
              `Attempting to save changes (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse<Booking> = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to update booking");
      }

      const updatedBooking = result.data;

      // Update with actual server response (replace if found, else leave list unchanged)
      setState(prev => ({
        ...prev,
        bookings: prev.bookings.some(b => b.id === id)
          ? prev.bookings.map(booking => (booking.id === id ? updatedBooking : booking))
          : prev.bookings,
      }));

      // Refresh calendar events to show the updated booking
      fetchCalendarEvents(state.startDate, state.endDate);

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast().success(
        "Booking Updated Successfully", 
        `The booking for "${updatedBooking.customer.name}" has been successfully updated.`,
        4000
      );

      return updatedBooking;
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update if any
      if (originalBooking) {
        setState(prev => ({
          ...prev,
          bookings: prev.bookings.map(booking => 
            booking.id === id ? originalBooking : booking
          ),
        }));
      }

      console.error("Error updating booking:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update booking";
      
      // Enhanced error handling
      let title = "Failed to Update Booking";
      let description = errorMessage;
      
      if (errorMessage.includes('not found')) {
        title = "Booking Not Found";
        description = `The booking no longer exists. It may have been deleted by another user.`;
      } else if (errorMessage.includes('conflict') || errorMessage.includes('Resource conflict')) {
        title = "Resource Conflict";
        description = "One or more selected resources are already booked for the specified time. Please choose different resources or time.";
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Booking Data";
        description = "Please check the booking information and try again.";
      }
      
      showToast().error(title, description, {
        label: "Retry",
        onClick: () => updateBooking(id, data)
      });
      
      throw error;
    }
  }, [state.bookings, state.startDate, state.endDate]); // Remove fetchCalendarEvents dependency

  // Delete booking with optimistic updates and enhanced feedback
  const deleteBooking = useCallback(async (id: number): Promise<void> => {
    // Store original booking for rollback
    const originalBooking = state.bookings.find(b => b.id === id);
    const originalIndex = state.bookings.findIndex(b => b.id === id);
    
    if (!originalBooking) {
      throw new Error("Booking not found in current list");
    }

    // Show loading toast
    const loadingToast = showToast().loading(
      "Deleting Booking", 
      `Removing booking for "${originalBooking.customer.name}"...`
    );

    // Optimistic update - remove booking from list
    setState(prev => ({
      ...prev,
      bookings: prev.bookings.filter(booking => booking.id !== id),
      totalBookings: prev.totalBookings - 1,
      totalPages: Math.ceil((prev.totalBookings - 1) / prev.pageSize),
    }));

    try {
      const response = await retryApiCall(
        () => fetch(`/api/bookings/${id}`, {
          method: "DELETE",
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying delete booking (attempt ${attempt}):`, error.message);
            showToast().warning(
              "Retrying Deletion", 
              `Attempting to delete booking (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to delete booking");
      }

      // Refresh calendar events to remove the deleted booking
      fetchCalendarEvents(state.startDate, state.endDate);

      // Dismiss loading toast
      loadingToast.dismiss();

      showToast().success(
        "Booking Deleted Successfully", 
        `The booking for "${originalBooking.customer.name}" has been permanently removed.`,
        4000
      );
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      // Rollback optimistic update
      if (originalIndex !== -1) {
        setState(prev => {
          const newBookings = [...prev.bookings];
          newBookings.splice(originalIndex, 0, originalBooking);
          return {
            ...prev,
            bookings: newBookings,
            totalBookings: prev.totalBookings + 1,
            totalPages: Math.ceil((prev.totalBookings + 1) / prev.pageSize),
          };
        });
      }

      console.error("Error deleting booking:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete booking";
      
      // Enhanced error handling
      let title = "Failed to Delete Booking";
      let description = errorMessage;
      
      if (errorMessage.includes('associated') || errorMessage.includes('constraint') || errorMessage.includes('P2003')) {
        title = "Cannot Delete Booking";
        description = `This booking cannot be deleted because it has an associated invoice. Please remove the invoice first.`;
      } else if (errorMessage.includes('not found')) {
        title = "Booking Not Found";
        description = `The booking no longer exists. It may have already been deleted.`;
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        title = "Network Error";
        description = "Unable to connect to the server. Please check your connection and try again.";
      }
      
      showToast().error(title, description, {
        label: "Retry",
        onClick: () => deleteBooking(id)
      });
      
      throw error;
    }
  }, [state.bookings, state.startDate, state.endDate]); // Remove fetchCalendarEvents dependency

  // Generate invoice from booking
  const generateInvoice = useCallback(async (bookingId: number): Promise<void> => {
    const booking = state.bookings.find(b => b.id === bookingId);
    const bookingCustomerName = booking?.customer.name || "Unknown Customer";
    const isPending = booking?.status === 'PENDING';

    // Show loading toast
    const loadingToast = showToast().loading(
      isPending ? "Confirming Booking & Generating Invoice" : "Generating Invoice", 
      isPending 
        ? `Confirming booking and creating invoice for "${bookingCustomerName}"...`
        : `Creating invoice for "${bookingCustomerName}"...`
    );

    try {
      const response = await retryApiCall(
        () => fetch(`/api/bookings/${bookingId}/invoice`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            autoConfirm: isPending // Tell the API to auto-confirm if pending
          }),
        }),
        {
          maxAttempts: 2,
          onRetry: (error, attempt) => {
            console.warn(`Retrying generate invoice (attempt ${attempt}):`, error.message);
            showToast().warning(
              "Retrying Invoice Generation", 
              `Attempting to create invoice (${attempt}/2)`
            );
          },
        }
      );

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to generate invoice");
      }

      // Update the booking to reflect that it now has an invoice and potentially updated status
      setState(prev => ({
        ...prev,
        bookings: prev.bookings.map(booking => 
          booking.id === bookingId 
            ? { 
                ...booking, 
                invoice: result.data.invoice,
                status: result.data.booking?.status || booking.status // Update status if changed
              }
            : booking
        ),
      }));

      // Refresh the bookings list to ensure we have the latest data
      fetchBookings(false);

      // Dismiss loading toast
      loadingToast.dismiss();

      if (isPending) {
        showToast().success(
          "Booking Confirmed & Invoice Generated", 
          `Booking for "${bookingCustomerName}" has been confirmed and invoice created. You can now manage payments.`,
          6000
        );
      } else {
        showToast().success(
          "Invoice Generated Successfully", 
          `Invoice has been created for "${bookingCustomerName}". You can now manage payments.`,
          5000
        );
      }
    } catch (error) {
      // Dismiss loading toast
      loadingToast.dismiss();
      
      console.error("Error generating invoice:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to generate invoice";
      
      // Enhanced error handling
      let title = "Failed to Generate Invoice";
      let description = errorMessage;
      
      if (errorMessage.includes('already exists') || errorMessage.includes('duplicate')) {
        title = "Invoice Already Exists";
        description = `An invoice for this booking already exists. Please check the booking details.`;
      } else if (errorMessage.includes('not found')) {
        title = "Booking Not Found";
        description = `The booking no longer exists. It may have been deleted.`;
      } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        title = "Invalid Booking Data";
        description = "The booking data is incomplete. Please ensure all required information is provided.";
      } else if (errorMessage.includes('cancelled') || errorMessage.includes('CANCELLED')) {
        title = "Cannot Generate Invoice";
        description = "Invoices cannot be generated for cancelled bookings.";
      }
      
      showToast().error(title, description, {
        label: "Retry",
        onClick: () => generateInvoice(bookingId)
      });
      
      throw error;
    }
  }, [state.bookings]); // Keep state.bookings dependency as it's needed

  // Enhanced search functionality with better user feedback
  const setSearchQuery = useCallback((query: string) => {
    const trimmedQuery = query.trim();
    
    setState(prev => ({
      ...prev,
      searchQuery: query,
      currentPage: 1, // Reset to first page when searching
    }));

    // Enhanced search feedback
    if (trimmedQuery) {
      if (trimmedQuery.length >= 3) {
        showToast().info(
          "Searching Bookings", 
          `Looking for bookings matching "${trimmedQuery}"`,
          2000
        );
      } else if (trimmedQuery.length > 0) {
        showToast().info(
          "Search Query Too Short", 
          "Enter at least 3 characters to search",
          2000
        );
      }
    } else {
      // Query cleared
      showToast().info(
        "Search Cleared", 
        "Showing all bookings",
        1500
      );
    }
  }, []); // Remove showToast dependency

  // Filter by customer
  const setCustomerFilter = useCallback((customerId?: number) => {
    setState(prev => ({
      ...prev,
      selectedCustomerId: customerId,
      currentPage: 1, // Reset to first page when filtering
    }));

    if (customerId) {
      showToast().info("Filter Applied", "Showing bookings for selected customer", 2000);
    } else {
      showToast().info("Filter Cleared", "Showing bookings for all customers", 1500);
    }
  }, []); // Remove showToast dependency

  // Filter by status
  const setStatusFilter = useCallback((status?: BookingStatus) => {
    setState(prev => ({
      ...prev,
      selectedStatus: status,
      currentPage: 1, // Reset to first page when filtering
    }));

    if (status) {
      showToast().info("Filter Applied", `Showing ${status.toLowerCase()} bookings`, 2000);
    } else {
      showToast().info("Filter Cleared", "Showing bookings with all statuses", 1500);
    }
  }, []); // Remove showToast dependency

  // Filter by resource
  const setResourceFilter = useCallback((resourceId?: number) => {
    setState(prev => ({
      ...prev,
      selectedResourceId: resourceId,
      currentPage: 1, // Reset to first page when filtering
    }));

    if (resourceId) {
      showToast().info("Filter Applied", "Showing bookings for selected resource", 2000);
    } else {
      showToast().info("Filter Cleared", "Showing bookings for all resources", 1500);
    }
  }, []); // Remove showToast dependency

  // Set date range filter
  const setDateRange = useCallback((startDate?: Date, endDate?: Date) => {
    setState(prev => ({
      ...prev,
      startDate,
      endDate,
      currentPage: 1, // Reset to first page when filtering
    }));

    if (startDate || endDate) {
      const dateRangeText = startDate && endDate 
        ? `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
        : startDate 
          ? `from ${startDate.toLocaleDateString()}`
          : `until ${endDate?.toLocaleDateString()}`;
      
      showToast().info("Date Filter Applied", `Showing bookings ${dateRangeText}`, 2000);
    } else {
      showToast().info("Date Filter Cleared", "Showing bookings for all dates", 1500);
    }
  }, []); // Remove showToast dependency

  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
  }, []);

  const setPageSize = useCallback((size: number) => {
    setState(prev => ({
      ...prev,
      pageSize: size,
      currentPage: 1, // Reset to first page when changing page size
    }));
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      searchQuery: "",
      selectedCustomerId: undefined,
      selectedStatus: undefined,
      selectedResourceId: undefined,
      selectedInvoiceStatus: undefined,
      startDate: undefined,
      endDate: undefined,
      currentPage: 1,
    }));

    showToast().info("Filters Cleared", "Showing all bookings", 2000);
  }, []); // Remove showToast dependency

  // Utility functions
  const refresh = useCallback(() => {
    fetchBookings(true);
  }, []); // Remove fetchBookings dependency

  const refreshCalendar = useCallback(() => {
    fetchCalendarEvents(state.startDate, state.endDate);
  }, [state.startDate, state.endDate]); // Remove fetchCalendarEvents dependency

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Auto-fetch when filter dependencies change
  useEffect(() => {
    fetchBookings();
  }, [fetchBookings]);

  return {
    // State
    bookings: state.bookings,
    calendarEvents: state.calendarEvents,
    loading: state.loading,
    error: state.error,
    errorAnalysis: state.errorAnalysis,
    retryCount: state.retryCount,
    isRetrying: state.isRetrying,
    totalBookings: state.totalBookings,
    totalPages: state.totalPages,
    currentPage: state.currentPage,
    pageSize: state.pageSize,
    searchQuery: state.searchQuery,
    selectedCustomerId: state.selectedCustomerId,
    selectedStatus: state.selectedStatus,
    selectedResourceId: state.selectedResourceId,
    selectedInvoiceStatus: state.selectedInvoiceStatus,
    startDate: state.startDate,
    endDate: state.endDate,
    
    // Actions
    fetchBookings,
    fetchCalendarEvents,
    createBooking,
    updateBooking,
    deleteBooking,
    generateInvoice,
    setSearchQuery,
    setCustomerFilter,
    setStatusFilter,
    setResourceFilter,
    setDateRange,
    setCurrentPage,
    setPageSize,
    refresh,
    refreshCalendar,
    clearError,
    clearFilters,
  };
}