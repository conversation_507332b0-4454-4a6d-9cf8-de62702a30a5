"use client";

import { useState, useCallback, useRef } from 'react';

interface OverlapCheckParams {
  resourceIds: number[];
  start: string;
  end: string;
  excludeBookingId?: number;
}

interface ConflictDetail {
  bookingId: number;
  customerName: string;
  start: Date;
  end: Date;
  status: string;
}

interface ResourceConflict {
  resourceId: number;
  resourceName: string;
  conflicts: ConflictDetail[];
}

interface OverlapCheckResult {
  hasConflicts: boolean;
  conflicts: ResourceConflict[];
  message: string;
}

export function useBookingOverlap() {
  const [checking, setChecking] = useState(false);
  const [lastCheck, setLastCheck] = useState<OverlapCheckResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Stable reference to avoid infinite loops
  // @ts-ignore
  const checkOverlapRef = useRef<(params: OverlapCheckParams) => Promise<OverlapCheckResult | null>>();

  const checkOverlap = useCallback(async (params: OverlapCheckParams): Promise<OverlapCheckResult | null> => {
    // Validate required parameters
    if (!params || !params.resourceIds || !params.resourceIds.length || !params.start || !params.end) {
      return null;
    }

    setChecking(true);
    setError(null);

    try {
      const response = await fetch('/api/bookings/check-overlap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to check overlap');
      }

      const result = await response.json();
      setLastCheck(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error checking booking overlap:', err);
      return null;
    } finally {
      setChecking(false);
    }
  }, []);

  // Update the ref whenever checkOverlap changes
  checkOverlapRef.current = checkOverlap;

  // Debounced version for real-time checking
  // @ts-ignore
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  const debouncedCheckOverlap = useCallback((params: OverlapCheckParams) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      checkOverlapRef.current?.(params);
    }, 500);
  }, []);

  const clearResults = useCallback(() => {
    setLastCheck(null);
    setError(null);
  }, []);

  return {
    checkOverlap,
    debouncedCheckOverlap,
    checking,
    lastCheck,
    error,
    clearResults,
  };
}