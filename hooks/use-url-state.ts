"use client";

import { useCallback, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

interface UseUrlStateOptions {
  replace?: boolean; // Use replace instead of push for navigation
  shallow?: boolean; // Shallow routing (Next.js specific)
}

/**
 * Hook for managing URL search parameters with React state synchronization
 * Provides a way to sync component state with URL parameters
 */
export function useUrlState<T extends string | undefined>(
  key: string,
  defaultValue: T,
  options: UseUrlStateOptions = {}
): [T, (value: T) => void] {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { replace = false } = options;

  // Get initial value from URL or use default
  const getInitialValue = useCallback((): T => {
    const urlValue = searchParams.get(key);
    return (urlValue as T) || defaultValue;
  }, [key, defaultValue, searchParams]);

  const [state, setState] = useState<T>(getInitialValue);

  // Update URL when state changes
  const updateUrl = useCallback((newValue: T) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    
    if (newValue && newValue !== defaultValue) {
      current.set(key, newValue);
    } else {
      current.delete(key);
    }

    const search = current.toString();
    const query = search ? `?${search}` : "";
    
    if (replace) {
      router.replace(`${window.location.pathname}${query}`, { scroll: false });
    } else {
      router.push(`${window.location.pathname}${query}`, { scroll: false });
    }
  }, [key, defaultValue, searchParams, router, replace]);

  // Update state and URL
  const setValue = useCallback((newValue: T) => {
    setState(newValue);
    updateUrl(newValue);
  }, [updateUrl]);

  // Sync state with URL changes (e.g., browser back/forward)
  useEffect(() => {
    const urlValue = getInitialValue();
    if (urlValue !== state) {
      setState(urlValue);
    }
  }, [searchParams, getInitialValue, state]);

  return [state, setValue];
}

/**
 * Hook for managing date URL parameters
 * Handles serialization/deserialization of Date objects to/from URL
 */
export function useUrlDateState(
  key: string,
  defaultValue?: Date
): [Date | undefined, (value: Date | undefined) => void] {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get initial value from URL or use default
  const getInitialValue = useCallback((): Date | undefined => {
    const urlValue = searchParams.get(key);
    if (urlValue) {
      const date = new Date(urlValue);
      return isNaN(date.getTime()) ? defaultValue : date;
    }
    return defaultValue;
  }, [key, defaultValue, searchParams]);

  const [state, setState] = useState<Date | undefined>(getInitialValue);

  // Update URL when state changes
  const updateUrl = useCallback((newValue: Date | undefined) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    
    if (newValue) {
      current.set(key, newValue.toISOString());
    } else {
      current.delete(key);
    }

    const search = current.toString();
    const query = search ? `?${search}` : "";
    
    router.replace(`${window.location.pathname}${query}`, { scroll: false });
  }, [key, searchParams, router]);

  // Update state and URL
  const setValue = useCallback((newValue: Date | undefined) => {
    setState(newValue);
    updateUrl(newValue);
  }, [updateUrl, key]);

  // Sync state with URL changes (e.g., browser back/forward)
  useEffect(() => {
    const urlValue = getInitialValue();
    if (urlValue?.getTime() !== state?.getTime()) {
      setState(urlValue);
    }
  }, [searchParams, getInitialValue, state]);

  return [state, setValue];
}

/**
 * Hook for managing multiple URL parameters at once
 * Useful for complex filter states
 */
export function useUrlMultiState<T extends Record<string, string | number | boolean | Date | undefined | null>>(
  defaultValues: T,
  options: UseUrlStateOptions = {}
): [T, (updates: Partial<T>) => void, () => void] {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { replace = true } = options;

  // Get initial values from URL or use defaults
  const getInitialValues = useCallback((): T => {
    const result = { ...defaultValues } as T;

    Object.keys(defaultValues).forEach((key) => {
      const urlValue = searchParams.get(key);
      if (urlValue !== null) {
        // Handle different types
        const defaultValue = defaultValues[key as keyof T];

        if (typeof defaultValue === 'boolean') {
          (result as any)[key] = urlValue === 'true';
        } else if (typeof defaultValue === 'number') {
          const numValue = Number(urlValue);
          (result as any)[key] = isNaN(numValue) ? defaultValue : numValue;
        } else if (defaultValue instanceof Date) {
          const dateValue = new Date(urlValue);
          (result as any)[key] = isNaN(dateValue.getTime()) ? defaultValue : dateValue;
        } else {
          (result as any)[key] = urlValue;
        }
      }
    });

    return result;
  }, [defaultValues, searchParams]);

  const [state, setState] = useState<T>(getInitialValues);

  // Update URL when state changes
  const updateUrl = useCallback((newState: T) => {
    const current = new URLSearchParams();

    Object.entries(newState).forEach(([key, value]) => {
      const defaultValue = defaultValues[key as keyof T];

      // Only add to URL if different from default
      if (value !== defaultValue && value !== undefined && value !== null && value !== '') {
        if (value instanceof Date) {
          current.set(key, value.toISOString());
        } else {
          current.set(key, String(value));
        }
      }
    });

    const search = current.toString();
    const query = search ? `?${search}` : "";

    if (replace) {
      router.replace(`${window.location.pathname}${query}`, { scroll: false });
    } else {
      router.push(`${window.location.pathname}${query}`, { scroll: false });
    }
  }, [defaultValues, router, replace]);

  // Update state and URL
  const updateState = useCallback((updates: Partial<T>) => {
    const newState = { ...state, ...updates };
    setState(newState);
    updateUrl(newState);
  }, [state, updateUrl]);

  // Clear all filters (reset to defaults)
  const clearState = useCallback(() => {
    setState(defaultValues);
    updateUrl(defaultValues);
  }, [defaultValues, updateUrl]);

  // Sync state with URL changes (e.g., browser back/forward)
  useEffect(() => {
    const urlValues = getInitialValues();
    const hasChanges = Object.keys(defaultValues).some(
      key => urlValues[key as keyof T] !== state[key as keyof T]
    );

    if (hasChanges) {
      setState(urlValues);
    }
  }, [searchParams, getInitialValues, state, defaultValues]);

  return [state, updateState, clearState];
}