import { describe, it, expect } from 'vitest'
import {
  computeBookingTimeRange,
  calculateTotalBookingDuration,
  formatPeriodDuration,
  formatBookingPeriodsOverview,
  Period
} from '../types'

describe('Period Type Utilities', () => {
  const mockPeriods: Period[] = [
    {
      id: 1,
      bookingId: 1,
      start: new Date('2025-08-07T10:00:00Z'),
      end: new Date('2025-08-07T12:00:00Z'),
      isRecurring: false,
      recurrenceRule: null,
      parentPeriodId: null,
      booking: {} as any,
      parentPeriod: null,
      childPeriods: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      createdById: null,
      updatedById: null
    },
    {
      id: 2,
      bookingId: 1,
      start: new Date('2025-08-07T14:00:00Z'),
      end: new Date('2025-08-07T16:00:00Z'),
      isRecurring: false,
      recurrenceRule: null,
      parentPeriodId: null,
      booking: {} as any,
      parentPeriod: null,
      childPeriods: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      createdById: null,
      updatedById: null
    }
  ]

  describe('computeBookingTimeRange', () => {
    it('should compute correct time range for multiple periods', () => {
      const result = computeBookingTimeRange(mockPeriods)
      
      expect(result.start).toEqual(new Date('2025-08-07T10:00:00Z'))
      expect(result.end).toEqual(new Date('2025-08-07T16:00:00Z'))
    })

    it('should handle single period', () => {
      const singlePeriod = [mockPeriods[0]]
      const result = computeBookingTimeRange(singlePeriod)
      
      expect(result.start).toEqual(new Date('2025-08-07T10:00:00Z'))
      expect(result.end).toEqual(new Date('2025-08-07T12:00:00Z'))
    })

    it('should throw error for empty periods array', () => {
      expect(() => computeBookingTimeRange([])).toThrow('Booking must have at least one period')
    })
  })

  describe('calculateTotalBookingDuration', () => {
    it('should calculate total duration for multiple periods', () => {
      const result = calculateTotalBookingDuration(mockPeriods)
      
      // Each period is 2 hours (2 * 60 * 60 * 1000 ms), total is 4 hours
      const expectedDuration = 4 * 60 * 60 * 1000
      expect(result).toBe(expectedDuration)
    })

    it('should return 0 for empty periods array', () => {
      const result = calculateTotalBookingDuration([])
      expect(result).toBe(0)
    })
  })

  describe('formatPeriodDuration', () => {
    it('should format duration in hours and minutes', () => {
      const start = new Date('2025-08-07T10:00:00Z')
      const end = new Date('2025-08-07T12:30:00Z')
      
      const result = formatPeriodDuration(start, end)
      expect(result).toBe('2 hours and 30 minutes')
    })

    it('should format duration with only hours', () => {
      const start = new Date('2025-08-07T10:00:00Z')
      const end = new Date('2025-08-07T12:00:00Z')
      
      const result = formatPeriodDuration(start, end)
      expect(result).toBe('2 hours')
    })

    it('should format duration with only minutes', () => {
      const start = new Date('2025-08-07T10:00:00Z')
      const end = new Date('2025-08-07T10:45:00Z')
      
      const result = formatPeriodDuration(start, end)
      expect(result).toBe('45 minutes')
    })

    it('should handle zero duration', () => {
      const start = new Date('2025-08-07T10:00:00Z')
      const end = new Date('2025-08-07T10:00:00Z')
      
      const result = formatPeriodDuration(start, end)
      expect(result).toBe('0 minutes')
    })

    it('should handle invalid dates', () => {
      const result = formatPeriodDuration('invalid', 'invalid')
      expect(result).toBe('Invalid duration')
    })

    it('should handle negative duration', () => {
      const start = new Date('2025-08-07T12:00:00Z')
      const end = new Date('2025-08-07T10:00:00Z')
      
      const result = formatPeriodDuration(start, end)
      expect(result).toBe('Invalid duration')
    })
  })

  describe('formatBookingPeriodsOverview', () => {
    it('should format single period', () => {
      const singlePeriod = [mockPeriods[0]]
      const result = formatBookingPeriodsOverview(singlePeriod)
      
      expect(result).toContain('8/7/2025')
      // Don't check specific times due to timezone differences
      expect(result).toContain(' - ')
    })

    it('should format multiple periods', () => {
      const result = formatBookingPeriodsOverview(mockPeriods)
      
      expect(result).toContain('2 periods')
      expect(result).toContain('8/7/2025')
    })

    it('should handle empty periods array', () => {
      const result = formatBookingPeriodsOverview([])
      expect(result).toBe('No periods')
    })
  })
})