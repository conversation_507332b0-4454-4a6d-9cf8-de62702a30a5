export type Expense = {
  id: string;
  amount: number;
  category: string;
  description: string;
  date: Date;
};

export type ExpenseFormData = Omit<Expense, "id" | "date"> & {
  date: string;
};

export const EXPENSE_CATEGORIES = [
  "Food",
  "Transportation",
  "Housing",
  "Utilities",
  "Entertainment",
  "Healthcare",
  "Shopping",
  "Education",
  "Other",
] as const;

export type DateRange = {
  from: Date | undefined;
  to: Date | undefined;
};

// User Management Types
export type UserRole = "ADMIN" | "LOGISTICS" | "RECEIPTION" | "FINANCE" | "OFFICE";

export interface User {
  id: number;
  firstName: string;
  lastName: string;
  username: string;
  role: UserRole;
  isSuperUser: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface UserFormData {
  firstName: string;
  lastName: string;
  username: string;
  password?: string; // Only for create operations
  role: UserRole;
  isSuperUser: boolean;
}

export interface PasswordChangeData {
  password: string;
  confirmPassword: string;
}

export interface UserSearchParams {
  search?: string;
  page?: number;
  limit?: number;
  role?: UserRole;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export type UserListResponse = PaginatedResponse<User>;

// Amenity Management Types
export type AmenityIcon =
  | "PROJECTOR"
  | "WHITEBOARD"
  | "SMARTBOARD"
  | "TABLE"
  | "WIFI"
  | "AIR_CONDITIONER"
  | "TV"
  | "MICROPHONE"
  | "SPEAKER"
  | "STAGE"
  | "COFFEE_MACHINE"
  | "WATER_DISPENSER"
  | "CATERING"
  | "SECURITY"
  | "PARKING";

export interface Amenity {
  id: number;
  name: string;
  icon: AmenityIcon;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface AmenityFormData {
  name: string;
  icon: AmenityIcon;
}

export interface AmenitySearchParams {
  search?: string;
  page?: number;
  limit?: number;
}

export type AmenityListResponse = PaginatedResponse<Amenity>;

// Resource Management Types
export type ResourceType =
  | "INDOOR_EVENT_HALL"
  | "OUTDOOR_EVENT_HALL"
  | "TRAINING_ROOM"
  | "MEETING_ROOM"
  | "DESK"
  | "PRIVATE_OFFICE";

export type SeatingStyle = "CINEMA" | "ROUND_TABLE";
export type StageStyle = "PODIUM" | "PANEL";

export interface Resource {
  id: number;
  name: string;
  type: ResourceType;
  basePrice: number;
  details?: string | null;
  seatingStyle?: SeatingStyle | null;
  numberOfAttendees?: number | null;
  numberOfDesks?: number | null;
  numberOfChairs?: number | null;
  amenities: Amenity[];
  stageStyles: ResourceStageStyle[];
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface ResourceStageStyle {
  id: number;
  resourceId: number;
  style: StageStyle;
  createdAt: Date;
  updatedAt: Date;
}

export interface ResourceFormData {
  name: string;
  type: ResourceType;
  basePrice: number;
  details?: string;
  seatingStyle?: SeatingStyle;
  numberOfAttendees?: number;
  numberOfDesks?: number;
  numberOfChairs?: number;
  amenityIds: number[];
  stageStyles: StageStyle[];
}

export interface ResourceSearchParams {
  search?: string;
  type?: ResourceType;
  page?: number;
  limit?: number;
}

export type ResourceListResponse = PaginatedResponse<Resource>;

// Resource type configuration for dynamic form fields
export interface ResourceTypeConfig {
  fields: string[];
  required: string[];
  allowMultipleStageStyles: boolean;
  displayName: string;
  description: string;
  capacityLabel?: string;
}

export const RESOURCE_TYPE_CONFIG: Record<ResourceType, ResourceTypeConfig> = {
  INDOOR_EVENT_HALL: {
    fields: ["seatingStyle", "numberOfAttendees", "stageStyles"],
    required: ["seatingStyle", "numberOfAttendees"],
    allowMultipleStageStyles: true,
    displayName: "Indoor Event Hall",
    description:
      "Large indoor space for events with configurable seating and stage options",
    capacityLabel: "attendees",
  },
  OUTDOOR_EVENT_HALL: {
    fields: ["seatingStyle", "numberOfAttendees"],
    required: ["seatingStyle", "numberOfAttendees"],
    allowMultipleStageStyles: false,
    displayName: "Outdoor Event Hall",
    description: "Open-air venue for outdoor events and gatherings",
    capacityLabel: "attendees",
  },
  TRAINING_ROOM: {
    fields: ["seatingStyle", "numberOfAttendees"],
    required: ["numberOfAttendees"],
    allowMultipleStageStyles: false,
    displayName: "Training Room",
    description: "Dedicated space for training sessions and workshops",
    capacityLabel: "attendees",
  },
  MEETING_ROOM: {
    fields: ["numberOfAttendees"],
    required: ["numberOfAttendees"],
    allowMultipleStageStyles: false,
    displayName: "Meeting Room",
    description: "Professional space for meetings and conferences",
    capacityLabel: "attendees",
  },
  DESK: {
    fields: [],
    required: [],
    allowMultipleStageStyles: false,
    displayName: "Desk",
    description: "Individual workspace for focused work",
    capacityLabel: undefined,
  },
  PRIVATE_OFFICE: {
    fields: ["numberOfDesks", "numberOfChairs"],
    required: ["numberOfDesks", "numberOfChairs"],
    allowMultipleStageStyles: false,
    displayName: "Private Office",
    description: "Enclosed office space with configurable furniture",
    capacityLabel: "desks",
  },
};

// Resource type display utilities
export const SEATING_STYLE_LABELS: Record<SeatingStyle, string> = {
  CINEMA: "Cinema Style",
  ROUND_TABLE: "Round Table",
};

export const STAGE_STYLE_LABELS: Record<StageStyle, string> = {
  PODIUM: "Podium",
  PANEL: "Panel",
};

// Helper functions for resource type management
export const getResourceTypeDisplayName = (type: ResourceType): string => {
  if (!type) {
    return 'Unknown Type';
  }
  
  const config = RESOURCE_TYPE_CONFIG[type];
  if (!config) {
    console.warn(`Unknown resource type: ${type}`);
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }
  return config.displayName;
};

export const getResourceTypeDescription = (type: ResourceType): string => {
  const config = RESOURCE_TYPE_CONFIG[type];
  if (!config) {
    console.warn(`Unknown resource type: ${type}`);
    return `Description for ${type}`;
  }
  return config.description;
};

export const getResourceCapacityLabel = (
  type: ResourceType
): string | undefined => {
  return RESOURCE_TYPE_CONFIG[type].capacityLabel;
};

export const getRequiredFieldsForResourceType = (
  type: ResourceType
): string[] => {
  return RESOURCE_TYPE_CONFIG[type].required;
};

export const getAvailableFieldsForResourceType = (
  type: ResourceType
): string[] => {
  return RESOURCE_TYPE_CONFIG[type].fields;
};

export const doesResourceTypeAllowMultipleStageStyles = (
  type: ResourceType
): boolean => {
  return RESOURCE_TYPE_CONFIG[type].allowMultipleStageStyles;
};

export const formatResourceCapacity = (resource: Resource): string => {
  const config = RESOURCE_TYPE_CONFIG[resource.type];

  switch (resource.type) {
    case "INDOOR_EVENT_HALL":
    case "OUTDOOR_EVENT_HALL":
    case "TRAINING_ROOM":
    case "MEETING_ROOM":
      return resource.numberOfAttendees
        ? `${resource.numberOfAttendees} ${config.capacityLabel}`
        : "N/A";
    case "PRIVATE_OFFICE":
      const desks = resource.numberOfDesks || 0;
      const chairs = resource.numberOfChairs || 0;
      return `${desks} desks, ${chairs} chairs`;
    case "DESK":
      return "1 person";
    default:
      return "N/A";
  }
};

export const formatResourcePrice = (price: number): string => {
  return `${price.toLocaleString()} IQD`;
};

// Catering Management Types
export interface Catering {
  id: number;
  offerName: string;
  pricePerPerson: number;
  firstPartyShare: number; // Fixed amount in IQD
  vendorShare: number; // Fixed amount in IQD
  bookings: CateringOnBooking[];
  lineItems: LineItem[];
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface CateringFormData {
  offerName: string;
  pricePerPerson: number;
  firstPartyShare: number;
  vendorShare: number;
}

export interface CateringOnBooking {
  id: number;
  bookingId: number;
  cateringId: number;
  quantity: number;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface CateringSearchParams {
  search?: string;
  page?: number;
  limit?: number;
}

export interface RevenueSplit {
  totalAmount: number;
  firstPartyAmount: number;
  vendorAmount: number;
  firstPartySharePerPerson: number; // Fixed amount per person
  vendorSharePerPerson: number; // Fixed amount per person
}

export interface CateringWithBookingCount extends Catering {
  _count: {
    bookings: number;
  };
}

export type CateringListResponse = PaginatedResponse<Catering>;

// LineItem interface for catering integration (updated for invoice management)
export interface LineItem {
  id: number;
  invoiceId: number;
  description: string;
  amount: number;
  quantity: number;
  isCatering: boolean;
  catering?: Catering | null;
  cateringId?: number | null;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

// Customer Management Types
export interface Customer {
  id: number;
  name: string;
  email?: string | null;
  phoneNumber: string;
  companyName?: string | null;
  specialization?: string | null;
  industry?: string | null;
  website?: string | null;
  linkedIn?: string | null;
  socialMedia?: string | null;
  notes?: string | null;
  bookings?: Booking[];
  _count?: {
    bookings: number;
  };
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface CustomerFormData {
  name: string;
  email?: string;
  phoneNumber: string;
  companyName?: string;
  specialization?: string;
  industry?: string;
  website?: string;
  linkedIn?: string;
  socialMedia?: string;
  notes?: string;
}

export interface CustomerSearchParams {
  search?: string;
  page?: number;
  limit?: number;
}

export type CustomerListResponse = PaginatedResponse<Customer>;

// Period Management Types
export type RecurrenceType = 'daily' | 'weekly' | 'monthly' | 'yearly';

export interface RecurrenceRule {
  type: RecurrenceType;
  interval: number; // Every N days/weeks/months/years
  endDate?: Date;   // When to stop generating periods
  count?: number;   // Maximum number of periods to generate
  
  // Weekly specific
  daysOfWeek?: number[]; // 0=Sunday, 1=Monday, etc.
  
  // Monthly specific
  dayOfMonth?: number;   // Specific day of month (1-31)
  weekOfMonth?: number;  // Which week of the month (1-4, -1=last)
  dayOfWeek?: number;    // Which day of the week
  
  // Yearly specific
  month?: number;        // Which month (1-12)
  dayOfYear?: number;    // Day of the year (1-365)
}

export interface Period {
  id: number;
  bookingId: number;
  start: Date;
  end: Date;
  isRecurring: boolean;
  recurrenceRule?: RecurrenceRule | null;
  parentPeriodId?: number | null;
  booking: Booking;
  parentPeriod?: Period | null;
  childPeriods?: Period[];
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface PeriodFormData {
  start: Date;
  end: Date;
  isRecurring?: boolean;
  recurrenceRule?: RecurrenceRule;
}

// Conflict Detection Types
export interface PeriodConflict {
  periodId: number;
  conflictingPeriodId: number;
  conflictingResources: {
    id: number;
    name: string;
  }[];
  overlapStart: Date;
  overlapEnd: Date;
  conflictingBooking: {
    id: number;
    customerName: string;
    status: BookingStatus;
  };
}

export interface ConflictCheckResult {
  hasConflicts: boolean;
  conflicts: PeriodConflict[];
  affectedResources: {
    id: number;
    name: string;
  }[];
  message: string;
}

// Booking Management Types
export type BookingStatus = "PENDING" | "CONFIRMED" | "CANCELLED";

export interface Booking {
  id: number;
  customer: Customer;
  customerId: number;
  resources: Resource[];
  periods: Period[];
  status: BookingStatus;
  // Computed fields for backward compatibility
  computedStart?: Date;
  computedEnd?: Date;
  invoice?: Invoice | null;
  caterings: CateringOnBooking[];
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface BookingFormData {
  customerId: number;
  resourceIds: number[];
  status: BookingStatus;
  periods: PeriodFormData[];
  caterings: {
    cateringId: number;
    quantity: number;
  }[];
}

export interface BookingSearchParams {
  search?: string;
  customerId?: number;
  status?: BookingStatus;
  resourceId?: number;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

export type BookingListResponse = PaginatedResponse<Booking>;

// Calendar Event Type for ilamy integration
export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  extendedProps: {
    bookingId: number;
    customerName: string;
    status: BookingStatus;
    resourceNames: string[];
  };
}

// Invoice Management Types
export type InvoiceStatus = "PENDING" | "PARTIALLY_PAID" | "PAID" | "CANCELLED";

export interface Invoice {
  id: number;
  booking: Booking;
  bookingId: number;
  lineItems: LineItem[];
  payments: Payment[];
  status: InvoiceStatus;
  total: number;
  paid: number;
  pdfUrl?: string | null;
  _count?: {
    payments: number;
  };
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface InvoiceFormData {
  bookingId: number;
  lineItems: {
    description: string;
    amount: number;
    quantity: number;
    isCatering: boolean;
    cateringId?: number;
  }[];
}

export interface InvoiceSearchParams {
  search?: string;
  status?: InvoiceStatus;
  customerId?: number;
  bookingId?: number;
  page?: number;
  limit?: number;
}

export type InvoiceListResponse = PaginatedResponse<Invoice>;

export interface LineItemFormData {
  description: string;
  amount: number;
  quantity: number;
  isCatering: boolean;
  cateringId?: number;
}

// Payment Management Types
export type PaymentMethod =
  | "CASH"
  | "CREDIT_CARD"
  | "DEBIT_CARD"
  | "BANK_TRANSFER"
  | "CHECK"
  | "ONLINE"
  | "OTHER";
export type PaymentStatus = "PENDING" | "COMPLETED" | "FAILED" | "REFUNDED";

export interface Payment {
  id: number;
  invoice: Invoice;
  invoiceId: number;
  amount: number;
  method: PaymentMethod;
  status: PaymentStatus;
  reference?: string | null;
  notes?: string | null;
  paidAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  createdById?: number | null;
  updatedById?: number | null;
}

export interface PaymentFormData {
  amount: number;
  method: PaymentMethod;
  reference?: string;
  notes?: string;
  paidAt?: Date;
}

export interface PaymentSearchParams {
  invoiceId?: number;
  method?: PaymentMethod;
  status?: PaymentStatus;
  page?: number;
  limit?: number;
}

export type PaymentListResponse = PaginatedResponse<Payment>;

// Payment method display utilities
export const PAYMENT_METHOD_LABELS: Record<PaymentMethod, string> = {
  CASH: "Cash",
  CREDIT_CARD: "Credit Card",
  DEBIT_CARD: "Debit Card",
  BANK_TRANSFER: "Bank Transfer",
  CHECK: "Check",
  ONLINE: "Online Payment",
  OTHER: "Other",
};

export const PAYMENT_STATUS_LABELS: Record<PaymentStatus, string> = {
  PENDING: "Pending",
  COMPLETED: "Completed",
  FAILED: "Failed",
  REFUNDED: "Refunded",
};

// Booking status display utilities
export const BOOKING_STATUS_LABELS: Record<BookingStatus, string> = {
  PENDING: "Pending",
  CONFIRMED: "Confirmed",
  CANCELLED: "Cancelled",
};

// Invoice status display utilities
export const INVOICE_STATUS_LABELS: Record<InvoiceStatus, string> = {
  PENDING: "Pending",
  PARTIALLY_PAID: "Partially Paid",
  PAID: "Paid",
  CANCELLED: "Cancelled",
};

// Helper functions for financial calculations
export const calculateInvoiceBalance = (invoice: Invoice): number => {
  return Number(invoice.total) - Number(invoice.paid);
};

export const calculateInvoiceStatus = (
  total: number,
  paid: number
): InvoiceStatus => {
  if (paid === 0) return "PENDING";
  if (paid >= total) return "PAID";
  return "PARTIALLY_PAID";
};

export const formatCurrency = (
  amount: number, 
  currency: string = "IQD", 
  locale: string = "en-US"
): string => {
  if (isNaN(amount) || !isFinite(amount)) {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(0);
  }
  
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Period utility functions
export const computeBookingTimeRange = (periods: Period[]): { start: Date; end: Date } => {
  if (periods.length === 0) {
    throw new Error('Booking must have at least one period')
  }
  
  const starts = periods.map(p => p.start)
  const ends = periods.map(p => p.end)
  
  return {
    start: new Date(Math.min(...starts.map(d => d.getTime()))),
    end: new Date(Math.max(...ends.map(d => d.getTime())))
  }
}

export const calculateTotalBookingDuration = (periods: Period[]): number => {
  return periods.reduce((total, period) => {
    const duration = period.end.getTime() - period.start.getTime()
    return total + duration
  }, 0)
}

export const formatPeriodDuration = (start: Date | string, end: Date | string): string => {
  try {
    // Convert to Date objects if they're strings
    const startDate = start instanceof Date ? start : new Date(start);
    const endDate = end instanceof Date ? end : new Date(end);
    
    // Validate that we have valid dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return "Invalid duration";
    }
    
    // Handle negative duration
    if (endDate.getTime() < startDate.getTime()) {
      return "Invalid duration";
    }
    
    // Calculate the difference in milliseconds
    const diffMs = endDate.getTime() - startDate.getTime();
    
    // Convert to hours and minutes
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    // Build the human-readable string
    const parts: string[] = [];
    
    if (hours > 0) {
      parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
    }
    
    if (minutes > 0) {
      parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
    }
    
    // If no duration (same time), return "0 minutes"
    if (parts.length === 0) {
      return "0 minutes";
    }
    
    // Join parts with "and"
    return parts.join(' and ');
    
  } catch (error) {
    console.error("Error formatting period duration:", error);
    return "Invalid duration";
  }
}

export const formatBookingDuration = (start: Date | string, end: Date | string): string => {
  try {
    // Convert to Date objects if they're strings
    const startDate = start instanceof Date ? start : new Date(start);
    const endDate = end instanceof Date ? end : new Date(end);
    
    // Validate that we have valid dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return "Invalid duration";
    }
    
    // Handle negative duration
    if (endDate.getTime() < startDate.getTime()) {
      return "Invalid duration";
    }
    
    // Calculate the difference in years, months, days, hours, and minutes
    let years = endDate.getFullYear() - startDate.getFullYear();
    let months = endDate.getMonth() - startDate.getMonth();
    let days = endDate.getDate() - startDate.getDate();
    let hours = endDate.getHours() - startDate.getHours();
    let minutes = endDate.getMinutes() - startDate.getMinutes();
    
    // Handle negative values by borrowing from higher units
    if (minutes < 0) {
      minutes += 60;
      hours--;
    }
    
    if (hours < 0) {
      hours += 24;
      days--;
    }
    
    if (days < 0) {
      // Get the number of days in the previous month
      const prevMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 0);
      days += prevMonth.getDate();
      months--;
    }
    
    if (months < 0) {
      months += 12;
      years--;
    }
    
    // Build the human-readable string
    const parts: string[] = [];
    
    if (years > 0) {
      parts.push(`${years} year${years !== 1 ? 's' : ''}`);
    }
    
    if (months > 0) {
      parts.push(`${months} month${months !== 1 ? 's' : ''}`);
    }
    
    if (days > 0) {
      parts.push(`${days} day${days !== 1 ? 's' : ''}`);
    }
    
    if (hours > 0) {
      parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
    }
    
    if (minutes > 0) {
      parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
    }
    
    // If no duration (same time), return "0 minutes"
    if (parts.length === 0) {
      return "0 minutes";
    }
    
    // Join parts with commas and "and" for the last item
    if (parts.length === 1) {
      return parts[0];
    } else if (parts.length === 2) {
      return parts.join(' and ');
    } else {
      const lastPart = parts.pop();
      return parts.join(', ') + ' and ' + lastPart;
    }
    
  } catch (error) {
    console.error("Error formatting booking duration:", error);
    return "Invalid duration";
  }
}

// Format multiple periods for display
export const formatBookingPeriodsOverview = (periods: Period[]): string => {
  if (periods.length === 0) return "No periods";
  if (periods.length === 1) {
    const period = periods[0];
    return `${period.start.toLocaleDateString()} ${period.start.toLocaleTimeString()} - ${period.end.toLocaleTimeString()}`;
  }
  
  const timeRange = computeBookingTimeRange(periods);
  return `${periods.length} periods from ${timeRange.start.toLocaleDateString()} to ${timeRange.end.toLocaleDateString()}`;
};

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, string[]>;
}

// Organization Settings Types
export interface OrganizationSettings {
  id: number;
  organizationName: string;
  address?: string | null;
  email?: string | null;
  phoneNumber?: string | null;
  website?: string | null;
  linkedin?: string | null;
  facebook?: string | null;
  instagram?: string | null;
  paymentTerms?: string | null;
  paymentMethods?: string | null;
  support?: string | null;
  invoiceFooter?: string | null;
  invoiceSubfooter?: string | null;
  invoiceTitle?: string | null;
  invoiceSubtitle?: string | null;
  logoUrl?: string | null;
}
