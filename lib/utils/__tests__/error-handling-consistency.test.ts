import { describe, it, expect, beforeEach, vi } from 'vitest'

/**
 * Error handling consistency tests for table components
 * These tests verify that error handling works consistently across all table components
 */

// Mock error types that can occur in table components
const mockErrors = {
  networkError: new Error('Network request failed'),
  timeoutError: new Error('Request timeout'),
  serverError: new Error('500 Internal Server Error'),
  authError: new Error('401 Unauthorized'),
  validationError: new Error('400 Bad Request - Invalid data'),
  parseError: new Error('Failed to parse JSON response'),
  offlineError: new Error('No internet connection')
}

// Mock retry mechanisms
const createRetryMechanism = (maxRetries: number = 3) => {
  let attempts = 0
  
  const mechanism = {
    attempt: async (operation: () => Promise<any>): Promise<any> => {
      attempts++
      try {
        return await operation()
      } catch (error) {
        if (attempts < maxRetries) {
          // Exponential backoff
          const delay = Math.pow(2, attempts - 1) * 1000
          await new Promise(resolve => setTimeout(resolve, delay))
          return mechanism.attempt(operation)
        }
        throw error
      }
    },
    getAttempts: () => attempts,
    reset: () => { attempts = 0 }
  }
  
  return mechanism
}

describe('Error Handling Consistency Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true
    })
  })

  describe('Error Classification Tests', () => {
    it('should correctly classify network errors', () => {
      const classifyError = (error: Error) => {
        const message = error.message.toLowerCase()
        
        if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
          return 'network'
        }
        if (message.includes('timeout')) {
          return 'timeout'
        }
        if (message.includes('500') || message.includes('502') || message.includes('503')) {
          return 'server'
        }
        if (message.includes('401') || message.includes('403')) {
          return 'auth'
        }
        if (message.includes('400')) {
          return 'validation'
        }
        return 'unknown'
      }

      expect(classifyError(mockErrors.networkError)).toBe('network')
      expect(classifyError(mockErrors.timeoutError)).toBe('timeout')
      expect(classifyError(mockErrors.serverError)).toBe('server')
      expect(classifyError(mockErrors.authError)).toBe('auth')
      expect(classifyError(mockErrors.validationError)).toBe('validation')
      expect(classifyError(new Error('Unknown error'))).toBe('unknown')
    })

    it('should provide appropriate error messages for each type', () => {
      const getErrorMessage = (errorType: string) => {
        const messages = {
          network: 'Unable to connect to the server. Please check your internet connection.',
          timeout: 'The request took too long to complete. Please try again.',
          server: 'The server is experiencing issues. Please try again later.',
          auth: 'You are not authorized to perform this action. Please log in again.',
          validation: 'The data provided is invalid. Please check your input.',
          unknown: 'An unexpected error occurred. Please try again.'
        }
        return messages[errorType as keyof typeof messages] || messages.unknown
      }

      expect(getErrorMessage('network')).toContain('internet connection')
      expect(getErrorMessage('timeout')).toContain('took too long')
      expect(getErrorMessage('server')).toContain('server is experiencing')
      expect(getErrorMessage('auth')).toContain('not authorized')
      expect(getErrorMessage('validation')).toContain('data provided is invalid')
      expect(getErrorMessage('unknown')).toContain('unexpected error')
    })
  })

  describe('Retry Mechanism Tests', () => {
    it('should implement exponential backoff for retries', async () => {
      const retryMechanism = createRetryMechanism(3)
      let callCount = 0
      
      const failingOperation = async () => {
        callCount++
        if (callCount < 3) {
          throw new Error('Operation failed')
        }
        return 'success'
      }

      const startTime = Date.now()
      const result = await retryMechanism.attempt(failingOperation)
      const endTime = Date.now()

      expect(result).toBe('success')
      expect(retryMechanism.getAttempts()).toBe(3)
      expect(endTime - startTime).toBeGreaterThan(2000) // Should have delays
    })

    it('should respect maximum retry limits', async () => {
      const retryMechanism = createRetryMechanism(2)
      
      const alwaysFailingOperation = async () => {
        throw new Error('Always fails')
      }

      try {
        await retryMechanism.attempt(alwaysFailingOperation)
        expect.fail('Should have thrown an error')
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect(retryMechanism.getAttempts()).toBe(2)
      }
    })

    it('should not retry for certain error types', () => {
      const shouldRetry = (error: Error) => {
        const message = error.message.toLowerCase()
        
        // Don't retry auth errors or validation errors
        if (message.includes('401') || message.includes('403') || message.includes('400')) {
          return false
        }
        
        // Retry network and server errors
        return true
      }

      expect(shouldRetry(mockErrors.networkError)).toBe(true)
      expect(shouldRetry(mockErrors.serverError)).toBe(true)
      expect(shouldRetry(mockErrors.authError)).toBe(false)
      expect(shouldRetry(mockErrors.validationError)).toBe(false)
    })
  })

  describe('Offline State Handling Tests', () => {
    it('should detect offline state correctly', () => {
      navigator.onLine = false
      
      const isOffline = () => !navigator.onLine
      const getOfflineMessage = () => 'You are currently offline. Some features may be limited.'

      expect(isOffline()).toBe(true)
      expect(getOfflineMessage()).toContain('currently offline')
    })

    it('should queue actions when offline', () => {
      const offlineQueue: Array<{ id: string, action: string, data: any }> = []
      
      const queueAction = (action: string, data: any) => {
        if (!navigator.onLine) {
          offlineQueue.push({
            id: `offline_${Date.now()}`,
            action,
            data
          })
          return true
        }
        return false
      }

      navigator.onLine = false
      
      const wasQueued = queueAction('create_customer', { name: 'John Doe' })
      expect(wasQueued).toBe(true)
      expect(offlineQueue).toHaveLength(1)
      expect(offlineQueue[0].action).toBe('create_customer')
    })

    it('should process queued actions when coming back online', async () => {
      const offlineQueue: Array<{ id: string, action: string, data: any }> = []
      let processedActions: Array<any> = []
      
      // Add some queued actions
      offlineQueue.push(
        { id: '1', action: 'create_customer', data: { name: 'John' } },
        { id: '2', action: 'update_customer', data: { id: 1, name: 'Jane' } }
      )

      const processOfflineQueue = async () => {
        while (offlineQueue.length > 0) {
          const action = offlineQueue.shift()
          if (action) {
            // Simulate processing
            await new Promise(resolve => setTimeout(resolve, 10))
            processedActions.push(action)
          }
        }
      }

      await processOfflineQueue()
      
      expect(offlineQueue).toHaveLength(0)
      expect(processedActions).toHaveLength(2)
      expect(processedActions[0].action).toBe('create_customer')
      expect(processedActions[1].action).toBe('update_customer')
    })
  })

  describe('Error State Recovery Tests', () => {
    it('should preserve user state during error recovery', () => {
      const userState = {
        sortConfig: { field: 'name', direction: 'asc' },
        filterConfig: { status: 'active' },
        selectedItems: [1, 2, 3],
        currentPage: 2
      }

      const preserveStateOnError = (currentState: typeof userState) => {
        // Simulate error occurring
        const errorOccurred = true
        
        if (errorOccurred) {
          // State should be preserved
          return { ...currentState, error: 'Network error occurred' }
        }
        
        return currentState
      }

      const newState = preserveStateOnError(userState)
      
      expect(newState.sortConfig).toEqual(userState.sortConfig)
      expect(newState.filterConfig).toEqual(userState.filterConfig)
      expect(newState.selectedItems).toEqual(userState.selectedItems)
      expect(newState.currentPage).toBe(userState.currentPage)
      expect(newState.error).toBe('Network error occurred')
    })

    it('should gracefully degrade functionality during errors', () => {
      const getAvailableActions = (hasError: boolean, errorType: string) => {
        const allActions = ['create', 'edit', 'delete', 'export', 'refresh']
        
        if (!hasError) {
          return allActions
        }
        
        // During errors, limit available actions
        if (errorType === 'network') {
          return ['refresh'] // Only allow refresh to retry
        }
        
        if (errorType === 'auth') {
          return [] // No actions available
        }
        
        return ['refresh'] // Default to refresh only
      }

      expect(getAvailableActions(false, '')).toEqual(['create', 'edit', 'delete', 'export', 'refresh'])
      expect(getAvailableActions(true, 'network')).toEqual(['refresh'])
      expect(getAvailableActions(true, 'auth')).toEqual([])
    })
  })

  describe('Error Boundary Integration Tests', () => {
    it('should catch and handle component errors', () => {
      let caughtError: Error | null = null
      let errorInfo: any = null
      
      const mockErrorBoundary = {
        componentDidCatch: (error: Error, info: any) => {
          caughtError = error
          errorInfo = info
        },
        
        render: () => {
          if (caughtError) {
            return 'Error occurred - showing fallback UI'
          }
          return 'Normal component'
        }
      }

      // Simulate component error
      const componentError = new Error('Component render failed')
      mockErrorBoundary.componentDidCatch(componentError, { componentStack: 'test' })
      
      expect(caughtError).toBe(componentError)
      expect(errorInfo.componentStack).toBe('test')
      expect(mockErrorBoundary.render()).toBe('Error occurred - showing fallback UI')
    })

    it('should provide fallback UI for different error types', () => {
      const getFallbackUI = (errorType: string) => {
        const fallbacks = {
          network: 'Unable to load data. Please check your connection.',
          server: 'Server error. Please try again later.',
          auth: 'Please log in to continue.',
          validation: 'Invalid data. Please check your input.',
          unknown: 'Something went wrong. Please refresh the page.'
        }
        
        return fallbacks[errorType as keyof typeof fallbacks] || fallbacks.unknown
      }

      expect(getFallbackUI('network')).toContain('check your connection')
      expect(getFallbackUI('server')).toContain('try again later')
      expect(getFallbackUI('auth')).toContain('log in to continue')
      expect(getFallbackUI('validation')).toContain('check your input')
      expect(getFallbackUI('unknown')).toContain('refresh the page')
    })
  })

  describe('Error Logging and Monitoring Tests', () => {
    it('should log errors with appropriate context', () => {
      const errorLogs: Array<any> = []
      
      const logError = (error: Error, context: any) => {
        errorLogs.push({
          message: error.message,
          stack: error.stack,
          context,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location?.href || 'test'
        })
      }

      const testError = new Error('Test error')
      const testContext = {
        component: 'CustomersTable',
        action: 'fetchData',
        userId: 123
      }

      logError(testError, testContext)
      
      expect(errorLogs).toHaveLength(1)
      expect(errorLogs[0].message).toBe('Test error')
      expect(errorLogs[0].context.component).toBe('CustomersTable')
      expect(errorLogs[0].context.action).toBe('fetchData')
      expect(errorLogs[0].timestamp).toBeDefined()
    })

    it('should track error frequency and patterns', () => {
      const errorTracker = {
        errors: new Map<string, number>(),
        
        trackError: function(errorType: string) {
          const count = this.errors.get(errorType) || 0
          this.errors.set(errorType, count + 1)
        },
        
        getErrorCount: function(errorType: string) {
          return this.errors.get(errorType) || 0
        },
        
        getMostFrequentError: function() {
          let maxCount = 0
          let mostFrequent = ''
          
          for (const [errorType, count] of this.errors.entries()) {
            if (count > maxCount) {
              maxCount = count
              mostFrequent = errorType
            }
          }
          
          return { errorType: mostFrequent, count: maxCount }
        }
      }

      errorTracker.trackError('network')
      errorTracker.trackError('network')
      errorTracker.trackError('server')
      errorTracker.trackError('network')

      expect(errorTracker.getErrorCount('network')).toBe(3)
      expect(errorTracker.getErrorCount('server')).toBe(1)
      expect(errorTracker.getMostFrequentError()).toEqual({ errorType: 'network', count: 3 })
    })
  })

  describe('User Experience During Errors Tests', () => {
    it('should provide clear error messages to users', () => {
      const getUserFriendlyMessage = (error: Error) => {
        const technicalMessage = error.message
        
        // Convert technical errors to user-friendly messages
        if (technicalMessage.includes('fetch')) {
          return 'Unable to load data. Please check your internet connection and try again.'
        }
        
        if (technicalMessage.includes('timeout')) {
          return 'The request is taking longer than expected. Please try again.'
        }
        
        if (technicalMessage.includes('500')) {
          return 'Our servers are experiencing issues. Please try again in a few minutes.'
        }
        
        if (technicalMessage.includes('401')) {
          return 'Your session has expired. Please log in again.'
        }
        
        return 'Something went wrong. Please try again or contact support if the problem persists.'
      }

      expect(getUserFriendlyMessage(new Error('fetch failed'))).toContain('check your internet connection')
      expect(getUserFriendlyMessage(new Error('timeout'))).toContain('taking longer than expected')
      expect(getUserFriendlyMessage(new Error('500 error'))).toContain('servers are experiencing issues')
      expect(getUserFriendlyMessage(new Error('401 unauthorized'))).toContain('session has expired')
    })

    it('should provide actionable recovery options', () => {
      const getRecoveryOptions = (errorType: string) => {
        const options = {
          network: [
            { label: 'Try Again', action: 'retry' },
            { label: 'Check Connection', action: 'check_connection' }
          ],
          server: [
            { label: 'Try Again', action: 'retry' },
            { label: 'Contact Support', action: 'contact_support' }
          ],
          auth: [
            { label: 'Log In Again', action: 'login' },
            { label: 'Go to Home', action: 'home' }
          ],
          validation: [
            { label: 'Fix Input', action: 'fix_input' },
            { label: 'Reset Form', action: 'reset_form' }
          ]
        }
        
        return options[errorType as keyof typeof options] || [
          { label: 'Try Again', action: 'retry' }
        ]
      }

      const networkOptions = getRecoveryOptions('network')
      expect(networkOptions).toHaveLength(2)
      expect(networkOptions[0].label).toBe('Try Again')
      expect(networkOptions[1].label).toBe('Check Connection')

      const authOptions = getRecoveryOptions('auth')
      expect(authOptions[0].action).toBe('login')
      expect(authOptions[1].action).toBe('home')
    })
  })

  describe('Error Prevention Tests', () => {
    it('should validate data before operations', () => {
      const validateCustomerData = (data: any) => {
        const errors: string[] = []
        
        if (!data.name || data.name.trim().length === 0) {
          errors.push('Name is required')
        }
        
        if (!data.email || !/\S+@\S+\.\S+/.test(data.email)) {
          errors.push('Valid email is required')
        }
        
        if (data.phone && !/^\d{10,}$/.test(data.phone.replace(/\D/g, ''))) {
          errors.push('Valid phone number is required')
        }
        
        return {
          isValid: errors.length === 0,
          errors
        }
      }

      const validData = { name: 'John Doe', email: '<EMAIL>', phone: '1234567890' }
      const invalidData = { name: '', email: 'invalid', phone: '123' }

      expect(validateCustomerData(validData).isValid).toBe(true)
      expect(validateCustomerData(invalidData).isValid).toBe(false)
      expect(validateCustomerData(invalidData).errors).toContain('Name is required')
      expect(validateCustomerData(invalidData).errors).toContain('Valid email is required')
    })

    it('should implement request timeouts', async () => {
      const createTimeoutPromise = (timeout: number) => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), timeout)
        })
      }

      const makeRequestWithTimeout = async (operation: () => Promise<any>, timeout: number = 5000) => {
        return Promise.race([
          operation(),
          createTimeoutPromise(timeout)
        ])
      }

      const slowOperation = () => new Promise(resolve => setTimeout(resolve, 10000))
      
      try {
        await makeRequestWithTimeout(slowOperation, 100)
        expect.fail('Should have timed out')
      } catch (error) {
        expect((error as Error).message).toBe('Request timeout')
      }
    })
  })
})

/**
 * Error handling utilities for consistent error management
 */
export const ErrorHandlingUtils = {
  /**
   * Classify error type based on error message or properties
   */
  classifyError: (error: Error): string => {
    const message = error.message.toLowerCase()
    
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return 'network'
    }
    if (message.includes('timeout')) {
      return 'timeout'
    }
    if (message.includes('500') || message.includes('502') || message.includes('503')) {
      return 'server'
    }
    if (message.includes('401') || message.includes('403')) {
      return 'auth'
    }
    if (message.includes('400')) {
      return 'validation'
    }
    return 'unknown'
  },

  /**
   * Get user-friendly error message
   */
  getUserFriendlyMessage: (error: Error): string => {
    const errorType = ErrorHandlingUtils.classifyError(error)
    
    const messages = {
      network: 'Unable to connect to the server. Please check your internet connection.',
      timeout: 'The request took too long to complete. Please try again.',
      server: 'The server is experiencing issues. Please try again later.',
      auth: 'You are not authorized to perform this action. Please log in again.',
      validation: 'The data provided is invalid. Please check your input.',
      unknown: 'An unexpected error occurred. Please try again.'
    }
    
    return messages[errorType as keyof typeof messages] || messages.unknown
  },

  /**
   * Determine if error should be retried
   */
  shouldRetry: (error: Error): boolean => {
    const errorType = ErrorHandlingUtils.classifyError(error)
    
    // Don't retry auth errors or validation errors
    return !['auth', 'validation'].includes(errorType)
  },

  /**
   * Create retry mechanism with exponential backoff
   */
  createRetryMechanism: (maxRetries: number = 3) => {
    let attempts = 0
    
    return {
      attempt: async (operation: () => Promise<any>) => {
        attempts++
        try {
          return await operation()
        } catch (error) {
          if (attempts < maxRetries && ErrorHandlingUtils.shouldRetry(error as Error)) {
            const delay = Math.pow(2, attempts - 1) * 1000
            await new Promise(resolve => setTimeout(resolve, delay))
            return this.attempt(operation)
          }
          throw error
        }
      },
      getAttempts: () => attempts,
      reset: () => { attempts = 0 }
    }
  },

  /**
   * Log error with context
   */
  logError: (error: Error, context: any) => {
    const errorLog = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      url: typeof window !== 'undefined' ? window.location?.href : 'unknown'
    }
    
    console.error('Error logged:', errorLog)
    
    // In production, send to error monitoring service
    if (typeof window !== 'undefined' && (window as any).errorMonitoring) {
      (window as any).errorMonitoring.captureError(errorLog)
    }
  }
}