import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  useSorting,
  useTableState,
  useDebounce,
  defaultSortValueExtractor,
  isValidDateString,
  formatTableDate,
  formatTableDateTime,
  normalizeError,
  getTableAriaLabel,
  getSortButtonAriaLabel,
  createTableActionHandler,
  validateTableData,
  SortDirection,
  SortConfig,
} from '../table-utils'

// Mock data for testing
interface TestItem {
  id: number
  name: string
  date: string
  createdAt: Date
  status: 'active' | 'inactive'
  count: number
}

const mockData: TestItem[] = [
  {
    id: 1,
    name: 'Alice',
    date: '2024-01-15',
    createdAt: new Date('2024-01-15T10:00:00Z'),
    status: 'active',
    count: 5
  },
  {
    id: 2,
    name: 'Bob',
    date: '2024-01-10',
    createdAt: new Date('2024-01-10T15:30:00Z'),
    status: 'inactive',
    count: 3
  },
  {
    id: 3,
    name: 'Charlie',
    date: '2024-01-20',
    createdAt: new Date('2024-01-20T08:45:00Z'),
    status: 'active',
    count: 8
  }
]

describe('useSorting', () => {
  it('initializes with correct default values', () => {
    const { result } = renderHook(() => 
      useSorting(mockData, 'name', 'asc')
    )

    expect(result.current.sortConfig).toEqual({
      field: 'name',
      direction: 'asc'
    })
    expect(result.current.sortedData).toHaveLength(3)
  })

  it('sorts data correctly by string field', () => {
    const { result } = renderHook(() => 
      useSorting(mockData, 'name', 'asc')
    )

    const sortedNames = result.current.sortedData.map(item => item.name)
    expect(sortedNames).toEqual(['Alice', 'Bob', 'Charlie'])
  })

  it('sorts data correctly by number field', () => {
    const { result } = renderHook(() => 
      useSorting(mockData, 'count', 'desc')
    )

    const sortedCounts = result.current.sortedData.map(item => item.count)
    expect(sortedCounts).toEqual([8, 5, 3])
  })

  it('sorts data correctly by date field', () => {
    const { result } = renderHook(() => 
      useSorting(mockData, 'createdAt', 'asc')
    )

    const sortedDates = result.current.sortedData.map(item => item.createdAt.getTime())
    expect(sortedDates).toEqual([
      new Date('2024-01-10T15:30:00Z').getTime(),
      new Date('2024-01-15T10:00:00Z').getTime(),
      new Date('2024-01-20T08:45:00Z').getTime()
    ])
  })

  it('toggles sort direction when clicking same field', () => {
    const { result } = renderHook(() => 
      useSorting(mockData, 'name', 'asc')
    )

    expect(result.current.sortConfig.direction).toBe('asc')

    act(() => {
      result.current.handleSort('name')
    })

    expect(result.current.sortConfig.direction).toBe('desc')
  })

  it('changes field and resets to asc when clicking different field', () => {
    const { result } = renderHook(() => 
      useSorting(mockData, 'name', 'desc')
    )

    act(() => {
      result.current.handleSort('count')
    })

    expect(result.current.sortConfig).toEqual({
      field: 'count',
      direction: 'asc'
    })
  })

  it('uses custom value extractor', () => {
    const customExtractor = (item: TestItem, field: keyof TestItem) => {
      if (field === 'name') {
        return item.name.length // Sort by name length instead of alphabetically
      }
      return defaultSortValueExtractor(item, field)
    }

    const { result } = renderHook(() => 
      useSorting(mockData, 'name', 'asc', customExtractor)
    )

    const sortedNames = result.current.sortedData.map(item => item.name)
    expect(sortedNames).toEqual(['Bob', 'Alice', 'Charlie']) // Sorted by length: 3, 5, 7
  })

  it('handles empty data array', () => {
    const { result } = renderHook(() => 
      useSorting([], 'name' as keyof TestItem, 'asc')
    )

    expect(result.current.sortedData).toEqual([])
  })
})

describe('useTableState', () => {
  it('initializes with correct default values', () => {
    const { result } = renderHook(() => 
      useTableState<TestItem>(mockData, 'name', 'asc')
    )

    expect(result.current.state).toEqual({
      data: mockData,
      loading: false,
      error: null,
      sortConfig: { field: 'name', direction: 'asc' },
      retryCount: 0,
      isRetrying: false
    })
  })

  it('updates data correctly', () => {
    const { result } = renderHook(() => 
      useTableState<TestItem>([], 'name', 'asc')
    )

    act(() => {
      result.current.setData(mockData)
    })

    expect(result.current.state.data).toEqual(mockData)
  })

  it('updates loading state correctly', () => {
    const { result } = renderHook(() => 
      useTableState<TestItem>([], 'name', 'asc')
    )

    act(() => {
      result.current.setLoading(true)
    })

    expect(result.current.state.loading).toBe(true)
  })

  it('updates error state correctly', () => {
    const { result } = renderHook(() => 
      useTableState<TestItem>([], 'name', 'asc')
    )

    const error = new Error('Test error')

    act(() => {
      result.current.setError(error)
    })

    expect(result.current.state.error).toBe(error)
  })

  it('increments retry count', () => {
    const { result } = renderHook(() => 
      useTableState<TestItem>([], 'name', 'asc')
    )

    act(() => {
      result.current.incrementRetry()
    })

    expect(result.current.state.retryCount).toBe(1)

    act(() => {
      result.current.incrementRetry()
    })

    expect(result.current.state.retryCount).toBe(2)
  })

  it('resets retry state when clearing error', () => {
    const { result } = renderHook(() => 
      useTableState<TestItem>([], 'name', 'asc')
    )

    act(() => {
      result.current.setError('Test error')
      result.current.incrementRetry()
      result.current.setIsRetrying(true)
    })

    expect(result.current.state.retryCount).toBe(1)
    expect(result.current.state.isRetrying).toBe(true)

    act(() => {
      result.current.clearError()
    })

    expect(result.current.state.error).toBeNull()
    expect(result.current.state.retryCount).toBe(0)
    expect(result.current.state.isRetrying).toBe(false)
  })

  it('updates sort config correctly', () => {
    const { result } = renderHook(() => 
      useTableState<TestItem>([], 'name', 'asc')
    )

    const newSortConfig: SortConfig<TestItem> = { field: 'count', direction: 'desc' }

    act(() => {
      result.current.setSortConfig(newSortConfig)
    })

    expect(result.current.state.sortConfig).toEqual(newSortConfig)
  })
})

describe('useDebounce', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('returns initial value immediately', () => {
    const { result } = renderHook(() => useDebounce('initial', 500))
    expect(result.current).toBe('initial')
  })

  it('debounces value changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    )

    expect(result.current).toBe('initial')

    // Change value
    rerender({ value: 'changed', delay: 500 })
    expect(result.current).toBe('initial') // Should still be initial

    // Fast forward time
    act(() => {
      vi.advanceTimersByTime(500)
    })

    expect(result.current).toBe('changed')
  })

  it('cancels previous timeout on rapid changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    )

    // Rapid changes
    rerender({ value: 'change1', delay: 500 })
    act(() => {
      vi.advanceTimersByTime(250)
    })

    rerender({ value: 'change2', delay: 500 })
    act(() => {
      vi.advanceTimersByTime(250)
    })

    expect(result.current).toBe('initial') // Should still be initial

    // Complete the debounce
    act(() => {
      vi.advanceTimersByTime(250)
    })

    expect(result.current).toBe('change2') // Should be the latest value
  })
})

describe('defaultSortValueExtractor', () => {
  it('handles null and undefined values', () => {
    const item = { value: null }
    expect(defaultSortValueExtractor(item, 'value')).toBe('')

    const item2 = { value: undefined }
    expect(defaultSortValueExtractor(item2, 'value')).toBe('')
  })

  it('handles Date objects', () => {
    const date = new Date('2024-01-15')
    const item = { date }
    expect(defaultSortValueExtractor(item, 'date')).toBe(date.getTime())
  })

  it('handles date strings', () => {
    const item = { date: '2024-01-15' }
    const result = defaultSortValueExtractor(item, 'date')
    expect(typeof result).toBe('number')
    expect(result).toBeGreaterThan(0)
  })

  it('handles regular strings (case-insensitive)', () => {
    const item = { name: 'Alice' }
    expect(defaultSortValueExtractor(item, 'name')).toBe('alice')
  })

  it('handles numbers', () => {
    const item = { count: 42 }
    expect(defaultSortValueExtractor(item, 'count')).toBe(42)
  })

  it('handles other primitive types', () => {
    const item = { flag: true }
    expect(defaultSortValueExtractor(item, 'flag')).toBe(true)
  })
})

describe('isValidDateString', () => {
  it('recognizes ISO date strings', () => {
    expect(isValidDateString('2024-01-15')).toBe(true)
    expect(isValidDateString('2024-12-31T23:59:59Z')).toBe(true)
  })

  it('recognizes US date format', () => {
    expect(isValidDateString('01/15/2024')).toBe(true)
    expect(isValidDateString('12/31/2024')).toBe(true)
  })

  it('recognizes European date format', () => {
    expect(isValidDateString('15-01-2024')).toBe(true)
    expect(isValidDateString('31-12-2024')).toBe(true)
  })

  it('rejects non-date strings', () => {
    expect(isValidDateString('hello')).toBe(false)
    expect(isValidDateString('123')).toBe(false)
    expect(isValidDateString('')).toBe(false)
  })
})

describe('formatTableDate', () => {
  it('formats valid dates correctly', () => {
    const date = new Date('2024-01-15T10:30:00Z')
    expect(formatTableDate(date)).toBe('2024-01-15')
  })

  it('formats date strings correctly', () => {
    expect(formatTableDate('2024-01-15T10:30:00Z')).toBe('2024-01-15')
  })

  it('handles null and undefined', () => {
    expect(formatTableDate(null)).toBe('N/A')
    expect(formatTableDate(undefined)).toBe('N/A')
  })

  it('handles invalid dates', () => {
    expect(formatTableDate('invalid-date')).toBe('Invalid date')
    expect(formatTableDate(new Date('invalid'))).toBe('Invalid date')
  })
})

describe('formatTableDateTime', () => {
  it('formats valid dates correctly', () => {
    const date = new Date('2024-01-15T10:30:00Z')
    const result = formatTableDateTime(date)
    expect(result).toMatch(/2024-01-15 \d{1,2}:\d{2} [AP]M/)
  })

  it('handles null and undefined', () => {
    expect(formatTableDateTime(null)).toBe('N/A')
    expect(formatTableDateTime(undefined)).toBe('N/A')
  })

  it('handles invalid dates', () => {
    expect(formatTableDateTime('invalid-date')).toBe('Invalid date')
  })
})

describe('normalizeError', () => {
  it('returns Error objects as-is', () => {
    const error = new Error('Test error')
    expect(normalizeError(error)).toBe(error)
  })

  it('returns strings as-is', () => {
    expect(normalizeError('Test error')).toBe('Test error')
  })

  it('extracts message from objects with message property', () => {
    const errorObj = { message: 'Test error', code: 500 }
    expect(normalizeError(errorObj)).toBe('Test error')
  })

  it('handles unknown error types', () => {
    expect(normalizeError(null)).toBe('An unexpected error occurred')
    expect(normalizeError(undefined)).toBe('An unexpected error occurred')
    expect(normalizeError(123)).toBe('An unexpected error occurred')
  })
})

describe('getTableAriaLabel', () => {
  it('generates basic aria label', () => {
    const label = getTableAriaLabel('booking', 5)
    expect(label).toBe('booking table with 5 items')
  })

  it('handles singular item count', () => {
    const label = getTableAriaLabel('booking', 1)
    expect(label).toBe('booking table with 1 item')
  })

  it('includes sort information', () => {
    const label = getTableAriaLabel('booking', 5, 'name', 'asc')
    expect(label).toBe('booking table with 5 items, sorted by name ascending')
  })

  it('handles descending sort', () => {
    const label = getTableAriaLabel('booking', 5, 'date', 'desc')
    expect(label).toBe('booking table with 5 items, sorted by date descending')
  })
})

describe('getSortButtonAriaLabel', () => {
  it('generates label for inactive sort field', () => {
    const label = getSortButtonAriaLabel('name')
    expect(label).toBe('Sort by name ascending')
  })

  it('generates label for active ascending field', () => {
    const label = getSortButtonAriaLabel('name', 'name', 'asc')
    expect(label).toBe('Sort by name descending')
  })

  it('generates label for active descending field', () => {
    const label = getSortButtonAriaLabel('name', 'name', 'desc')
    expect(label).toBe('Sort by name ascending')
  })

  it('generates label for different active field', () => {
    const label = getSortButtonAriaLabel('name', 'date', 'asc')
    expect(label).toBe('Sort by name ascending')
  })
})

describe('createTableActionHandler', () => {
  it('executes action successfully', async () => {
    const mockAction = vi.fn().mockResolvedValue(undefined)
    const handler = createTableActionHandler(mockAction)
    const item = { id: 1, name: 'Test' }

    await handler(item)

    expect(mockAction).toHaveBeenCalledWith(item)
  })

  it('handles action errors', async () => {
    const mockAction = vi.fn().mockRejectedValue(new Error('Action failed'))
    const mockOnError = vi.fn()
    const handler = createTableActionHandler(mockAction, mockOnError)
    const item = { id: 1, name: 'Test' }

    await handler(item)

    expect(mockOnError).toHaveBeenCalledWith(expect.any(Error))
  })

  it('handles synchronous actions', async () => {
    const mockAction = vi.fn()
    const handler = createTableActionHandler(mockAction)
    const item = { id: 1, name: 'Test' }

    await handler(item)

    expect(mockAction).toHaveBeenCalledWith(item)
  })

  it('converts non-Error exceptions to Error objects', async () => {
    const mockAction = vi.fn().mockRejectedValue('String error')
    const mockOnError = vi.fn()
    const handler = createTableActionHandler(mockAction, mockOnError)
    const item = { id: 1, name: 'Test' }

    await handler(item)

    expect(mockOnError).toHaveBeenCalledWith(expect.any(Error))
    expect(mockOnError).toHaveBeenCalledWith(
      expect.objectContaining({ message: 'String error' })
    )
  })
})

describe('validateTableData', () => {
  it('validates correct data structure', () => {
    const data = [
      { id: 1, name: 'Alice' },
      { id: 2, name: 'Bob' }
    ]
    const requiredFields = ['id', 'name'] as const

    expect(validateTableData(data, requiredFields)).toBe(true)
  })

  it('rejects non-array data', () => {
    const data = { id: 1, name: 'Alice' }
    const requiredFields = ['id', 'name'] as const

    expect(validateTableData(data, requiredFields)).toBe(false)
  })

  it('rejects data with missing required fields', () => {
    const data = [
      { id: 1, name: 'Alice' },
      { id: 2 } // Missing name field
    ]
    const requiredFields = ['id', 'name'] as const

    expect(validateTableData(data, requiredFields)).toBe(false)
  })

  it('rejects data with null/undefined items', () => {
    const data = [
      { id: 1, name: 'Alice' },
      null
    ]
    const requiredFields = ['id', 'name'] as const

    expect(validateTableData(data, requiredFields)).toBe(false)
  })

  it('rejects data with non-object items', () => {
    const data = [
      { id: 1, name: 'Alice' },
      'invalid item'
    ]
    const requiredFields = ['id', 'name'] as const

    expect(validateTableData(data, requiredFields)).toBe(false)
  })

  it('handles empty arrays', () => {
    const data: any[] = []
    const requiredFields = ['id', 'name'] as const

    expect(validateTableData(data, requiredFields)).toBe(true)
  })
})