import { useState, useCallback } from "react"

/**
 * Enhanced retry mechanism with exponential backoff, circuit breaker pattern,
 * and comprehensive error categorization for the booking management system
 */

export interface RetryOptions {
  maxAttempts?: number
  baseDelay?: number
  maxDelay?: number
  exponentialFactor?: number
  jitter?: boolean
  abortSignal?: AbortSignal
  onRetry?: (error: Error, attempt: number, delay: number) => void
  shouldRetry?: (error: Error, attempt: number) => boolean
  timeout?: number
}

export interface RetryResult<T> {
  success: boolean
  data?: T
  error?: Error
  attempts: number
  totalTime: number
  aborted: boolean
}

export type ErrorCategory = 'network' | 'server' | 'auth' | 'validation' | 'timeout' | 'abort' | 'unknown'

export interface ErrorAnalysis {
  category: ErrorCategory
  severity: 'low' | 'medium' | 'high' | 'critical'
  retryable: boolean
  userMessage: string
  technicalMessage: string
  suggestedAction?: string
}

/**
 * Enhanced error categorization for booking system errors
 */
export function analyzeError(error: Error | string): ErrorAnalysis {
  const message = typeof error === 'string' ? error : error.message
  const lowerMessage = message.toLowerCase()
  const stack = typeof error === 'object' ? error.stack : undefined

  // Abort errors - not retryable
  if (lowerMessage.includes('aborted') || lowerMessage.includes('aborterror')) {
    return {
      category: 'abort',
      severity: 'low',
      retryable: false,
      userMessage: 'Request was cancelled',
      technicalMessage: message,
      suggestedAction: 'Try the operation again'
    }
  }

  // Network connectivity errors - highly retryable
  if (lowerMessage.includes('network') || lowerMessage.includes('fetch failed') || 
      lowerMessage.includes('connection') || lowerMessage.includes('network error') ||
      !navigator.onLine) {
    return {
      category: 'network',
      severity: 'high',
      retryable: true,
      userMessage: navigator.onLine 
        ? 'Network connection issue detected'
        : 'You appear to be offline',
      technicalMessage: message,
      suggestedAction: 'Check your internet connection and try again'
    }
  }

  // Timeout errors - retryable with caution
  if (lowerMessage.includes('timeout') || lowerMessage.includes('timed out')) {
    return {
      category: 'timeout',
      severity: 'medium',
      retryable: true,
      userMessage: 'Request timed out',
      technicalMessage: message,
      suggestedAction: 'The server is taking longer than expected. Try again in a moment'
    }
  }

  // Server errors (5xx) - retryable
  if (lowerMessage.includes('500') || lowerMessage.includes('502') || 
      lowerMessage.includes('503') || lowerMessage.includes('504') ||
      lowerMessage.includes('server error') || lowerMessage.includes('internal server')) {
    return {
      category: 'server',
      severity: 'high',
      retryable: true,
      userMessage: 'Server error detected',
      technicalMessage: message,
      suggestedAction: 'Our team has been notified. Please try again in a moment'
    }
  }

  // Authentication errors (401, 403) - not retryable
  if (lowerMessage.includes('401') || lowerMessage.includes('403') || 
      lowerMessage.includes('unauthorized') || lowerMessage.includes('forbidden')) {
    return {
      category: 'auth',
      severity: 'medium',
      retryable: false,
      userMessage: 'Authentication required',
      technicalMessage: message,
      suggestedAction: 'Please refresh the page and try again'
    }
  }

  // Validation errors (400, 422) - not retryable
  if (lowerMessage.includes('400') || lowerMessage.includes('422') || 
      lowerMessage.includes('validation') || lowerMessage.includes('invalid') ||
      lowerMessage.includes('bad request')) {
    return {
      category: 'validation',
      severity: 'medium',
      retryable: false,
      userMessage: 'Request validation failed',
      technicalMessage: message,
      suggestedAction: 'Please check your input and try again'
    }
  }

  // Unknown errors - retryable with caution
  return {
    category: 'unknown',
    severity: 'medium',
    retryable: true,
    userMessage: 'An unexpected error occurred',
    technicalMessage: message,
    suggestedAction: 'Please try again. If the problem persists, contact support'
  }
}

/**
 * Circuit breaker implementation to prevent cascading failures
 */
class CircuitBreaker {
  private static instances = new Map<string, CircuitBreaker>()
  
  private state: 'closed' | 'open' | 'half-open' = 'closed'
  private failureCount = 0
  private lastFailureTime: number | null = null
  private readonly threshold: number
  private readonly timeout: number

  constructor(private key: string, threshold = 5, timeout = 30000) {
    this.threshold = threshold
    this.timeout = timeout
  }

  static getInstance(key: string, threshold?: number, timeout?: number): CircuitBreaker {
    if (!CircuitBreaker.instances.has(key)) {
      CircuitBreaker.instances.set(key, new CircuitBreaker(key, threshold, timeout))
    }
    return CircuitBreaker.instances.get(key)!
  }

  canExecute(): boolean {
    if (this.state === 'closed') {
      return true
    }

    if (this.state === 'open') {
      const now = Date.now()
      if (this.lastFailureTime && (now - this.lastFailureTime) >= this.timeout) {
        this.state = 'half-open'
        this.failureCount = 0
        return true
      }
      return false
    }

    // half-open state - allow one attempt
    return true
  }

  onSuccess(): void {
    this.state = 'closed'
    this.failureCount = 0
    this.lastFailureTime = null
  }

  onFailure(): void {
    this.failureCount++
    this.lastFailureTime = Date.now()

    if (this.failureCount >= this.threshold) {
      this.state = 'open'
    }
  }

  getState() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime
    }
  }
}

/**
 * Enhanced retry mechanism with circuit breaker and comprehensive error handling
 */
export async function enhancedRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {},
  circuitBreakerKey?: string
): Promise<RetryResult<T>> {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 30000,
    exponentialFactor = 2,
    jitter = true,
    abortSignal,
    onRetry,
    shouldRetry,
    timeout = 30000
  } = options

  const startTime = Date.now()
  let lastError: Error | null = null
  let attempts = 0
  
  // Initialize circuit breaker if key provided
  const circuitBreaker = circuitBreakerKey 
    ? CircuitBreaker.getInstance(circuitBreakerKey)
    : null

  // Check circuit breaker before starting
  if (circuitBreaker && !circuitBreaker.canExecute()) {
    const breakerState = circuitBreaker.getState()
    const error = new Error(
      `Circuit breaker is ${breakerState.state}. Too many recent failures (${breakerState.failureCount})`
    )
    return {
      success: false,
      error,
      attempts: 0,
      totalTime: Date.now() - startTime,
      aborted: false
    }
  }

  while (attempts < maxAttempts) {
    attempts++

    try {
      // Check for abort signal
      if (abortSignal?.aborted) {
        return {
          success: false,
          error: new Error('Operation aborted'),
          attempts,
          totalTime: Date.now() - startTime,
          aborted: true
        }
      }

      // Create timeout promise if timeout specified
      let timeoutId: NodeJS.Timeout | null = null
      const timeoutPromise = timeout > 0 ? new Promise<never>((_, reject) => {
        timeoutId = setTimeout(() => {
          reject(new Error(`Operation timed out after ${timeout}ms`))
        }, timeout)
      }) : null

      // Execute operation with optional timeout
      const operationPromise = operation()
      const result = timeoutPromise 
        ? await Promise.race([operationPromise, timeoutPromise])
        : await operationPromise

      // Clear timeout if operation completed
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      // Success - update circuit breaker and return
      circuitBreaker?.onSuccess()
      
      return {
        success: true,
        data: result,
        attempts,
        totalTime: Date.now() - startTime,
        aborted: false
      }

    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))
      
      // Update circuit breaker on failure
      circuitBreaker?.onFailure()

      // Analyze error to determine if retry should be attempted
      const errorAnalysis = analyzeError(lastError)
      
      // Check if we should retry based on error analysis and custom logic
      const defaultShouldRetry = errorAnalysis.retryable && attempts < maxAttempts
      const customShouldRetry = shouldRetry ? shouldRetry(lastError, attempts) : defaultShouldRetry
      
      if (!customShouldRetry) {
        break
      }

      // Don't delay on the last attempt
      if (attempts < maxAttempts) {
        // Calculate delay with exponential backoff
        let delay = Math.min(
          baseDelay * Math.pow(exponentialFactor, attempts - 1),
          maxDelay
        )

        // Add jitter to prevent thundering herd
        if (jitter) {
          delay += Math.random() * delay * 0.1
        }

        // Call retry callback
        onRetry?.(lastError, attempts, delay)

        // Wait before retrying, checking for abort signal
        await new Promise<void>((resolve, reject) => {
          const delayTimeoutId = setTimeout(resolve, delay)
          
          if (abortSignal) {
            const abortHandler = () => {
              clearTimeout(delayTimeoutId)
              reject(new Error('Operation aborted during retry delay'))
            }
            
            abortSignal.addEventListener('abort', abortHandler, { once: true })
            
            // Clean up listener after timeout
            setTimeout(() => {
              abortSignal.removeEventListener('abort', abortHandler)
            }, delay + 100)
          }
        })
      }
    }
  }

  return {
    success: false,
    error: lastError || new Error('Operation failed'),
    attempts,
    totalTime: Date.now() - startTime,
    aborted: false
  }
}

/**
 * Specialized retry function for API calls with booking-specific error handling
 */
export async function retryApiCall<T>(
  apiCall: () => Promise<Response>,
  options: RetryOptions = {}
): Promise<Response> {
  const result = await enhancedRetry(
    async () => {
      const response = await apiCall()
      
      // Check if response indicates a retryable error
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        const error = new Error(`HTTP ${response.status}: ${errorText}`)
        throw error
      }
      
      return response
    },
    {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      ...options
    },
    'api-calls' // Circuit breaker key for API calls
  )

  if (!result.success) {
    throw result.error || new Error('API call failed after retries')
  }

  return result.data!
}

/**
 * Hook for React components to use enhanced retry with state management
 */
export function useEnhancedRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
) {
  const [isRetrying, setIsRetrying] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [lastError, setLastError] = useState<Error | null>(null)

  const retry = useCallback(async (): Promise<RetryResult<T>> => {
    setIsRetrying(true)
    setLastError(null)
    
    const result = await enhancedRetry(
      operation,
      {
        ...options,
        onRetry: (error, attempt, delay) => {
          setRetryCount(attempt)
          options.onRetry?.(error, attempt, delay)
        }
      }
    )
    
    setIsRetrying(false)
    setRetryCount(result.attempts)
    
    if (!result.success) {
      setLastError(result.error || null)
    }
    
    return result
  }, [operation, options])

  return {
    retry,
    isRetrying,
    retryCount,
    lastError,
    reset: useCallback(() => {
      setIsRetrying(false)
      setRetryCount(0)
      setLastError(null)
    }, [])
  }
}