/**
 * Shared styling patterns and constants for consistent table design
 * across all dashboard table components
 */

/**
 * Responsive breakpoints for table components
 * These should match the Tailwind CSS breakpoints used throughout the app
 */
export const TABLE_BREAKPOINTS = {
  // Mobile-first approach
  mobile: '(max-width: 767px)',
  tablet: '(min-width: 768px) and (max-width: 1023px)', 
  desktop: '(min-width: 1024px)',
  largeDesktop: '(min-width: 1280px)',
} as const

/**
 * Spacing constants for consistent table layout
 */
export const TABLE_SPACING = {
  // Mobile-specific spacing
  mobileCardGap: 'space-y-4',
  mobileCardPadding: 'p-4',
  mobileControlsPadding: 'p-4',
  mobileHeaderSpacing: 'mb-4',
  
  // Desktop table spacing
  desktopCellPadding: 'px-4 py-3',
  desktopHeaderPadding: 'px-4 py-3',
  desktopTableMargin: 'mb-6',
  
  // General spacing
  sectionGap: 'space-y-6',
  controlsGap: 'space-x-2',
  badgeGap: 'gap-1',
  iconGap: 'gap-2',
} as const

/**
 * Touch target sizes for mobile accessibility
 * Following WCAG guidelines for minimum touch target size (44px)
 */
export const TOUCH_TARGETS = {
  minimum: 'min-h-[44px] min-w-[44px]',
  button: 'h-10 min-w-[44px]',
  smallButton: 'h-8 min-w-[36px]',
  iconButton: 'h-10 w-10',
  smallIconButton: 'h-8 w-8',
  dropdownTrigger: 'h-10 min-w-[44px]',
  sortButton: 'h-auto p-2 min-w-[44px]',
} as const

/**
 * Focus and interaction states for accessibility
 */
export const FOCUS_STYLES = {
  enhanced: 'focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:outline-none',
  subtle: 'focus:ring-1 focus:ring-primary focus:ring-offset-1 focus:outline-none',
  inset: 'focus:ring-2 focus:ring-inset focus:ring-primary focus:outline-none',
  visible: 'focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
} as const

/**
 * Loading state styles and animations
 */
export const LOADING_STYLES = {
  skeleton: 'animate-pulse bg-muted rounded',
  shimmer: 'animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted',
  spinner: 'animate-spin',
  pulse: 'animate-pulse',
  
  // Skeleton dimensions
  skeletonText: 'h-4 bg-muted rounded animate-pulse',
  skeletonTitle: 'h-5 bg-muted rounded animate-pulse',
  skeletonButton: 'h-8 w-16 bg-muted rounded animate-pulse',
  skeletonBadge: 'h-6 w-12 bg-muted rounded-full animate-pulse',
  skeletonAvatar: 'h-8 w-8 bg-muted rounded-full animate-pulse',
} as const

/**
 * Error state styling
 */
export const ERROR_STYLES = {
  // Border colors
  errorBorder: 'border-red-200',
  warningBorder: 'border-yellow-200',
  infoBorder: 'border-blue-200',
  
  // Background colors
  errorBackground: 'bg-red-50',
  warningBackground: 'bg-yellow-50',
  infoBackground: 'bg-blue-50',
  
  // Text colors
  errorText: 'text-red-800',
  warningText: 'text-yellow-800',
  infoText: 'text-blue-800',
  
  // Muted text colors
  errorTextMuted: 'text-red-600',
  warningTextMuted: 'text-yellow-600',
  infoTextMuted: 'text-blue-600',
} as const

/**
 * Status badge color schemes
 */
export const STATUS_COLORS = {
  // Success states
  success: 'bg-green-100 border-green-300 text-green-800',
  confirmed: 'bg-green-100 border-green-300 text-green-800',
  active: 'bg-green-100 border-green-300 text-green-800',
  paid: 'bg-green-100 border-green-300 text-green-800',
  
  // Warning states
  warning: 'bg-yellow-100 border-yellow-300 text-yellow-800',
  pending: 'bg-yellow-100 border-yellow-300 text-yellow-800',
  partial: 'bg-yellow-100 border-yellow-300 text-yellow-800',
  
  // Error states
  error: 'bg-red-100 border-red-300 text-red-800',
  cancelled: 'bg-red-100 border-red-300 text-red-800',
  failed: 'bg-red-100 border-red-300 text-red-800',
  
  // Neutral states
  neutral: 'bg-gray-100 border-gray-300 text-gray-800',
  inactive: 'bg-gray-100 border-gray-300 text-gray-800',
  draft: 'bg-gray-100 border-gray-300 text-gray-800',
  
  // Info states
  info: 'bg-blue-100 border-blue-300 text-blue-800',
  processing: 'bg-blue-100 border-blue-300 text-blue-800',
} as const

/**
 * Responsive visibility classes
 */
export const RESPONSIVE_VISIBILITY = {
  // Hide on mobile, show on desktop
  hiddenMobile: 'hidden md:block',
  hiddenMobileInline: 'hidden md:inline',
  hiddenMobileFlex: 'hidden md:flex',
  
  // Show on mobile, hide on desktop
  showMobile: 'block md:hidden',
  showMobileInline: 'inline md:hidden',
  showMobileFlex: 'flex md:hidden',
  
  // Hide on tablet
  hiddenTablet: 'hidden lg:table-cell',
  hiddenTabletBlock: 'hidden lg:block',
  
  // Show only on desktop
  desktopOnly: 'hidden lg:block',
  desktopOnlyFlex: 'hidden lg:flex',
  desktopOnlyInline: 'hidden lg:inline',
} as const

/**
 * Table-specific layout classes
 */
export const TABLE_LAYOUT = {
  // Container classes
  tableContainer: 'rounded-md border overflow-hidden',
  tableWrapper: 'overflow-x-auto',
  mobileContainer: 'space-y-4',
  
  // Table structure
  table: 'w-full',
  tableHeader: 'border-b bg-muted/50',
  tableBody: '',
  tableRow: 'border-b transition-colors hover:bg-muted/50',
  tableCell: 'px-4 py-3 text-left',
  tableHeaderCell: 'px-4 py-3 text-left font-medium',
  
  // Card layout for mobile
  card: 'rounded-lg border bg-card text-card-foreground shadow-sm',
  cardHeader: 'flex flex-row items-center justify-between space-y-0 pb-2',
  cardContent: 'pt-0',
  cardTitle: 'text-sm font-medium',
  cardDescription: 'text-xs text-muted-foreground',
  
  // Empty states
  emptyState: 'flex flex-col items-center justify-center py-12 text-center',
  emptyStateIcon: 'h-12 w-12 text-muted-foreground mb-4',
  emptyStateTitle: 'text-lg font-medium mb-2',
  emptyStateDescription: 'text-sm text-muted-foreground mb-4',
} as const

/**
 * Sort control styling
 */
export const SORT_STYLES = {
  // Sort buttons
  sortButton: 'h-auto p-2 font-semibold hover:bg-muted/50 transition-colors',
  sortButtonActive: 'bg-muted/50',
  
  // Sort icons
  sortIcon: 'ml-2 h-4 w-4',
  sortIconActive: 'text-primary',
  
  // Mobile sort controls
  mobileSortContainer: 'flex items-center justify-between mb-4 p-4 bg-muted/50 rounded-lg',
  mobileSortLabel: 'text-sm font-medium',
  mobileSortDropdown: 'min-w-[120px]',
} as const

/**
 * Action menu styling
 */
export const ACTION_STYLES = {
  // Dropdown menus
  dropdownTrigger: 'h-10 w-10 p-0',
  dropdownContent: 'w-48',
  dropdownItem: 'cursor-pointer py-3 px-2',
  dropdownItemDestructive: 'text-destructive cursor-pointer py-3 px-2',
  
  // Action buttons
  actionButton: 'h-8 px-3 text-xs',
  primaryAction: 'bg-primary text-primary-foreground hover:bg-primary/90',
  secondaryAction: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
  destructiveAction: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
} as const

/**
 * Typography scales for table content
 */
export const TABLE_TYPOGRAPHY = {
  // Headers
  tableTitle: 'text-2xl font-bold tracking-tight',
  sectionTitle: 'text-lg font-semibold',
  columnHeader: 'text-sm font-medium',
  
  // Content
  cellText: 'text-sm',
  cellTextMuted: 'text-sm text-muted-foreground',
  cellTextBold: 'text-sm font-medium',
  
  // Mobile card content
  cardTitle: 'text-base font-medium',
  cardSubtitle: 'text-sm text-muted-foreground',
  cardMeta: 'text-xs text-muted-foreground',
  
  // Labels and badges
  badgeText: 'text-xs font-medium',
  labelText: 'text-xs font-medium text-muted-foreground',
} as const

/**
 * Animation and transition classes
 */
export const ANIMATIONS = {
  // Standard transitions
  transition: 'transition-colors duration-200',
  transitionAll: 'transition-all duration-200',
  transitionFast: 'transition-colors duration-150',
  
  // Hover effects
  hoverScale: 'hover:scale-105 transition-transform duration-200',
  hoverBg: 'hover:bg-muted/50 transition-colors duration-200',
  hoverShadow: 'hover:shadow-md transition-shadow duration-200',
  
  // Loading animations
  fadeIn: 'animate-in fade-in duration-200',
  slideIn: 'animate-in slide-in-from-top-2 duration-200',
  scaleIn: 'animate-in zoom-in-95 duration-200',
} as const

/**
 * Z-index layers for proper stacking
 */
export const Z_INDEX = {
  dropdown: 'z-50',
  modal: 'z-50',
  tooltip: 'z-50',
  overlay: 'z-40',
  sticky: 'z-30',
  header: 'z-20',
  content: 'z-10',
} as const

/**
 * Utility function to combine table classes
 */
export const combineTableClasses = (...classes: (string | undefined | false)[]): string => {
  return classes.filter(Boolean).join(' ')
}

/**
 * Get status color based on status value
 */
export const getStatusColor = (status: string): string => {
  const normalizedStatus = status.toLowerCase()
  
  // Map common status values to color schemes
  const statusMap: Record<string, string> = {
    // Success variants
    'confirmed': STATUS_COLORS.confirmed,
    'active': STATUS_COLORS.active,
    'paid': STATUS_COLORS.paid,
    'completed': STATUS_COLORS.success,
    'approved': STATUS_COLORS.success,
    
    // Warning variants
    'pending': STATUS_COLORS.pending,
    'partial': STATUS_COLORS.partial,
    'partially_paid': STATUS_COLORS.partial,
    'in_progress': STATUS_COLORS.warning,
    'review': STATUS_COLORS.warning,
    
    // Error variants
    'cancelled': STATUS_COLORS.cancelled,
    'failed': STATUS_COLORS.failed,
    'rejected': STATUS_COLORS.error,
    'expired': STATUS_COLORS.error,
    
    // Neutral variants
    'inactive': STATUS_COLORS.inactive,
    'draft': STATUS_COLORS.draft,
    'disabled': STATUS_COLORS.neutral,
    
    // Info variants
    'processing': STATUS_COLORS.processing,
    'scheduled': STATUS_COLORS.info,
  }
  
  return statusMap[normalizedStatus] || STATUS_COLORS.neutral
}

/**
 * Get responsive column classes based on priority
 */
export const getColumnVisibility = (priority: 'high' | 'medium' | 'low'): string => {
  switch (priority) {
    case 'high':
      return '' // Always visible
    case 'medium':
      return RESPONSIVE_VISIBILITY.hiddenMobile // Hidden on mobile
    case 'low':
      return RESPONSIVE_VISIBILITY.hiddenTablet // Hidden on mobile and tablet
    default:
      return ''
  }
}

/**
 * Generate consistent table cell classes
 */
export const getTableCellClasses = (options: {
  align?: 'left' | 'center' | 'right'
  priority?: 'high' | 'medium' | 'low'
  type?: 'text' | 'number' | 'date' | 'status' | 'actions'
} = {}): string => {
  const { align = 'left', priority = 'high', type = 'text' } = options
  
  const classes = [
    TABLE_LAYOUT.tableCell,
    getColumnVisibility(priority),
  ]
  
  // Alignment
  if (align === 'center') classes.push('text-center')
  if (align === 'right') classes.push('text-right')
  
  // Type-specific styling
  if (type === 'number') classes.push('font-mono')
  if (type === 'actions') classes.push('w-[70px] min-w-[70px]')
  if (type === 'status') classes.push('min-w-[100px]')
  
  return combineTableClasses(...classes)
}

/**
 * Generate consistent mobile card classes
 */
export const getMobileCardClasses = (variant: 'default' | 'compact' | 'detailed' = 'default'): string => {
  const baseClasses: string[] = [
    TABLE_LAYOUT.card,
    TABLE_SPACING.mobileCardPadding,
    ANIMATIONS.transition,
    ANIMATIONS.hoverBg,
  ]
  
  if (variant === 'compact') {
    baseClasses.push('p-3')
  } else if (variant === 'detailed') {
    baseClasses.push('p-5')
  }
  
  return combineTableClasses(...baseClasses)
}

/**
 * Export all styling utilities as a single object
 */
export const TableStyles = {
  // Constants
  BREAKPOINTS: TABLE_BREAKPOINTS,
  SPACING: TABLE_SPACING,
  TOUCH_TARGETS,
  FOCUS_STYLES,
  LOADING_STYLES,
  ERROR_STYLES,
  STATUS_COLORS,
  RESPONSIVE_VISIBILITY,
  TABLE_LAYOUT,
  SORT_STYLES,
  ACTION_STYLES,
  TABLE_TYPOGRAPHY,
  ANIMATIONS,
  Z_INDEX,
  
  // Utilities
  combineTableClasses,
  getStatusColor,
  getColumnVisibility,
  getTableCellClasses,
  getMobileCardClasses,
} as const

/**
 * CSS custom properties for dynamic theming
 * These can be used in CSS-in-JS or CSS variables
 */
export const TABLE_CSS_VARS = {
  '--table-border-radius': '0.5rem',
  '--table-border-width': '1px',
  '--table-cell-padding-x': '1rem',
  '--table-cell-padding-y': '0.75rem',
  '--table-header-height': '3rem',
  '--table-row-height': '3.5rem',
  '--mobile-card-padding': '1rem',
  '--mobile-card-gap': '1rem',
  '--touch-target-size': '44px',
  '--focus-ring-width': '2px',
  '--focus-ring-offset': '2px',
} as const