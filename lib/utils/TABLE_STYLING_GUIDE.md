# Table Styling Guidelines

This document outlines the consistent styling patterns and guidelines for all dashboard table components.

## Overview

All table components should follow these styling patterns to ensure visual consistency, accessibility, and responsive behavior across the application.

## Responsive Design

### Breakpoints

Use the predefined breakpoints for consistent responsive behavior:

```typescript
import { TABLE_BREAKPOINTS } from '@/lib/utils/table-styles'

// Mobile: (max-width: 767px)
// Tablet: (min-width: 768px) and (max-width: 1023px)
// Desktop: (min-width: 1024px)
// Large Desktop: (min-width: 1280px)
```

### Mobile-First Approach

- **Mobile**: Card-based layout with vertical stacking
- **Desktop**: Traditional table layout with columns
- **Responsive Visibility**: Use predefined classes to show/hide content

```typescript
import { RESPONSIVE_VISIBILITY } from '@/lib/utils/table-styles'

// Hide on mobile, show on desktop
className={RESPONSIVE_VISIBILITY.hiddenMobile}

// Show on mobile, hide on desktop  
className={RESPONSIVE_VISIBILITY.showMobile}
```

## Touch Targets and Accessibility

### Minimum Touch Target Size

All interactive elements must meet WCAG guidelines (44px minimum):

```typescript
import { TOUCH_TARGETS } from '@/lib/utils/table-styles'

// Standard button
className={TOUCH_TARGETS.button} // h-10 min-w-[44px]

// Icon button
className={TOUCH_TARGETS.iconButton} // h-10 w-10

// Sort button
className={TOUCH_TARGETS.sortButton} // h-auto p-2 min-w-[44px]
```

### Focus States

Use consistent focus styling for keyboard navigation:

```typescript
import { FOCUS_STYLES } from '@/lib/utils/table-styles'

// Enhanced focus ring (default)
className={FOCUS_STYLES.enhanced}

// Subtle focus ring
className={FOCUS_STYLES.subtle}
```

## Layout Patterns

### Desktop Table Structure

```tsx
<div className={TABLE_LAYOUT.tableContainer}>
  <div className={TABLE_LAYOUT.tableWrapper}>
    <Table className={TABLE_LAYOUT.table}>
      <TableHeader className={TABLE_LAYOUT.tableHeader}>
        <TableRow>
          <TableHead className={TABLE_LAYOUT.tableHeaderCell}>
            {/* Column header content */}
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody className={TABLE_LAYOUT.tableBody}>
        <TableRow className={TABLE_LAYOUT.tableRow}>
          <TableCell className={TABLE_LAYOUT.tableCell}>
            {/* Cell content */}
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  </div>
</div>
```

### Mobile Card Layout

```tsx
<div className={TABLE_LAYOUT.mobileContainer}>
  {items.map(item => (
    <Card key={item.id} className={getMobileCardClasses()}>
      <CardHeader className={TABLE_LAYOUT.cardHeader}>
        <CardTitle className={TABLE_LAYOUT.cardTitle}>
          {/* Card title */}
        </CardTitle>
      </CardHeader>
      <CardContent className={TABLE_LAYOUT.cardContent}>
        {/* Card content */}
      </CardContent>
    </Card>
  ))}
</div>
```

## Status and State Styling

### Status Badges

Use predefined status colors for consistency:

```typescript
import { getStatusColor } from '@/lib/utils/table-styles'

// Automatically maps status to appropriate color
const statusClass = getStatusColor('confirmed') // Returns green styling
const statusClass = getStatusColor('pending')   // Returns yellow styling
const statusClass = getStatusColor('cancelled') // Returns red styling
```

### Loading States

Use consistent loading animations:

```typescript
import { LOADING_STYLES } from '@/lib/utils/table-styles'

// Skeleton loading
className={LOADING_STYLES.skeleton}

// Shimmer effect
className={LOADING_STYLES.shimmer}

// Specific skeleton elements
className={LOADING_STYLES.skeletonText}    // For text content
className={LOADING_STYLES.skeletonButton}  // For buttons
className={LOADING_STYLES.skeletonBadge}   // For badges
```

### Error States

Use consistent error styling:

```typescript
import { ERROR_STYLES } from '@/lib/utils/table-styles'

// Error container
className={`${ERROR_STYLES.errorBorder} ${ERROR_STYLES.errorBackground}`}

// Error text
className={ERROR_STYLES.errorText}

// Warning states
className={`${ERROR_STYLES.warningBorder} ${ERROR_STYLES.warningBackground}`}
```

## Typography

### Table Typography Scale

```typescript
import { TABLE_TYPOGRAPHY } from '@/lib/utils/table-styles'

// Headers
className={TABLE_TYPOGRAPHY.tableTitle}     // Main table title
className={TABLE_TYPOGRAPHY.columnHeader}   // Column headers

// Content
className={TABLE_TYPOGRAPHY.cellText}       // Regular cell text
className={TABLE_TYPOGRAPHY.cellTextMuted}  // Muted cell text
className={TABLE_TYPOGRAPHY.cellTextBold}   // Bold cell text

// Mobile cards
className={TABLE_TYPOGRAPHY.cardTitle}      // Card titles
className={TABLE_TYPOGRAPHY.cardSubtitle}   // Card subtitles
```

## Spacing and Layout

### Consistent Spacing

```typescript
import { TABLE_SPACING } from '@/lib/utils/table-styles'

// Mobile spacing
className={TABLE_SPACING.mobileCardGap}      // space-y-4
className={TABLE_SPACING.mobileCardPadding}  // p-4

// Desktop spacing
className={TABLE_SPACING.desktopCellPadding} // px-4 py-3

// General spacing
className={TABLE_SPACING.sectionGap}         // space-y-6
className={TABLE_SPACING.controlsGap}        // space-x-2
```

## Sorting Controls

### Desktop Sort Headers

```tsx
<Button
  variant="ghost"
  onClick={() => handleSort('fieldName')}
  className={SORT_STYLES.sortButton}
  aria-label={getSortButtonAriaLabel('fieldName', currentField, currentDirection)}
>
  Column Name
  <ArrowUpDown className={SORT_STYLES.sortIcon} />
</Button>
```

### Mobile Sort Controls

```tsx
<div className={SORT_STYLES.mobileSortContainer}>
  <span className={SORT_STYLES.mobileSortLabel}>Sort by:</span>
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="outline" size="sm" className={TOUCH_TARGETS.button}>
        {currentSortField}
        <ArrowUpDown className={SORT_STYLES.sortIcon} />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent className={SORT_STYLES.mobileSortDropdown}>
      {/* Sort options */}
    </DropdownMenuContent>
  </DropdownMenu>
</div>
```

## Action Menus

### Dropdown Actions

```tsx
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button 
      variant="ghost" 
      className={ACTION_STYLES.dropdownTrigger}
      aria-label={`Actions for ${item.name}`}
    >
      <MoreHorizontal className="h-4 w-4" />
    </Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent className={ACTION_STYLES.dropdownContent}>
    <DropdownMenuItem className={ACTION_STYLES.dropdownItem}>
      Edit
    </DropdownMenuItem>
    <DropdownMenuItem className={ACTION_STYLES.dropdownItemDestructive}>
      Delete
    </DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

## Empty States

### Consistent Empty State Design

```tsx
<div className={TABLE_LAYOUT.emptyState}>
  <Icon className={TABLE_LAYOUT.emptyStateIcon} />
  <h3 className={TABLE_LAYOUT.emptyStateTitle}>No items found</h3>
  <p className={TABLE_LAYOUT.emptyStateDescription}>
    Try adjusting your filters or create a new item
  </p>
  <Button variant="outline" className={TOUCH_TARGETS.button}>
    Refresh
  </Button>
</div>
```

## Animations and Transitions

### Standard Transitions

```typescript
import { ANIMATIONS } from '@/lib/utils/table-styles'

// Standard transition
className={ANIMATIONS.transition}

// Hover effects
className={ANIMATIONS.hoverBg}
className={ANIMATIONS.hoverScale}

// Loading animations
className={ANIMATIONS.fadeIn}
className={ANIMATIONS.slideIn}
```

## Utility Functions

### Combining Classes

```typescript
import { combineTableClasses } from '@/lib/utils/table-styles'

const className = combineTableClasses(
  'base-class',
  condition && 'conditional-class',
  undefined, // Will be filtered out
  'another-class'
)
```

### Dynamic Cell Classes

```typescript
import { getTableCellClasses } from '@/lib/utils/table-styles'

// Standard cell
const cellClass = getTableCellClasses()

// Right-aligned number cell, hidden on mobile
const numberCellClass = getTableCellClasses({
  align: 'right',
  type: 'number',
  priority: 'medium'
})

// Actions cell
const actionsCellClass = getTableCellClasses({
  type: 'actions'
})
```

## Best Practices

### 1. Consistency
- Always use predefined classes and utilities
- Follow the established patterns for new components
- Maintain consistent spacing and typography

### 2. Accessibility
- Use proper ARIA labels and roles
- Ensure adequate color contrast
- Provide keyboard navigation support
- Meet touch target size requirements

### 3. Performance
- Use CSS classes instead of inline styles
- Leverage Tailwind's utility classes for consistency
- Minimize custom CSS when possible

### 4. Responsive Design
- Design mobile-first
- Test on various screen sizes
- Use appropriate breakpoints
- Ensure touch-friendly interactions

### 5. Maintenance
- Document any custom styling decisions
- Keep styling patterns centralized
- Update this guide when adding new patterns
- Review and refactor outdated patterns regularly

## Migration Guide

When updating existing table components:

1. **Import the styling utilities**:
   ```typescript
   import { TableStyles, getStatusColor, combineTableClasses } from '@/lib/utils/table-styles'
   ```

2. **Replace hardcoded classes** with predefined constants:
   ```typescript
   // Before
   className="p-4 space-y-4"
   
   // After
   className={TABLE_SPACING.mobileCardPadding + ' ' + TABLE_SPACING.mobileCardGap}
   ```

3. **Update responsive visibility**:
   ```typescript
   // Before
   className="hidden md:block"
   
   // After
   className={RESPONSIVE_VISIBILITY.hiddenMobile}
   ```

4. **Standardize status colors**:
   ```typescript
   // Before
   className="bg-green-100 text-green-800"
   
   // After
   className={getStatusColor('confirmed')}
   ```

5. **Add proper touch targets**:
   ```typescript
   // Before
   <Button size="sm">
   
   // After
   <Button size="sm" className={TOUCH_TARGETS.button}>
   ```

This guide ensures all table components maintain visual consistency, accessibility standards, and responsive behavior across the application.