/**
 * Simple in-memory cache utility for API responses
 * DISABLED: Caching has been disabled to prevent stale data issues with customers and bookings
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class SimpleCache {
  private cache = new Map<string, CacheEntry<any>>();

  set<T>(key: string, data: T, ttlMs: number = 5 * 60 * 1000): void { // Default 5 minutes
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Clear expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  // Get cache statistics
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Global cache instance
export const apiCache = new SimpleCache();

// Cleanup expired entries every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    apiCache.cleanup();
  }, 5 * 60 * 1000);
}

// Cache key generators
export const generateCacheKey = (endpoint: string, params?: Record<string, any>): string => {
  if (!params) return endpoint;
  
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {} as Record<string, any>);
  
  return `${endpoint}?${new URLSearchParams(sortedParams).toString()}`;
};

// Cache-aware fetch wrapper - DISABLED: Always bypasses cache
export const cachedFetch = async <T>(
  url: string,
  options?: RequestInit,
  cacheOptions?: {
    ttl?: number;
    skipCache?: boolean;
    cacheKey?: string;
  }
): Promise<T> => {
  // CACHING DISABLED: Always fetch fresh data to prevent stale data issues
  console.warn('cachedFetch: Caching is disabled - fetching fresh data');

  // Fetch from API directly without caching
  const response = await fetch(url, options);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  // No caching - return fresh data
  return data;
};

// Invalidate cache entries by pattern - DISABLED: No-op function
export const invalidateCache = (pattern: string | RegExp): void => {
  // CACHING DISABLED: No cache to invalidate
  console.warn('invalidateCache: Caching is disabled - no cache to invalidate');
};

// Cache warming utility - DISABLED: No-op function
export const warmCache = async (endpoints: Array<{ url: string; ttl?: number }>): Promise<void> => {
  // CACHING DISABLED: No cache to warm
  console.warn('warmCache: Caching is disabled - no cache to warm');
};