/**
 * Relationship Maintenance Utilities
 * 
 * This module provides utilities for maintaining data relationships and ensuring
 * accurate count displays across the customer, booking, and invoice management system.
 */

import { prisma } from '@/lib/db';

/**
 * Recalculates and updates relationship counts for a customer
 * This ensures accurate booking counts are maintained
 */
export async function updateCustomerRelationshipCounts(customerId: number) {
  try {
    // Get current booking count
    const bookingCount = await prisma.booking.count({
      where: { 
        customerId: customerId,
        status: { not: 'CANCELLED' } // Only count non-cancelled bookings
      }
    });

    // Note: Prisma automatically maintains _count fields, but this function
    // can be used for custom business logic or manual corrections if needed
    
    return {
      bookings: bookingCount
    };
  } catch (error) {
    console.error('Error updating customer relationship counts:', error);
    throw error;
  }
}

/**
 * Recalculates and updates relationship counts for a booking
 * This ensures accurate invoice status is maintained
 */
export async function updateBookingRelationshipCounts(bookingId: number) {
  try {
    // Check if booking has an invoice
    const invoice = await prisma.invoice.findFirst({
      where: { bookingId: bookingId },
      include: {
        payments: {
          where: { status: 'COMPLETED' }
        }
      }
    });

    return {
      hasInvoice: !!invoice,
      invoiceStatus: invoice?.status || null,
      paymentsCount: invoice?.payments.length || 0
    };
  } catch (error) {
    console.error('Error updating booking relationship counts:', error);
    throw error;
  }
}

/**
 * Recalculates and updates relationship counts for an invoice
 * This ensures accurate payment counts and status are maintained
 */
export async function updateInvoiceRelationshipCounts(invoiceId: number) {
  try {
    // Get payment counts and totals
    const payments = await prisma.payment.findMany({
      where: { 
        invoiceId: invoiceId,
        status: 'COMPLETED'
      }
    });

    const totalPaid = payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
    
    // Get invoice total
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      select: { total: true }
    });

    if (!invoice) {
      throw new Error('Invoice not found');
    }

    const total = Number(invoice.total);
    
    // Determine status based on payments
    let status: 'PENDING' | 'PARTIALLY_PAID' | 'PAID' = 'PENDING';
    if (totalPaid > 0) {
      status = totalPaid >= total ? 'PAID' : 'PARTIALLY_PAID';
    }

    // Update invoice with calculated values
    await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        paid: totalPaid,
        status: status
      }
    });

    return {
      paymentsCount: payments.length,
      totalPaid: totalPaid,
      status: status
    };
  } catch (error) {
    console.error('Error updating invoice relationship counts:', error);
    throw error;
  }
}

/**
 * Validates referential integrity before deletion
 * Returns validation result with details about blocking relationships
 */
export async function validateCustomerDeletion(customerId: number) {
  try {
    const bookings = await prisma.booking.findMany({
      where: { customerId: customerId },
      select: {
        id: true,
        status: true,
        periods: {
          select: {
            start: true,
            end: true
          },
          orderBy: { start: 'asc' },
          take: 1 // Get the earliest period
        },
        invoice: {
          select: { id: true }
        }
      }
    });

    const activeBookings = bookings.filter(b => b.status !== 'CANCELLED');
    const bookingsWithInvoices = bookings.filter(b => b.invoice);

    return {
      canDelete: activeBookings.length === 0,
      blockingReasons: [
        ...(activeBookings.length > 0 ? [`Customer has ${activeBookings.length} active booking(s)`] : []),
        ...(bookingsWithInvoices.length > 0 ? [`Customer has ${bookingsWithInvoices.length} booking(s) with invoices`] : [])
      ],
      details: {
        totalBookings: bookings.length,
        activeBookings: activeBookings.length,
        bookingsWithInvoices: bookingsWithInvoices.length
      }
    };
  } catch (error) {
    console.error('Error validating customer deletion:', error);
    throw error;
  }
}

/**
 * Validates referential integrity before booking deletion
 */
export async function validateBookingDeletion(bookingId: number) {
  try {
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        invoice: {
          include: {
            payments: true
          }
        }
      }
    });

    if (!booking) {
      return {
        canDelete: false,
        blockingReasons: ['Booking not found'],
        details: {}
      };
    }

    const hasInvoice = !!booking.invoice;
    const hasPayments = booking.invoice?.payments && booking.invoice.payments.length > 0;

    return {
      canDelete: !hasInvoice,
      blockingReasons: [
        ...(hasInvoice ? ['Booking has an associated invoice'] : []),
        ...(hasPayments ? [`Invoice has ${booking.invoice?.payments?.length || 0} payment(s)`] : [])
      ],
      details: {
        hasInvoice,
        paymentsCount: booking.invoice?.payments?.length || 0,
        invoiceStatus: booking.invoice?.status || null
      }
    };
  } catch (error) {
    console.error('Error validating booking deletion:', error);
    throw error;
  }
}

/**
 * Validates referential integrity before invoice deletion
 */
export async function validateInvoiceDeletion(invoiceId: number) {
  try {
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        payments: true
      }
    });

    if (!invoice) {
      return {
        canDelete: false,
        blockingReasons: ['Invoice not found'],
        details: {}
      };
    }

    const hasPayments = invoice.payments.length > 0;
    const completedPayments = invoice.payments.filter(p => p.status === 'COMPLETED');

    return {
      canDelete: !hasPayments,
      blockingReasons: [
        ...(hasPayments ? [`Invoice has ${invoice.payments.length} payment(s)`] : []),
        ...(completedPayments.length > 0 ? [`${completedPayments.length} payment(s) are completed`] : [])
      ],
      details: {
        totalPayments: invoice.payments.length,
        completedPayments: completedPayments.length,
        totalPaid: completedPayments.reduce((sum, p) => sum + Number(p.amount), 0)
      }
    };
  } catch (error) {
    console.error('Error validating invoice deletion:', error);
    throw error;
  }
}

/**
 * Cascading update helper - updates related entities when a parent entity changes
 */
export async function cascadeCustomerUpdate(customerId: number, changes: { name?: string; email?: string }) {
  try {
    // If customer name or email changed, we might want to update related records
    // For now, we just return the counts, but this could be extended for business logic
    
    const relationshipCounts = await updateCustomerRelationshipCounts(customerId);
    
    return {
      updated: true,
      relationshipCounts
    };
  } catch (error) {
    console.error('Error in cascading customer update:', error);
    throw error;
  }
}

/**
 * Utility to get comprehensive relationship summary for an entity
 */
export async function getCustomerRelationshipSummary(customerId: number) {
  try {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        bookings: {
          include: {
            invoice: {
              include: {
                payments: true
              }
            }
          }
        },
        _count: {
          select: {
            bookings: true
          }
        }
      }
    });

    if (!customer) {
      throw new Error('Customer not found');
    }

    const activeBookings = customer.bookings.filter(b => b.status !== 'CANCELLED');
    const bookingsWithInvoices = customer.bookings.filter(b => b.invoice);
    const totalInvoiceAmount = bookingsWithInvoices.reduce((sum, b) => sum + Number(b.invoice?.total || 0), 0);
    const totalPaidAmount = bookingsWithInvoices.reduce((sum, b) => {
      const payments = b.invoice?.payments.filter(p => p.status === 'COMPLETED') || [];
      return sum + payments.reduce((pSum, p) => pSum + Number(p.amount), 0);
    }, 0);

    return {
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email
      },
      counts: {
        totalBookings: customer._count.bookings,
        activeBookings: activeBookings.length,
        bookingsWithInvoices: bookingsWithInvoices.length
      },
      financial: {
        totalInvoiceAmount,
        totalPaidAmount,
        outstandingAmount: totalInvoiceAmount - totalPaidAmount
      }
    };
  } catch (error) {
    console.error('Error getting customer relationship summary:', error);
    throw error;
  }
}