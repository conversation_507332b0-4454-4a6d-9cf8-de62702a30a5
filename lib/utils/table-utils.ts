/**
 * Shared table utilities and hooks for consistent table functionality
 * across all dashboard table components
 */

import { useState, useCallback, useMemo, useEffect } from 'react'
import { format, isValid } from 'date-fns'

/**
 * Generic sort direction type
 */
export type SortDirection = 'asc' | 'desc'

/**
 * Generic sort configuration interface
 */
export interface SortConfig<T> {
  field: keyof T
  direction: SortDirection
}

/**
 * Table state interface for consistent state management
 */
export interface TableState<T> {
  data: T[]
  loading: boolean
  error: Error | string | null
  sortConfig: SortConfig<T>
  retryCount: number
  isRetrying: boolean
}

/**
 * Sort handlers interface returned by useSorting hook
 */
export interface SortHandlers<T> {
  sortConfig: SortConfig<T>
  handleSort: (field: keyof T) => void
  getSortIcon: (field: keyof T) => React.ReactNode
  sortedData: T[]
}

/**
 * Table state handlers interface returned by useTableState hook
 */
export interface TableStateHandlers<T> {
  state: TableState<T>
  setData: (data: T[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: Error | string | null) => void
  setSortConfig: (config: SortConfig<T>) => void
  incrementRetry: () => void
  setIsRetrying: (isRetrying: boolean) => void
  resetRetry: () => void
  clearError: () => void
}

/**
 * Sort value extractor function type
 */
export type SortValueExtractor<T> = (item: T, field: keyof T) => any

/**
 * Default sort value extractor that handles common data types
 */
export const defaultSortValueExtractor = <T>(item: T, field: keyof T): any => {
  const value = item[field]
  
  // Handle null/undefined
  if (value == null) return ''
  
  // Handle dates
  if (value instanceof Date) {
    return value.getTime()
  }
  
  // Handle date strings
  if (typeof value === 'string' && isValidDateString(value)) {
    const date = new Date(value)
    return isValid(date) ? date.getTime() : 0
  }
  
  // Handle strings (case-insensitive)
  if (typeof value === 'string') {
    return value.toLowerCase()
  }
  
  // Handle numbers and other primitives
  return value
}

/**
 * Check if a string represents a valid date
 */
export const isValidDateString = (dateString: string): boolean => {
  // Common date patterns
  const datePatterns = [
    /^\d{4}-\d{2}-\d{2}/, // ISO date
    /^\d{2}\/\d{2}\/\d{4}/, // US date
    /^\d{2}-\d{2}-\d{4}/, // European date
  ]
  
  return datePatterns.some(pattern => pattern.test(dateString))
}

/**
 * Custom sorting hook for table components
 * Provides consistent sorting functionality with customizable value extraction
 * 
 * @param data - Array of data to sort
 * @param initialField - Initial field to sort by
 * @param initialDirection - Initial sort direction
 * @param valueExtractor - Custom function to extract sort values
 * @returns Sort handlers and sorted data
 * 
 * @example
 * ```tsx
 * const { sortConfig, handleSort, getSortIcon, sortedData } = useSorting(
 *   bookings,
 *   'createdAt',
 *   'desc',
 *   (booking, field) => {
 *     if (field === 'customerName') return booking.customer.name.toLowerCase()
 *     return defaultSortValueExtractor(booking, field)
 *   }
 * )
 * ```
 */
export const useSorting = <T>(
  data: T[],
  initialField: keyof T,
  initialDirection: SortDirection = 'asc',
  valueExtractor: SortValueExtractor<T> = defaultSortValueExtractor
): SortHandlers<T> => {
  const [sortConfig, setSortConfig] = useState<SortConfig<T>>({
    field: initialField,
    direction: initialDirection,
  })

  const handleSort = useCallback((field: keyof T) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }, [])

  const getSortIcon = useCallback((field: keyof T): React.ReactNode => {
    // This will be replaced with actual icons in the component
    if (sortConfig.field !== field) {
      return 'sort'
    }
    return sortConfig.direction === 'asc' ? 'sort-asc' : 'sort-desc'
  }, [sortConfig.field, sortConfig.direction])

  const sortedData = useMemo(() => {
    if (!data || data.length === 0) return data

    return [...data].sort((a, b) => {
      const { field, direction } = sortConfig
      
      const aValue = valueExtractor(a, field)
      const bValue = valueExtractor(b, field)

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0
      if (aValue == null) return direction === 'asc' ? -1 : 1
      if (bValue == null) return direction === 'asc' ? 1 : -1

      // Compare values
      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [data, sortConfig, valueExtractor])

  return {
    sortConfig,
    handleSort,
    getSortIcon,
    sortedData,
  }
}

/**
 * Table state management hook for consistent state handling
 * Provides centralized state management for table components
 * 
 * @param initialData - Initial data array
 * @param initialSortField - Initial field to sort by
 * @param initialSortDirection - Initial sort direction
 * @returns Table state and handlers
 * 
 * @example
 * ```tsx
 * const {
 *   state,
 *   setData,
 *   setLoading,
 *   setError,
 *   incrementRetry,
 *   clearError
 * } = useTableState([], 'createdAt', 'desc')
 * ```
 */
export const useTableState = <T>(
  initialData: T[] = [],
  initialSortField: keyof T,
  initialSortDirection: SortDirection = 'asc'
): TableStateHandlers<T> => {
  const [state, setState] = useState<TableState<T>>({
    data: initialData,
    loading: false,
    error: null,
    sortConfig: {
      field: initialSortField,
      direction: initialSortDirection,
    },
    retryCount: 0,
    isRetrying: false,
  })

  const setData = useCallback((data: T[]) => {
    setState(prev => ({ ...prev, data }))
  }, [])

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }))
  }, [])

  const setError = useCallback((error: Error | string | null) => {
    setState(prev => ({ 
      ...prev, 
      error,
      // Reset retry state when setting new error
      retryCount: error ? prev.retryCount : 0,
      isRetrying: false
    }))
  }, [])

  const setSortConfig = useCallback((sortConfig: SortConfig<T>) => {
    setState(prev => ({ ...prev, sortConfig }))
  }, [])

  const incrementRetry = useCallback(() => {
    setState(prev => ({ ...prev, retryCount: prev.retryCount + 1 }))
  }, [])

  const setIsRetrying = useCallback((isRetrying: boolean) => {
    setState(prev => ({ ...prev, isRetrying }))
  }, [])

  const resetRetry = useCallback(() => {
    setState(prev => ({ ...prev, retryCount: 0, isRetrying: false }))
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      error: null, 
      retryCount: 0, 
      isRetrying: false 
    }))
  }, [])

  return {
    state,
    setData,
    setLoading,
    setError,
    setSortConfig,
    incrementRetry,
    setIsRetrying,
    resetRetry,
    clearError,
  }
}

/**
 * Date formatting utilities for consistent date display
 */
export const formatTableDate = (date: Date | string | null | undefined): string => {
  if (!date) return 'N/A'
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    if (!isValid(dateObj)) return 'Invalid date'
    return format(dateObj, 'yyyy-MM-dd')
  } catch (error) {
    return 'Invalid date'
  }
}

export const formatTableDateTime = (date: Date | string | null | undefined): string => {
  if (!date) return 'N/A'
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    if (!isValid(dateObj)) return 'Invalid date'
    return format(dateObj, 'yyyy-MM-dd h:mm a')
  } catch (error) {
    return 'Invalid date'
  }
}

/**
 * Error handling utilities for consistent error processing
 */
export const normalizeError = (error: unknown): Error | string => {
  if (error instanceof Error) {
    return error
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  
  return 'An unexpected error occurred'
}

/**
 * Performance optimization utilities
 */

/**
 * Memoization helper for expensive computations
 */
export const memoizeTableComputation = <T, R>(
  fn: (data: T[], ...args: any[]) => R,
  dependencies: any[] = []
) => {
  return useMemo(() => fn, dependencies)
}

/**
 * Debounce utility for search and filter operations
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Common table breakpoints for responsive design
 */
export const TABLE_BREAKPOINTS = {
  mobile: '(max-width: 767px)',
  tablet: '(min-width: 768px) and (max-width: 1023px)',
  desktop: '(min-width: 1024px)',
} as const

/**
 * Common table CSS classes for consistent styling
 */
export const TABLE_CLASSES = {
  // Touch targets for mobile
  touchTarget: 'min-h-[44px] min-w-[44px]',
  focusEnhanced: 'focus:ring-2 focus:ring-primary focus:ring-offset-2',
  mobileSpacing: 'p-4 space-y-4',
  
  // Responsive visibility
  hiddenMobile: 'hidden md:block',
  hiddenDesktop: 'block md:hidden',
  hiddenTablet: 'hidden lg:block',
  
  // Loading states
  loadingSkeleton: 'animate-pulse bg-muted rounded',
  loadingShimmer: 'animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted',
  
  // Error states
  errorBorder: 'border-destructive',
  errorText: 'text-destructive',
  errorBackground: 'bg-destructive/10',
} as const

/**
 * Accessibility helpers for table components
 */
export const getTableAriaLabel = (
  itemType: string,
  totalCount: number,
  sortField?: string,
  sortDirection?: SortDirection
): string => {
  let label = `${itemType} table with ${totalCount} ${totalCount === 1 ? 'item' : 'items'}`
  
  if (sortField && sortDirection) {
    label += `, sorted by ${String(sortField)} ${sortDirection === 'asc' ? 'ascending' : 'descending'}`
  }
  
  return label
}

export const getSortButtonAriaLabel = (
  fieldName: string,
  currentField?: string,
  currentDirection?: SortDirection
): string => {
  if (currentField === fieldName) {
    const nextDirection = currentDirection === 'asc' ? 'descending' : 'ascending'
    return `Sort by ${fieldName} ${nextDirection}`
  }
  
  return `Sort by ${fieldName} ascending`
}

/**
 * Common table action handlers with error handling
 */
export const createTableActionHandler = <T extends { id: number | string }>(
  action: (item: T) => Promise<void> | void,
  onError?: (error: Error) => void
) => {
  return async (item: T) => {
    try {
      await action(item)
    } catch (error) {
      const normalizedError = error instanceof Error ? error : new Error(String(error))
      onError?.(normalizedError)
      console.error(`Table action failed for item ${item.id}:`, normalizedError)
    }
  }
}

/**
 * Table data validation utilities
 */
export const validateTableData = <T>(
  data: unknown,
  requiredFields: (keyof T)[]
): data is T[] => {
  if (!Array.isArray(data)) return false
  
  return data.every(item => {
    if (!item || typeof item !== 'object') return false
    
    return requiredFields.every(field => 
      field in item && item[field as keyof typeof item] !== undefined
    )
  })
}

/**
 * Export all utilities as a single object for easier importing
 */
export const TableUtils = {
  // Hooks
  useSorting,
  useTableState,
  useDebounce,
  
  // Utilities
  defaultSortValueExtractor,
  isValidDateString,
  formatTableDate,
  formatTableDateTime,
  normalizeError,
  memoizeTableComputation,
  getTableAriaLabel,
  getSortButtonAriaLabel,
  createTableActionHandler,
  validateTableData,
  
  // Constants
  TABLE_BREAKPOINTS,
  TABLE_CLASSES,
} as const