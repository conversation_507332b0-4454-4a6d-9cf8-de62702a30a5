import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ConflictDetectionService } from '../conflict-detection';
import { periodService } from '../period-service';
import { prisma } from '@/lib/db';
import { PeriodFormData, Period, BookingStatus } from '@/lib/types';

// Mock the database
vi.mock('@/lib/db', () => ({
  prisma: {
    booking: {
      findUnique: vi.fn(),
    },
    resource: {
      findUnique: vi.fn(),
    },
  },
}));

// Mock the period service
vi.mock('../period-service', () => ({
  periodService: {
    findConflictingPeriods: vi.fn(),
    getPeriodById: vi.fn(),
    getPeriodsByBookingId: vi.fn(),
    getPeriodsInRange: vi.fn(),
  },
}));

describe('ConflictDetectionService', () => {
  let conflictDetectionService: ConflictDetectionService;
  
  beforeEach(() => {
    conflictDetectionService = new ConflictDetectionService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('detectConflicts', () => {
    it('should return no conflicts when no periods provided', async () => {
      const result = await conflictDetectionService.detectConflicts([], [1, 2], 1);
      
      expect(result).toEqual({
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No periods to check'
      });
    });

    it('should return no conflicts when no resources provided', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      }];

      const result = await conflictDetectionService.detectConflicts(periods, [], 1);
      
      expect(result).toEqual({
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No resources to check'
      });
    });

    it('should detect conflicts when periods overlap with shared resources', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      }];

      const conflictingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date('2024-01-01T11:00:00Z'),
        end: new Date('2024-01-01T13:00:00Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'John Doe' } as any,
          resources: [
            { id: 1, name: 'Conference Room A' } as any,
            { id: 2, name: 'Conference Room B' } as any
          ],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([conflictingPeriod]);

      const result = await conflictDetectionService.detectConflicts(periods, [1, 2], 1);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts).toHaveLength(1);
      expect(result.conflicts[0]).toEqual({
        periodId: 0,
        conflictingPeriodId: 1,
        conflictingResources: [
          { id: 1, name: 'Conference Room A' },
          { id: 2, name: 'Conference Room B' }
        ],
        overlapStart: new Date('2024-01-01T11:00:00Z'),
        overlapEnd: new Date('2024-01-01T12:00:00Z'),
        conflictingBooking: {
          id: 2,
          customerName: 'John Doe',
          status: 'CONFIRMED'
        }
      });
      expect(result.affectedResources).toHaveLength(2);
      expect(result.message).toBe('Found 1 conflict(s) across 2 resource(s)');
    });

    it('should not detect conflicts when periods overlap but no shared resources', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      }];

      const conflictingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date('2024-01-01T11:00:00Z'),
        end: new Date('2024-01-01T13:00:00Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'John Doe' } as any,
          resources: [
            { id: 3, name: 'Conference Room C' } as any,
            { id: 4, name: 'Conference Room D' } as any
          ],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([conflictingPeriod]);

      const result = await conflictDetectionService.detectConflicts(periods, [1, 2], 1);

      expect(result.hasConflicts).toBe(false);
      expect(result.conflicts).toHaveLength(0);
      expect(result.affectedResources).toHaveLength(0);
      expect(result.message).toBe('No conflicts detected');
    });

    it('should handle multiple periods with different conflict scenarios', async () => {
      const periods: PeriodFormData[] = [
        {
          start: new Date('2024-01-01T10:00:00Z'),
          end: new Date('2024-01-01T12:00:00Z'),
          isRecurring: false
        },
        {
          start: new Date('2024-01-01T14:00:00Z'),
          end: new Date('2024-01-01T16:00:00Z'),
          isRecurring: false
        }
      ];

      const conflictingPeriods: Period[] = [
        {
          id: 1,
          bookingId: 2,
          start: new Date('2024-01-01T11:00:00Z'),
          end: new Date('2024-01-01T13:00:00Z'),
          isRecurring: false,
          recurrenceRule: null,
          parentPeriodId: null,
          booking: {
            id: 2,
            customerId: 1,
            status: 'CONFIRMED' as BookingStatus,
            customer: { id: 1, name: 'John Doe' } as any,
            resources: [{ id: 1, name: 'Conference Room A' } as any],
            periods: [],
            caterings: [],
            createdAt: new Date(),
            updatedAt: new Date()
          } as any,
          parentPeriod: null,
          childPeriods: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          createdById: null,
          updatedById: null
        },
        {
          id: 2,
          bookingId: 3,
          start: new Date('2024-01-01T15:00:00Z'),
          end: new Date('2024-01-01T17:00:00Z'),
          isRecurring: false,
          recurrenceRule: null,
          parentPeriodId: null,
          booking: {
            id: 3,
            customerId: 2,
            status: 'PENDING' as BookingStatus,
            customer: { id: 2, name: 'Jane Smith' } as any,
            resources: [{ id: 2, name: 'Conference Room B' } as any],
            periods: [],
            caterings: [],
            createdAt: new Date(),
            updatedAt: new Date()
          } as any,
          parentPeriod: null,
          childPeriods: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          createdById: null,
          updatedById: null
        }
      ];

      // Mock different responses for different periods
      vi.mocked(periodService.findConflictingPeriods)
        .mockResolvedValueOnce([conflictingPeriods[0]]) // First period conflicts
        .mockResolvedValueOnce([conflictingPeriods[1]]); // Second period conflicts

      const result = await conflictDetectionService.detectConflicts(periods, [1, 2], 1);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts).toHaveLength(2);
      expect(result.affectedResources).toHaveLength(2);
      expect(result.message).toBe('Found 2 conflict(s) across 2 resource(s)');
    });
  });

  describe('checkSinglePeriodConflicts', () => {
    it('should check conflicts for a single period', async () => {
      const period: PeriodFormData = {
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([]);

      const result = await conflictDetectionService.checkSinglePeriodConflicts(period, [1, 2], 1);

      expect(result.hasConflicts).toBe(false);
      expect(periodService.findConflictingPeriods).toHaveBeenCalledWith(
        [1, 2],
        period.start,
        period.end,
        1
      );
    });
  });

  describe('checkExistingPeriodConflicts', () => {
    it('should check conflicts for an existing period being updated', async () => {
      const existingPeriod: Period = {
        id: 1,
        bookingId: 5,
        start: new Date('2024-01-01T09:00:00Z'),
        end: new Date('2024-01-01T11:00:00Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {} as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      const updatedPeriod: PeriodFormData = {
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      };

      vi.mocked(periodService.getPeriodById).mockResolvedValue(existingPeriod);
      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([]);

      const result = await conflictDetectionService.checkExistingPeriodConflicts(1, updatedPeriod, [1, 2]);

      expect(result.hasConflicts).toBe(false);
      expect(periodService.getPeriodById).toHaveBeenCalledWith(1);
      expect(periodService.findConflictingPeriods).toHaveBeenCalledWith(
        [1, 2],
        updatedPeriod.start,
        updatedPeriod.end,
        5 // excludeBookingId should be the existing period's booking ID
      );
    });

    it('should throw error when period not found', async () => {
      vi.mocked(periodService.getPeriodById).mockResolvedValue(null);

      await expect(
        conflictDetectionService.checkExistingPeriodConflicts(999, {} as PeriodFormData, [1, 2])
      ).rejects.toThrow('Period not found');
    });
  });

  describe('getBookingConflictDetails', () => {
    it('should get conflict details for a booking', async () => {
      const bookingPeriods: Period[] = [{
        id: 1,
        bookingId: 1,
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {} as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      }];

      const booking = {
        id: 1,
        resources: [
          { id: 1, name: 'Conference Room A' },
          { id: 2, name: 'Conference Room B' }
        ]
      };

      vi.mocked(periodService.getPeriodsByBookingId).mockResolvedValue(bookingPeriods);
      vi.mocked(prisma.booking.findUnique).mockResolvedValue(booking as any);
      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([]);

      const result = await conflictDetectionService.getBookingConflictDetails(1);

      expect(result.hasConflicts).toBe(false);
      expect(periodService.getPeriodsByBookingId).toHaveBeenCalledWith(1);
      expect(prisma.booking.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { resources: true }
      });
    });

    it('should return no conflicts message when booking has no periods', async () => {
      vi.mocked(periodService.getPeriodsByBookingId).mockResolvedValue([]);

      const result = await conflictDetectionService.getBookingConflictDetails(1);

      expect(result).toEqual({
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No periods found for booking'
      });
    });

    it('should throw error when booking not found', async () => {
      vi.mocked(periodService.getPeriodsByBookingId).mockResolvedValue([{} as Period]);
      vi.mocked(prisma.booking.findUnique).mockResolvedValue(null);

      await expect(
        conflictDetectionService.getBookingConflictDetails(999)
      ).rejects.toThrow('Booking not found');
    });
  });

  describe('getResourceUtilization', () => {
    it('should calculate resource utilization correctly', async () => {
      const startDate = new Date('2024-01-01T00:00:00Z');
      const endDate = new Date('2024-01-01T24:00:00Z');
      
      const resource = { id: 1, name: 'Conference Room A' };
      const periods: Period[] = [{
        id: 1,
        bookingId: 1,
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'), // 2 hours
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 1,
          customer: { id: 1, name: 'John Doe' } as any
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      }];

      vi.mocked(prisma.resource.findUnique).mockResolvedValue(resource as any);
      vi.mocked(periodService.getPeriodsInRange).mockResolvedValue(periods);

      const result = await conflictDetectionService.getResourceUtilization([1], startDate, endDate);

      expect(result).toHaveLength(1);
      expect(result[0].resourceId).toBe(1);
      expect(result[0].resourceName).toBe('Conference Room A');
      expect(result[0].totalBookedTime).toBe(2 * 60 * 60 * 1000); // 2 hours in milliseconds
      expect(result[0].totalAvailableTime).toBe(24 * 60 * 60 * 1000); // 24 hours in milliseconds
      expect(result[0].utilizationPercentage).toBeCloseTo(8.33, 2); // 2/24 * 100
      expect(result[0].conflictingBookings).toHaveLength(1);
      expect(result[0].conflictingBookings[0].customerName).toBe('John Doe');
    });

    it('should handle periods that partially overlap with date range', async () => {
      const startDate = new Date('2024-01-01T10:00:00Z');
      const endDate = new Date('2024-01-01T14:00:00Z'); // 4 hour window
      
      const resource = { id: 1, name: 'Conference Room A' };
      const periods: Period[] = [{
        id: 1,
        bookingId: 1,
        start: new Date('2024-01-01T08:00:00Z'), // Starts before range
        end: new Date('2024-01-01T12:00:00Z'), // Ends within range
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 1,
          customer: { id: 1, name: 'John Doe' } as any
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      }];

      vi.mocked(prisma.resource.findUnique).mockResolvedValue(resource as any);
      vi.mocked(periodService.getPeriodsInRange).mockResolvedValue(periods);

      const result = await conflictDetectionService.getResourceUtilization([1], startDate, endDate);

      expect(result[0].totalBookedTime).toBe(2 * 60 * 60 * 1000); // Only 2 hours overlap (10:00-12:00)
      expect(result[0].utilizationPercentage).toBe(50); // 2/4 * 100
    });

    it('should skip resources that do not exist', async () => {
      vi.mocked(prisma.resource.findUnique).mockResolvedValue(null);

      const result = await conflictDetectionService.getResourceUtilization(
        [999], 
        new Date(), 
        new Date()
      );

      expect(result).toHaveLength(0);
    });
  });

  describe('validateInternalPeriodOverlaps', () => {
    it('should detect overlaps between periods in the same booking', () => {
      const periods: PeriodFormData[] = [
        {
          start: new Date('2024-01-01T10:00:00Z'),
          end: new Date('2024-01-01T12:00:00Z'),
          isRecurring: false
        },
        {
          start: new Date('2024-01-01T11:00:00Z'),
          end: new Date('2024-01-01T13:00:00Z'),
          isRecurring: false
        }
      ];

      const result = conflictDetectionService.validateInternalPeriodOverlaps(periods);

      expect(result.hasOverlaps).toBe(true);
      expect(result.overlaps).toHaveLength(1);
      expect(result.overlaps[0]).toEqual({
        period1Index: 0,
        period2Index: 1,
        period1: periods[0],
        period2: periods[1],
        overlapStart: new Date('2024-01-01T11:00:00Z'),
        overlapEnd: new Date('2024-01-01T12:00:00Z')
      });
    });

    it('should return no overlaps when periods do not overlap', () => {
      const periods: PeriodFormData[] = [
        {
          start: new Date('2024-01-01T10:00:00Z'),
          end: new Date('2024-01-01T12:00:00Z'),
          isRecurring: false
        },
        {
          start: new Date('2024-01-01T13:00:00Z'),
          end: new Date('2024-01-01T15:00:00Z'),
          isRecurring: false
        }
      ];

      const result = conflictDetectionService.validateInternalPeriodOverlaps(periods);

      expect(result.hasOverlaps).toBe(false);
      expect(result.overlaps).toHaveLength(0);
    });

    it('should handle adjacent periods correctly (no overlap)', () => {
      const periods: PeriodFormData[] = [
        {
          start: new Date('2024-01-01T10:00:00Z'),
          end: new Date('2024-01-01T12:00:00Z'),
          isRecurring: false
        },
        {
          start: new Date('2024-01-01T12:00:00Z'), // Starts exactly when first ends
          end: new Date('2024-01-01T14:00:00Z'),
          isRecurring: false
        }
      ];

      const result = conflictDetectionService.validateInternalPeriodOverlaps(periods);

      expect(result.hasOverlaps).toBe(false);
      expect(result.overlaps).toHaveLength(0);
    });
  });

  describe('getMultiBookingConflictSummary', () => {
    it('should provide conflict summary for multiple bookings', async () => {
      const booking1 = {
        id: 1,
        customer: { id: 1, name: 'John Doe' }
      };
      
      const booking2 = {
        id: 2,
        customer: { id: 2, name: 'Jane Smith' }
      };

      const conflictResult1 = {
        hasConflicts: true,
        conflicts: [
          {
            periodId: 1,
            conflictingPeriodId: 2,
            conflictingResources: [{ id: 1, name: 'Room A' }],
            overlapStart: new Date(),
            overlapEnd: new Date(),
            conflictingBooking: { id: 3, customerName: 'Bob', status: 'CONFIRMED' as BookingStatus }
          }
        ],
        affectedResources: [{ id: 1, name: 'Room A' }],
        message: 'Found 1 conflict(s) across 1 resource(s)'
      };

      const conflictResult2 = {
        hasConflicts: true,
        conflicts: [
          {
            periodId: 3,
            conflictingPeriodId: 4,
            conflictingResources: [{ id: 1, name: 'Room A' }, { id: 2, name: 'Room B' }],
            overlapStart: new Date(),
            overlapEnd: new Date(),
            conflictingBooking: { id: 4, customerName: 'Alice', status: 'PENDING' as BookingStatus }
          }
        ],
        affectedResources: [{ id: 1, name: 'Room A' }, { id: 2, name: 'Room B' }],
        message: 'Found 1 conflict(s) across 2 resource(s)'
      };

      // Mock the getBookingConflictDetails method
      const getBookingConflictDetailsSpy = vi.spyOn(conflictDetectionService, 'getBookingConflictDetails')
        .mockResolvedValueOnce(conflictResult1)
        .mockResolvedValueOnce(conflictResult2);

      vi.mocked(prisma.booking.findUnique)
        .mockResolvedValueOnce(booking1 as any)
        .mockResolvedValueOnce(booking2 as any);

      const result = await conflictDetectionService.getMultiBookingConflictSummary([1, 2]);

      expect(result.totalConflicts).toBe(2);
      expect(result.conflictsByBooking).toHaveLength(2);
      expect(result.conflictsByBooking[0]).toEqual({
        bookingId: 1,
        customerName: 'John Doe',
        conflictCount: 1,
        affectedResources: ['Room A']
      });
      expect(result.conflictsByBooking[1]).toEqual({
        bookingId: 2,
        customerName: 'Jane Smith',
        conflictCount: 1,
        affectedResources: ['Room A', 'Room B']
      });
      expect(result.mostConflictedResources).toHaveLength(2);
      expect(result.mostConflictedResources[0]).toEqual({
        resourceId: 1,
        resourceName: 'Room A',
        conflictCount: 2 // Appears in both bookings
      });
      expect(result.mostConflictedResources[1]).toEqual({
        resourceId: 2,
        resourceName: 'Room B',
        conflictCount: 1
      });

      getBookingConflictDetailsSpy.mockRestore();
    });
  });
});