import { describe, it, expect, beforeEach } from 'vitest';
import { RecurrenceEngine } from '../recurrence-engine';
import { RecurrenceRule, PeriodFormData } from '@/lib/types';

describe('RecurrenceEngine', () => {
  let engine: RecurrenceEngine;
  let basePeriod: { start: Date; end: Date };

  beforeEach(() => {
    engine = new RecurrenceEngine();
    // Base period: January 15, 2024, 9:00 AM - 10:00 AM
    basePeriod = {
      start: new Date(2024, 0, 15, 9, 0, 0), // January 15, 2024, 9:00 AM
      end: new Date(2024, 0, 15, 10, 0, 0)   // January 15, 2024, 10:00 AM
    };
  });

  describe('validateRecurrenceRule', () => {
    it('should validate a correct daily recurrence rule', () => {
      const rule: RecurrenceRule = {
        type: 'daily',
        interval: 1,
        count: 5
      };

      const result = engine.validateRecurrenceRule(rule);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate a correct weekly recurrence rule', () => {
      const rule: RecurrenceRule = {
        type: 'weekly',
        interval: 1,
        daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
        count: 10
      };

      const result = engine.validateRecurrenceRule(rule);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate a correct monthly recurrence rule with day of month', () => {
      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 1,
        dayOfMonth: 15,
        count: 6
      };

      const result = engine.validateRecurrenceRule(rule);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate a correct monthly recurrence rule with relative day', () => {
      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 1,
        weekOfMonth: 2, // Second week
        dayOfWeek: 1,   // Monday
        count: 6
      };

      const result = engine.validateRecurrenceRule(rule);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate a correct yearly recurrence rule', () => {
      const rule: RecurrenceRule = {
        type: 'yearly',
        interval: 1,
        month: 6, // June
        count: 3
      };

      const result = engine.validateRecurrenceRule(rule);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid recurrence rules', () => {
      const invalidRule = {
        type: 'daily',
        interval: 0, // Invalid: must be at least 1
        count: 5
      } as RecurrenceRule;

      const result = engine.validateRecurrenceRule(invalidRule);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should reject weekly recurrence without days of week', () => {
      const rule: RecurrenceRule = {
        type: 'weekly',
        interval: 1,
        count: 5
        // Missing daysOfWeek
      };

      const result = engine.validateRecurrenceRule(rule);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('daysOfWeek'))).toBe(true);
    });

    it('should reject monthly recurrence without day specification', () => {
      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 1,
        count: 5
        // Missing dayOfMonth or weekOfMonth/dayOfWeek
      };

      const result = engine.validateRecurrenceRule(rule);
      expect(result.isValid).toBe(false);
    });

    it('should reject yearly recurrence without month', () => {
      const rule: RecurrenceRule = {
        type: 'yearly',
        interval: 1,
        count: 3
        // Missing month
      };

      const result = engine.validateRecurrenceRule(rule);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('month'))).toBe(true);
    });
  });

  describe('generateDailyPeriods', () => {
    it('should generate daily periods with count limit', () => {
      const rule: RecurrenceRule = {
        type: 'daily',
        interval: 1,
        count: 5
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(5);
      
      // Check first period
      expect(periods[0].start).toEqual(basePeriod.start);
      expect(periods[0].end).toEqual(basePeriod.end);
      
      // Check second period (next day)
      expect(periods[1].start).toEqual(new Date(2024, 0, 16, 9, 0, 0));
      expect(periods[1].end).toEqual(new Date(2024, 0, 16, 10, 0, 0));
      
      // Check last period
      expect(periods[4].start).toEqual(new Date(2024, 0, 19, 9, 0, 0));
      expect(periods[4].end).toEqual(new Date(2024, 0, 19, 10, 0, 0));
    });

    it('should generate daily periods with interval > 1', () => {
      const rule: RecurrenceRule = {
        type: 'daily',
        interval: 3, // Every 3 days
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      // Check dates are 3 days apart
      expect(periods[0].start).toEqual(new Date(2024, 0, 15, 9, 0, 0));
      expect(periods[1].start).toEqual(new Date(2024, 0, 18, 9, 0, 0));
      expect(periods[2].start).toEqual(new Date(2024, 0, 21, 9, 0, 0));
    });

    it('should generate daily periods with end date limit', () => {
      const rule: RecurrenceRule = {
        type: 'daily',
        interval: 1,
        endDate: new Date(2024, 0, 18, 23, 59, 59) // End on January 18
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(4); // Jan 15, 16, 17, 18
      expect(periods[periods.length - 1].start).toEqual(new Date(2024, 0, 18, 9, 0, 0));
    });
  });

  describe('generateWeeklyPeriods', () => {
    it('should generate weekly periods for single day', () => {
      const rule: RecurrenceRule = {
        type: 'weekly',
        interval: 1,
        daysOfWeek: [1], // Monday only
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      // January 15, 2024 is a Monday, so periods should be on consecutive Mondays
      expect(periods[0].start).toEqual(new Date(2024, 0, 15, 9, 0, 0));
      expect(periods[1].start).toEqual(new Date(2024, 0, 22, 9, 0, 0));
      expect(periods[2].start).toEqual(new Date(2024, 0, 29, 9, 0, 0));
    });

    it('should generate weekly periods for multiple days', () => {
      const rule: RecurrenceRule = {
        type: 'weekly',
        interval: 1,
        daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
        count: 6
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(6);
      
      // Should generate Mon, Wed, Fri of first week, then Mon, Wed, Fri of second week
      expect(periods[0].start).toEqual(new Date(2024, 0, 15, 9, 0, 0)); // Monday
      expect(periods[1].start).toEqual(new Date(2024, 0, 17, 9, 0, 0)); // Wednesday
      expect(periods[2].start).toEqual(new Date(2024, 0, 19, 9, 0, 0)); // Friday
      expect(periods[3].start).toEqual(new Date(2024, 0, 22, 9, 0, 0)); // Next Monday
    });

    it('should generate weekly periods with interval > 1', () => {
      const rule: RecurrenceRule = {
        type: 'weekly',
        interval: 2, // Every 2 weeks
        daysOfWeek: [1], // Monday only
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      // Should be every other Monday
      expect(periods[0].start).toEqual(new Date(2024, 0, 15, 9, 0, 0)); // Jan 15
      expect(periods[1].start).toEqual(new Date(2024, 0, 29, 9, 0, 0)); // Jan 29 (2 weeks later)
      expect(periods[2].start).toEqual(new Date(2024, 1, 12, 9, 0, 0)); // Feb 12 (2 weeks later)
    });

    it('should handle weekly periods when base period is not on specified day', () => {
      // Base period is Monday, but we want Tuesday
      const rule: RecurrenceRule = {
        type: 'weekly',
        interval: 1,
        daysOfWeek: [2], // Tuesday
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      // Should start from the first Tuesday after the base period
      expect(periods[0].start).toEqual(new Date(2024, 0, 16, 9, 0, 0)); // Jan 16 (Tuesday)
      expect(periods[1].start).toEqual(new Date(2024, 0, 23, 9, 0, 0)); // Jan 23 (Tuesday)
      expect(periods[2].start).toEqual(new Date(2024, 0, 30, 9, 0, 0)); // Jan 30 (Tuesday)
    });
  });

  describe('generateMonthlyPeriods', () => {
    it('should generate monthly periods by day of month', () => {
      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 1,
        dayOfMonth: 15,
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      expect(periods[0].start).toEqual(new Date(2024, 0, 15, 9, 0, 0)); // Jan 15
      expect(periods[1].start).toEqual(new Date(2024, 1, 15, 9, 0, 0)); // Feb 15
      expect(periods[2].start).toEqual(new Date(2024, 2, 15, 9, 0, 0)); // Mar 15
    });

    it('should generate monthly periods by relative day (first Monday)', () => {
      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 1,
        weekOfMonth: 1, // First week
        dayOfWeek: 1,   // Monday
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      // First Monday of each month
      expect(periods[0].start).toEqual(new Date(2024, 0, 1, 9, 0, 0));  // Jan 1, 2024 (first Monday)
      expect(periods[1].start).toEqual(new Date(2024, 1, 5, 9, 0, 0));  // Feb 5, 2024 (first Monday)
      expect(periods[2].start).toEqual(new Date(2024, 2, 4, 9, 0, 0));  // Mar 4, 2024 (first Monday)
    });

    it('should generate monthly periods by relative day (last Friday)', () => {
      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 1,
        weekOfMonth: -1, // Last week
        dayOfWeek: 5,    // Friday
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      // Last Friday of each month
      expect(periods[0].start).toEqual(new Date(2024, 0, 26, 9, 0, 0)); // Jan 26, 2024 (last Friday)
      expect(periods[1].start).toEqual(new Date(2024, 1, 23, 9, 0, 0)); // Feb 23, 2024 (last Friday)
      expect(periods[2].start).toEqual(new Date(2024, 2, 29, 9, 0, 0)); // Mar 29, 2024 (last Friday)
    });

    it('should handle monthly periods with interval > 1', () => {
      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 3, // Every 3 months
        dayOfMonth: 15,
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      expect(periods[0].start).toEqual(new Date(2024, 0, 15, 9, 0, 0)); // Jan 15
      expect(periods[1].start).toEqual(new Date(2024, 3, 15, 9, 0, 0)); // Apr 15 (3 months later)
      expect(periods[2].start).toEqual(new Date(2024, 6, 15, 9, 0, 0)); // Jul 15 (3 months later)
    });

    it('should skip months where the day does not exist', () => {
      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 1,
        dayOfMonth: 31, // Not all months have 31 days
        count: 12 // Try for a full year to see skipping behavior
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      // The algorithm correctly skips months without 31 days and continues to the next valid month
      // In 2024: Jan, Mar, May, Jul, Aug, Oct, Dec (7 months)
      // In 2025: Jan, Mar, May, Jul, Aug (5 months) = 12 total
      expect(periods.length).toBe(12);
      
      // Check that all generated periods are on the 31st
      periods.forEach(period => {
        expect(period.start.getDate()).toBe(31);
      });
      
      // Check that we skipped months without 31 days and only generated for valid months
      const monthsWithPeriods = periods.map(p => p.start.getMonth());
      // Should be: Jan, Mar, May, Jul, Aug, Oct, Dec (2024) + Jan, Mar, May, Jul, Aug (2025)
      expect(monthsWithPeriods).toEqual([0, 2, 4, 6, 7, 9, 11, 0, 2, 4, 6, 7]);
    });
  });

  describe('generateYearlyPeriods', () => {
    it('should generate yearly periods', () => {
      const rule: RecurrenceRule = {
        type: 'yearly',
        interval: 1,
        month: 1, // January
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      expect(periods[0].start).toEqual(new Date(2024, 0, 15, 9, 0, 0)); // Jan 15, 2024
      expect(periods[1].start).toEqual(new Date(2025, 0, 15, 9, 0, 0)); // Jan 15, 2025
      expect(periods[2].start).toEqual(new Date(2026, 0, 15, 9, 0, 0)); // Jan 15, 2026
    });

    it('should generate yearly periods with different month', () => {
      const rule: RecurrenceRule = {
        type: 'yearly',
        interval: 1,
        month: 6, // June
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      expect(periods[0].start).toEqual(new Date(2024, 5, 15, 9, 0, 0)); // Jun 15, 2024
      expect(periods[1].start).toEqual(new Date(2025, 5, 15, 9, 0, 0)); // Jun 15, 2025
      expect(periods[2].start).toEqual(new Date(2026, 5, 15, 9, 0, 0)); // Jun 15, 2026
    });

    it('should generate yearly periods with interval > 1', () => {
      const rule: RecurrenceRule = {
        type: 'yearly',
        interval: 2, // Every 2 years
        month: 1,
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      
      expect(periods[0].start).toEqual(new Date(2024, 0, 15, 9, 0, 0)); // Jan 15, 2024
      expect(periods[1].start).toEqual(new Date(2026, 0, 15, 9, 0, 0)); // Jan 15, 2026
      expect(periods[2].start).toEqual(new Date(2028, 0, 15, 9, 0, 0)); // Jan 15, 2028
    });

    it('should generate yearly periods with day of year', () => {
      const rule: RecurrenceRule = {
        type: 'yearly',
        interval: 1,
        month: 1, // This will be overridden by dayOfYear
        dayOfYear: 100, // 100th day of the year (April 9 in non-leap years)
        count: 2
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(2);
      
      // 100th day of 2024 (leap year) is April 9
      expect(periods[0].start).toEqual(new Date(2024, 3, 9, 9, 0, 0));
      // 100th day of 2025 (non-leap year) is April 10
      expect(periods[1].start).toEqual(new Date(2025, 3, 10, 9, 0, 0));
    });
  });

  describe('previewRecurringPeriods', () => {
    it('should return single period for non-recurring', () => {
      const periodData: PeriodFormData = {
        start: basePeriod.start,
        end: basePeriod.end,
        isRecurring: false
      };

      const preview = engine.previewRecurringPeriods(periodData);
      
      expect(preview).toHaveLength(1);
      expect(preview[0].start).toEqual(basePeriod.start);
      expect(preview[0].end).toEqual(basePeriod.end);
    });

    it('should limit preview to maxPreview count', () => {
      const periodData: PeriodFormData = {
        start: basePeriod.start,
        end: basePeriod.end,
        isRecurring: true,
        recurrenceRule: {
          type: 'daily',
          interval: 1,
          count: 100 // Large count
        }
      };

      const preview = engine.previewRecurringPeriods(periodData, 5);
      
      expect(preview).toHaveLength(5);
    });

    it('should preview recurring periods correctly', () => {
      const periodData: PeriodFormData = {
        start: basePeriod.start,
        end: basePeriod.end,
        isRecurring: true,
        recurrenceRule: {
          type: 'daily',
          interval: 1,
          count: 3
        }
      };

      const preview = engine.previewRecurringPeriods(periodData);
      
      expect(preview).toHaveLength(3);
      expect(preview[0].start).toEqual(new Date(2024, 0, 15, 9, 0, 0));
      expect(preview[1].start).toEqual(new Date(2024, 0, 16, 9, 0, 0));
      expect(preview[2].start).toEqual(new Date(2024, 0, 17, 9, 0, 0));
    });
  });

  describe('generatePeriodsFromFormData', () => {
    it('should generate single period for non-recurring form data', () => {
      const formData: PeriodFormData = {
        start: basePeriod.start,
        end: basePeriod.end,
        isRecurring: false
      };

      const periods = engine.generatePeriodsFromFormData(formData);
      
      expect(periods).toHaveLength(1);
      expect(periods[0].start).toEqual(basePeriod.start);
      expect(periods[0].end).toEqual(basePeriod.end);
    });

    it('should generate multiple periods for recurring form data', () => {
      const formData: PeriodFormData = {
        start: basePeriod.start,
        end: basePeriod.end,
        isRecurring: true,
        recurrenceRule: {
          type: 'daily',
          interval: 1,
          count: 3
        }
      };

      const periods = engine.generatePeriodsFromFormData(formData);
      
      expect(periods).toHaveLength(3);
    });
  });

  describe('calculateTotalPeriods', () => {
    it('should return count when specified', () => {
      const rule: RecurrenceRule = {
        type: 'daily',
        interval: 1,
        count: 15
      };

      const total = engine.calculateTotalPeriods(basePeriod, rule);
      expect(total).toBe(15);
    });

    it('should estimate periods based on end date for daily recurrence', () => {
      const rule: RecurrenceRule = {
        type: 'daily',
        interval: 1,
        endDate: new Date(2024, 0, 25) // 10 days after start
      };

      const total = engine.calculateTotalPeriods(basePeriod, rule);
      expect(total).toBeGreaterThan(5);
      expect(total).toBeLessThan(15);
    });

    it('should estimate periods for weekly recurrence with multiple days', () => {
      const rule: RecurrenceRule = {
        type: 'weekly',
        interval: 1,
        daysOfWeek: [1, 3, 5], // 3 days per week
        endDate: new Date(2024, 1, 15) // About 4 weeks
      };

      const total = engine.calculateTotalPeriods(basePeriod, rule);
      expect(total).toBeGreaterThan(8); // At least 3 days * 3 weeks
    });

    it('should cap estimates at maximum', () => {
      const rule: RecurrenceRule = {
        type: 'daily',
        interval: 1,
        count: 2000 // Very large count
      };

      const total = engine.calculateTotalPeriods(basePeriod, rule);
      expect(total).toBe(1000); // Should be capped
    });

    it('should return default when no end condition', () => {
      const rule: RecurrenceRule = {
        type: 'daily',
        interval: 1
        // No count or endDate
      };

      const total = engine.calculateTotalPeriods(basePeriod, rule);
      expect(total).toBe(10); // Default value
    });
  });

  describe('error handling', () => {
    it('should throw error for invalid recurrence type', () => {
      const rule = {
        type: 'invalid',
        interval: 1,
        count: 5
      } as any;

      expect(() => engine.generatePeriods(basePeriod, rule)).toThrow();
    });

    it('should throw error for weekly recurrence without days', () => {
      const rule: RecurrenceRule = {
        type: 'weekly',
        interval: 1,
        count: 5
        // Missing daysOfWeek
      };

      expect(() => engine.generatePeriods(basePeriod, rule)).toThrow('Days of week must be specified');
    });

    it('should throw error for monthly recurrence without day specification', () => {
      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 1,
        count: 5
        // Missing dayOfMonth or weekOfMonth/dayOfWeek
      };

      expect(() => engine.generatePeriods(basePeriod, rule)).toThrow('Monthly recurrence must specify');
    });

    it('should throw error for yearly recurrence without month', () => {
      const rule: RecurrenceRule = {
        type: 'yearly',
        interval: 1,
        count: 3
        // Missing month
      };

      expect(() => engine.generatePeriods(basePeriod, rule)).toThrow('Month must be specified');
    });
  });

  describe('edge cases', () => {
    it('should handle leap year correctly for yearly recurrence', () => {
      const leapYearPeriod = {
        start: new Date(2024, 1, 29, 9, 0, 0), // Feb 29, 2024 (leap year)
        end: new Date(2024, 1, 29, 10, 0, 0)
      };

      const rule: RecurrenceRule = {
        type: 'yearly',
        interval: 1,
        month: 2, // February
        count: 3
      };

      const periods = engine.generatePeriods(leapYearPeriod, rule);
      
      expect(periods).toHaveLength(3);
      expect(periods[0].start).toEqual(new Date(2024, 1, 29, 9, 0, 0)); // Feb 29, 2024
      // JavaScript Date constructor adjusts Feb 29 in non-leap years to Feb 1
      expect(periods[1].start).toEqual(new Date(2025, 1, 1, 9, 0, 0)); // Feb 1, 2025 (adjusted from Feb 29)
      expect(periods[2].start).toEqual(new Date(2026, 1, 1, 9, 0, 0)); // Feb 1, 2026 (adjusted from Feb 29)
    });

    it('should handle end of month correctly for monthly recurrence', () => {
      const endOfMonthPeriod = {
        start: new Date(2024, 0, 31, 9, 0, 0), // Jan 31, 2024
        end: new Date(2024, 0, 31, 10, 0, 0)
      };

      const rule: RecurrenceRule = {
        type: 'monthly',
        interval: 1,
        dayOfMonth: 31,
        count: 12 // Try for a full year
      };

      const periods = engine.generatePeriods(endOfMonthPeriod, rule);
      
      // Same logic as above - 12 periods across 2024-2025
      expect(periods.length).toBe(12);
      periods.forEach(period => {
        expect(period.start.getDate()).toBe(31);
      });
    });

    it('should preserve time across different recurrence patterns', () => {
      const afternoonPeriod = {
        start: new Date(2024, 0, 15, 14, 30, 45), // 2:30:45 PM
        end: new Date(2024, 0, 15, 16, 15, 30)    // 4:15:30 PM
      };

      const rule: RecurrenceRule = {
        type: 'weekly',
        interval: 1,
        daysOfWeek: [1], // Monday
        count: 3
      };

      const periods = engine.generatePeriods(afternoonPeriod, rule);
      
      periods.forEach(period => {
        expect(period.start.getHours()).toBe(14);
        expect(period.start.getMinutes()).toBe(30);
        expect(period.start.getSeconds()).toBe(45);
        expect(period.end.getHours()).toBe(16);
        expect(period.end.getMinutes()).toBe(15);
        expect(period.end.getSeconds()).toBe(30);
      });
    });

    it('should handle very large intervals', () => {
      const rule: RecurrenceRule = {
        type: 'daily',
        interval: 365, // Every year (daily)
        count: 3
      };

      const periods = engine.generatePeriods(basePeriod, rule);
      
      expect(periods).toHaveLength(3);
      expect(periods[1].start.getFullYear()).toBe(2025);
      expect(periods[2].start.getFullYear()).toBe(2026);
    });
  });
});