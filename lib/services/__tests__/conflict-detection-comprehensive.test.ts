import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ConflictDetectionService } from '../conflict-detection';
import { periodService } from '../period-service';
import { prisma } from '@/lib/db';
import { PeriodFormData, Period, BookingStatus } from '@/lib/types';

// Mock the database
vi.mock('@/lib/db', () => ({
  prisma: {
    booking: {
      findUnique: vi.fn(),
    },
    resource: {
      findUnique: vi.fn(),
    },
  },
}));

// Mock the period service
vi.mock('../period-service', () => ({
  periodService: {
    findConflictingPeriods: vi.fn(),
    getPeriodById: vi.fn(),
    getPeriodsByBookingId: vi.fn(),
    getPeriodsInRange: vi.fn(),
  },
}));

describe('ConflictDetectionService - Comprehensive Tests', () => {
  let conflictDetectionService: ConflictDetectionService;
  
  beforeEach(() => {
    conflictDetectionService = new ConflictDetectionService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Edge Cases and Complex Scenarios', () => {
    it('should handle periods that exactly touch (no overlap)', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      }];

      const touchingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date('2024-01-01T12:00:00Z'), // Starts exactly when first ends
        end: new Date('2024-01-01T14:00:00Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'John Doe' } as any,
          resources: [{ id: 1, name: 'Conference Room A' } as any],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      // Since the periods exactly touch (no overlap), findConflictingPeriods should return empty array
      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([]);

      const result = await conflictDetectionService.detectConflicts(periods, [1], 1);

      // Touching periods should not be considered conflicts
      expect(result.hasConflicts).toBe(false);
      expect(result.conflicts).toHaveLength(0);
    });

    it('should handle periods with millisecond precision overlaps', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00.000Z'),
        end: new Date('2024-01-01T12:00:00.000Z'),
        isRecurring: false
      }];

      const overlappingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date('2024-01-01T11:59:59.999Z'), // Overlaps by 1 millisecond
        end: new Date('2024-01-01T14:00:00.000Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'John Doe' } as any,
          resources: [{ id: 1, name: 'Conference Room A' } as any],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([overlappingPeriod]);

      const result = await conflictDetectionService.detectConflicts(periods, [1], 1);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts).toHaveLength(1);
      expect(result.conflicts[0].overlapStart).toEqual(new Date('2024-01-01T11:59:59.999Z'));
      expect(result.conflicts[0].overlapEnd).toEqual(new Date('2024-01-01T12:00:00.000Z'));
    });

    it('should handle multiple resources with partial conflicts', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      }];

      const conflictingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date('2024-01-01T11:00:00Z'),
        end: new Date('2024-01-01T13:00:00Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'John Doe' } as any,
          resources: [
            { id: 1, name: 'Conference Room A' } as any,
            { id: 3, name: 'Conference Room C' } as any // Only Room A conflicts
          ],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([conflictingPeriod]);

      const result = await conflictDetectionService.detectConflicts(periods, [1, 2], 1);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts).toHaveLength(1);
      expect(result.conflicts[0].conflictingResources).toEqual([
        { id: 1, name: 'Conference Room A' }
      ]);
      expect(result.affectedResources).toEqual([
        { id: 1, name: 'Conference Room A' }
      ]);
    });

    it('should handle cancelled bookings correctly (should not cause conflicts)', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      }];

      const cancelledBookingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date('2024-01-01T11:00:00Z'),
        end: new Date('2024-01-01T13:00:00Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CANCELLED' as BookingStatus, // Cancelled booking
          customer: { id: 1, name: 'John Doe' } as any,
          resources: [{ id: 1, name: 'Conference Room A' } as any],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      // The findConflictingPeriods should not return cancelled bookings
      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([]);

      const result = await conflictDetectionService.detectConflicts(periods, [1], 1);

      expect(result.hasConflicts).toBe(false);
      expect(result.conflicts).toHaveLength(0);
    });

    it('should handle very large number of periods efficiently', async () => {
      // Create 100 periods
      const periods: PeriodFormData[] = Array.from({ length: 100 }, (_, i) => ({
        start: new Date(`2024-01-${String(i + 1).padStart(2, '0')}T10:00:00Z`),
        end: new Date(`2024-01-${String(i + 1).padStart(2, '0')}T12:00:00Z`),
        isRecurring: false
      }));

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([]);

      const startTime = Date.now();
      const result = await conflictDetectionService.detectConflicts(periods, [1], 1);
      const endTime = Date.now();

      expect(result.hasConflicts).toBe(false);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
      expect(periodService.findConflictingPeriods).toHaveBeenCalledTimes(100);
    });

    it('should handle periods spanning multiple days', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T22:00:00Z'),
        end: new Date('2024-01-02T02:00:00Z'), // Spans midnight
        isRecurring: false
      }];

      const conflictingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date('2024-01-02T01:00:00Z'),
        end: new Date('2024-01-02T03:00:00Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'John Doe' } as any,
          resources: [{ id: 1, name: 'Conference Room A' } as any],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([conflictingPeriod]);

      const result = await conflictDetectionService.detectConflicts(periods, [1], 1);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts[0].overlapStart).toEqual(new Date('2024-01-02T01:00:00Z'));
      expect(result.conflicts[0].overlapEnd).toEqual(new Date('2024-01-02T02:00:00Z'));
    });
  });

  describe('Resource Utilization Edge Cases', () => {
    it('should handle zero utilization correctly', async () => {
      const startDate = new Date('2024-01-01T00:00:00Z');
      const endDate = new Date('2024-01-01T24:00:00Z');
      
      const resource = { id: 1, name: 'Conference Room A' };

      vi.mocked(prisma.resource.findUnique).mockResolvedValue(resource as any);
      vi.mocked(periodService.getPeriodsInRange).mockResolvedValue([]);

      const result = await conflictDetectionService.getResourceUtilization([1], startDate, endDate);

      expect(result).toHaveLength(1);
      expect(result[0].utilizationPercentage).toBe(0);
      expect(result[0].totalBookedTime).toBe(0);
      expect(result[0].conflictingBookings).toHaveLength(0);
    });

    it('should handle 100% utilization correctly', async () => {
      const startDate = new Date('2024-01-01T00:00:00Z');
      const endDate = new Date('2024-01-01T24:00:00Z');
      
      const resource = { id: 1, name: 'Conference Room A' };
      const periods: Period[] = [{
        id: 1,
        bookingId: 1,
        start: new Date('2024-01-01T00:00:00Z'),
        end: new Date('2024-01-01T24:00:00Z'), // Full day
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 1,
          customer: { id: 1, name: 'John Doe' } as any
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      }];

      vi.mocked(prisma.resource.findUnique).mockResolvedValue(resource as any);
      vi.mocked(periodService.getPeriodsInRange).mockResolvedValue(periods);

      const result = await conflictDetectionService.getResourceUtilization([1], startDate, endDate);

      expect(result[0].utilizationPercentage).toBe(100);
      expect(result[0].totalBookedTime).toBe(24 * 60 * 60 * 1000);
    });

    it('should handle overlapping periods in utilization calculation', async () => {
      const startDate = new Date('2024-01-01T00:00:00Z');
      const endDate = new Date('2024-01-01T24:00:00Z');
      
      const resource = { id: 1, name: 'Conference Room A' };
      const periods: Period[] = [
        {
          id: 1,
          bookingId: 1,
          start: new Date('2024-01-01T10:00:00Z'),
          end: new Date('2024-01-01T14:00:00Z'), // 4 hours
          isRecurring: false,
          recurrenceRule: null,
          parentPeriodId: null,
          booking: {
            id: 1,
            customer: { id: 1, name: 'John Doe' } as any
          } as any,
          parentPeriod: null,
          childPeriods: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          createdById: null,
          updatedById: null
        },
        {
          id: 2,
          bookingId: 2,
          start: new Date('2024-01-01T12:00:00Z'),
          end: new Date('2024-01-01T16:00:00Z'), // 4 hours, overlaps 2 hours
          isRecurring: false,
          recurrenceRule: null,
          parentPeriodId: null,
          booking: {
            id: 2,
            customer: { id: 2, name: 'Jane Smith' } as any
          } as any,
          parentPeriod: null,
          childPeriods: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          createdById: null,
          updatedById: null
        }
      ];

      vi.mocked(prisma.resource.findUnique).mockResolvedValue(resource as any);
      vi.mocked(periodService.getPeriodsInRange).mockResolvedValue(periods);

      const result = await conflictDetectionService.getResourceUtilization([1], startDate, endDate);

      // Total booked time should be 8 hours (4 + 4), even though there's overlap
      // This represents the actual booking time, not unique time
      expect(result[0].totalBookedTime).toBe(8 * 60 * 60 * 1000);
      expect(result[0].utilizationPercentage).toBeCloseTo(33.33, 2); // 8/24 * 100
      expect(result[0].conflictingBookings).toHaveLength(2);
    });
  });

  describe('Internal Period Overlap Validation', () => {
    it('should detect complex overlapping scenarios', () => {
      const periods: PeriodFormData[] = [
        {
          start: new Date('2024-01-01T10:00:00Z'),
          end: new Date('2024-01-01T12:00:00Z'),
          isRecurring: false
        },
        {
          start: new Date('2024-01-01T11:00:00Z'),
          end: new Date('2024-01-01T13:00:00Z'),
          isRecurring: false
        },
        {
          start: new Date('2024-01-01T11:30:00Z'),
          end: new Date('2024-01-01T14:00:00Z'),
          isRecurring: false
        }
      ];

      const result = conflictDetectionService.validateInternalPeriodOverlaps(periods);

      expect(result.hasOverlaps).toBe(true);
      expect(result.overlaps).toHaveLength(3); // All combinations: 0-1, 0-2, 1-2
      
      // Check specific overlaps
      const overlap1 = result.overlaps.find(o => o.period1Index === 0 && o.period2Index === 1);
      expect(overlap1).toBeDefined();
      expect(overlap1?.overlapStart).toEqual(new Date('2024-01-01T11:00:00Z'));
      expect(overlap1?.overlapEnd).toEqual(new Date('2024-01-01T12:00:00Z'));

      const overlap2 = result.overlaps.find(o => o.period1Index === 0 && o.period2Index === 2);
      expect(overlap2).toBeDefined();
      expect(overlap2?.overlapStart).toEqual(new Date('2024-01-01T11:30:00Z'));
      expect(overlap2?.overlapEnd).toEqual(new Date('2024-01-01T12:00:00Z'));

      const overlap3 = result.overlaps.find(o => o.period1Index === 1 && o.period2Index === 2);
      expect(overlap3).toBeDefined();
      expect(overlap3?.overlapStart).toEqual(new Date('2024-01-01T11:30:00Z'));
      expect(overlap3?.overlapEnd).toEqual(new Date('2024-01-01T13:00:00Z'));
    });

    it('should handle periods with same start or end times', () => {
      const periods: PeriodFormData[] = [
        {
          start: new Date('2024-01-01T10:00:00Z'),
          end: new Date('2024-01-01T12:00:00Z'),
          isRecurring: false
        },
        {
          start: new Date('2024-01-01T10:00:00Z'), // Same start time
          end: new Date('2024-01-01T11:00:00Z'),
          isRecurring: false
        }
      ];

      const result = conflictDetectionService.validateInternalPeriodOverlaps(periods);

      expect(result.hasOverlaps).toBe(true);
      expect(result.overlaps).toHaveLength(1);
      expect(result.overlaps[0].overlapStart).toEqual(new Date('2024-01-01T10:00:00Z'));
      expect(result.overlaps[0].overlapEnd).toEqual(new Date('2024-01-01T11:00:00Z'));
    });

    it('should handle empty periods array', () => {
      const result = conflictDetectionService.validateInternalPeriodOverlaps([]);

      expect(result.hasOverlaps).toBe(false);
      expect(result.overlaps).toHaveLength(0);
    });

    it('should handle single period', () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      }];

      const result = conflictDetectionService.validateInternalPeriodOverlaps(periods);

      expect(result.hasOverlaps).toBe(false);
      expect(result.overlaps).toHaveLength(0);
    });
  });

  describe('Multi-Booking Conflict Summary', () => {
    it('should handle bookings with no conflicts', async () => {
      const conflictResult = {
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No conflicts detected'
      };

      const booking = {
        id: 1,
        customer: { id: 1, name: 'John Doe' }
      };

      const getBookingConflictDetailsSpy = vi.spyOn(conflictDetectionService, 'getBookingConflictDetails')
        .mockResolvedValue(conflictResult);

      vi.mocked(prisma.booking.findUnique).mockResolvedValue(booking as any);

      const result = await conflictDetectionService.getMultiBookingConflictSummary([1]);

      expect(result.totalConflicts).toBe(0);
      expect(result.conflictsByBooking).toHaveLength(1);
      expect(result.conflictsByBooking[0].conflictCount).toBe(0);
      expect(result.mostConflictedResources).toHaveLength(0);

      getBookingConflictDetailsSpy.mockRestore();
    });

    it('should handle missing bookings gracefully', async () => {
      const conflictResult = {
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No conflicts detected'
      };

      const getBookingConflictDetailsSpy = vi.spyOn(conflictDetectionService, 'getBookingConflictDetails')
        .mockResolvedValue(conflictResult);

      vi.mocked(prisma.booking.findUnique).mockResolvedValue(null);

      const result = await conflictDetectionService.getMultiBookingConflictSummary([999]);

      expect(result.totalConflicts).toBe(0);
      expect(result.conflictsByBooking).toHaveLength(0);
      expect(result.mostConflictedResources).toHaveLength(0);

      getBookingConflictDetailsSpy.mockRestore();
    });

    it('should sort resources by conflict count correctly', async () => {
      const conflictResult1 = {
        hasConflicts: true,
        conflicts: [
          {
            periodId: 1,
            conflictingPeriodId: 2,
            conflictingResources: [{ id: 1, name: 'Room A' }],
            overlapStart: new Date(),
            overlapEnd: new Date(),
            conflictingBooking: { id: 3, customerName: 'Bob', status: 'CONFIRMED' as BookingStatus }
          }
        ],
        affectedResources: [{ id: 1, name: 'Room A' }],
        message: 'Found 1 conflict(s) across 1 resource(s)'
      };

      const conflictResult2 = {
        hasConflicts: true,
        conflicts: [
          {
            periodId: 3,
            conflictingPeriodId: 4,
            conflictingResources: [{ id: 1, name: 'Room A' }, { id: 2, name: 'Room B' }],
            overlapStart: new Date(),
            overlapEnd: new Date(),
            conflictingBooking: { id: 4, customerName: 'Alice', status: 'PENDING' as BookingStatus }
          }
        ],
        affectedResources: [{ id: 1, name: 'Room A' }, { id: 2, name: 'Room B' }],
        message: 'Found 1 conflict(s) across 2 resource(s)'
      };

      const booking1 = { id: 1, customer: { id: 1, name: 'John Doe' } };
      const booking2 = { id: 2, customer: { id: 2, name: 'Jane Smith' } };

      const getBookingConflictDetailsSpy = vi.spyOn(conflictDetectionService, 'getBookingConflictDetails')
        .mockResolvedValueOnce(conflictResult1)
        .mockResolvedValueOnce(conflictResult2);

      vi.mocked(prisma.booking.findUnique)
        .mockResolvedValueOnce(booking1 as any)
        .mockResolvedValueOnce(booking2 as any);

      const result = await conflictDetectionService.getMultiBookingConflictSummary([1, 2]);

      expect(result.mostConflictedResources).toHaveLength(2);
      expect(result.mostConflictedResources[0]).toEqual({
        resourceId: 1,
        resourceName: 'Room A',
        conflictCount: 2 // Appears in both bookings
      });
      expect(result.mostConflictedResources[1]).toEqual({
        resourceId: 2,
        resourceName: 'Room B',
        conflictCount: 1 // Appears in one booking
      });

      getBookingConflictDetailsSpy.mockRestore();
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      }];

      vi.mocked(periodService.findConflictingPeriods).mockRejectedValue(new Error('Database connection failed'));

      await expect(
        conflictDetectionService.detectConflicts(periods, [1], 1)
      ).rejects.toThrow('Database connection failed');
    });

    it('should handle invalid date ranges', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T12:00:00Z'),
        end: new Date('2024-01-01T10:00:00Z'), // End before start
        isRecurring: false
      }];

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([]);

      const result = await conflictDetectionService.detectConflicts(periods, [1], 1);

      // The service should still work, but the overlap calculation might be affected
      expect(result.hasConflicts).toBe(false);
    });

    it('should handle null/undefined values in period data', async () => {
      const periods: PeriodFormData[] = [{
        start: new Date('2024-01-01T10:00:00Z'),
        end: new Date('2024-01-01T12:00:00Z'),
        isRecurring: false
      }];

      const conflictingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date('2024-01-01T11:00:00Z'),
        end: new Date('2024-01-01T13:00:00Z'),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'John Doe' } as any,
          resources: [], // Empty resources array
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([conflictingPeriod]);

      const result = await conflictDetectionService.detectConflicts(periods, [1], 1);

      // Should handle empty resources gracefully
      expect(result.hasConflicts).toBe(false);
      expect(result.conflicts).toHaveLength(0);
    });
  });
});