import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PeriodService } from '../period-service';
import { prisma } from '@/lib/db';
import { PeriodCreateInput, PeriodUpdateInput, BulkPeriodCreateInput } from '@/lib/validations/period';

// Mock Prisma
vi.mock('@/lib/db', () => ({
  prisma: {
    period: {
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      deleteMany: vi.fn(),
      findUnique: vi.fn(),
      findMany: vi.fn(),
    },
    booking: {
      findUnique: vi.fn(),
    },
    $transaction: vi.fn(),
  },
}));

describe('PeriodService', () => {
  let periodService: PeriodService;
  const mockBooking = {
    id: 1,
    customerId: 1,
    status: 'CONFIRMED',
    resources: [
      { id: 1, name: 'Conference Room A', type: 'MEETING_ROOM', basePrice: 100 },
      { id: 2, name: 'Conference Room B', type: 'MEETING_ROOM', basePrice: 150 }
    ],
    customer: { id: 1, name: '<PERSON>', email: '<EMAIL>' },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Use future dates for tests
  const futureDate1 = new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow
  const futureDate2 = new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000); // Tomorrow + 1 hour
  const futureDate3 = new Date(Date.now() + 24 * 60 * 60 * 1000 + 5 * 60 * 60 * 1000); // Tomorrow + 5 hours
  const futureDate4 = new Date(Date.now() + 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000); // Tomorrow + 6 hours

  const mockPeriod = {
    id: 1,
    bookingId: 1,
    start: futureDate1,
    end: futureDate2,
    isRecurring: false,
    recurrenceRule: null,
    parentPeriodId: null,
    booking: mockBooking,
    parentPeriod: null,
    childPeriods: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdById: null,
    updatedById: null
  };

  beforeEach(() => {
    periodService = new PeriodService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('createPeriod', () => {
    const validCreateData: PeriodCreateInput = {
      bookingId: 1,
      start: futureDate1,
      end: futureDate2,
      isRecurring: false
    };

    it('should create a period with valid data', async () => {
      // Mock booking exists
      (prisma.booking.findUnique as any).mockResolvedValue(mockBooking);
      
      // Mock no existing periods (no overlap)
      (prisma.period.findMany as any).mockResolvedValue([]);
      
      // Mock period creation
      (prisma.period.create as any).mockResolvedValue(mockPeriod);

      const result = await periodService.createPeriod(validCreateData);

      expect(prisma.booking.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { resources: true }
      });
      expect(prisma.period.findMany).toHaveBeenCalledWith({
        where: { bookingId: 1 }
      });
      expect(prisma.period.create).toHaveBeenCalledWith({
        data: {
          bookingId: 1,
          start: validCreateData.start,
          end: validCreateData.end,
          isRecurring: false,
          recurrenceRule: null,
        },
        include: {
          booking: {
            include: {
              customer: true,
              resources: true
            }
          },
          parentPeriod: true,
          childPeriods: true
        }
      });
      expect(result).toEqual(mockPeriod);
    });

    it('should reject periods with invalid time ranges', async () => {
      const invalidData = {
        ...validCreateData,
        start: futureDate2,
        end: futureDate1 // End before start
      };

      await expect(periodService.createPeriod(invalidData)).rejects.toThrow();
    });

    it('should reject periods for non-existent bookings', async () => {
      (prisma.booking.findUnique as any).mockResolvedValue(null);

      await expect(periodService.createPeriod(validCreateData)).rejects.toThrow('Booking not found');
    });

    it('should reject overlapping periods within the same booking', async () => {
      const existingPeriod = {
        id: 2,
        bookingId: 1,
        start: new Date(futureDate1.getTime() + 30 * 60 * 1000), // 30 minutes after start
        end: new Date(futureDate2.getTime() + 30 * 60 * 1000) // 30 minutes after end
      };

      (prisma.booking.findUnique as any).mockResolvedValue(mockBooking);
      (prisma.period.findMany as any).mockResolvedValue([existingPeriod]);

      await expect(periodService.createPeriod(validCreateData)).rejects.toThrow('Period overlaps with existing periods in the same booking');
    });

    it('should create recurring periods with recurrence rule', async () => {
      const recurringData = {
        ...validCreateData,
        isRecurring: true,
        recurrenceRule: {
          type: 'weekly' as const,
          interval: 1,
          daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
          count: 5
        }
      };

      const recurringPeriod = {
        ...mockPeriod,
        isRecurring: true,
        recurrenceRule: JSON.stringify(recurringData.recurrenceRule)
      };

      (prisma.booking.findUnique as any).mockResolvedValue(mockBooking);
      (prisma.period.findMany as any).mockResolvedValue([]);
      (prisma.period.create as any).mockResolvedValue(recurringPeriod);

      const result = await periodService.createPeriod(recurringData);

      expect(prisma.period.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            bookingId: 1,
            start: recurringData.start,
            end: recurringData.end,
            isRecurring: true,
            recurrenceRule: expect.stringContaining('"type":"weekly"')
          }),
          include: {
            booking: {
              include: {
                customer: true,
                resources: true
              }
            },
            parentPeriod: true,
            childPeriods: true
          }
        })
      );
      expect(result.isRecurring).toBe(true);
      expect(result.recurrenceRule).toEqual(recurringData.recurrenceRule);
    });
  });

  describe('createPeriods', () => {
    const validBulkData: BulkPeriodCreateInput = {
      bookingId: 1,
      periods: [
        {
          start: futureDate1,
          end: futureDate2,
          isRecurring: false
        },
        {
          start: futureDate3,
          end: futureDate4,
          isRecurring: false
        }
      ]
    };

    it('should create multiple periods for a booking', async () => {
      const mockPeriods = [
        { ...mockPeriod, id: 1 },
        { ...mockPeriod, id: 2, start: futureDate3, end: futureDate4 }
      ];

      (prisma.booking.findUnique as any).mockResolvedValue(mockBooking);
      (prisma.period.findMany as any).mockResolvedValue([]);
      (prisma.$transaction as any).mockImplementation(async (callback) => {
        return await callback({
          period: {
            create: vi.fn()
              .mockResolvedValueOnce(mockPeriods[0])
              .mockResolvedValueOnce(mockPeriods[1])
          }
        });
      });

      const result = await periodService.createPeriods(validBulkData);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(1);
      expect(result[1].id).toBe(2);
    });

    it('should reject bulk creation for non-existent booking', async () => {
      (prisma.booking.findUnique as any).mockResolvedValue(null);

      await expect(periodService.createPeriods(validBulkData)).rejects.toThrow('Booking not found');
    });

    it('should reject bulk creation with overlapping existing periods', async () => {
      const existingPeriod = {
        id: 3,
        bookingId: 1,
        start: new Date(futureDate1.getTime() + 30 * 60 * 1000), // 30 minutes after start
        end: new Date(futureDate2.getTime() + 30 * 60 * 1000) // 30 minutes after end
      };

      (prisma.booking.findUnique as any).mockResolvedValue(mockBooking);
      (prisma.period.findMany as any).mockResolvedValue([existingPeriod]);

      await expect(periodService.createPeriods(validBulkData)).rejects.toThrow(/overlaps with existing periods/);
    });
  });

  describe('updatePeriod', () => {
    const validUpdateData: PeriodUpdateInput = {
      start: futureDate2,
      end: new Date(futureDate2.getTime() + 60 * 60 * 1000), // 1 hour later
      isRecurring: false
    };

    it('should update a period with valid data', async () => {
      const updatedPeriod = {
        ...mockPeriod,
        start: validUpdateData.start,
        end: validUpdateData.end
      };

      (prisma.period.findUnique as any).mockResolvedValue(mockPeriod);
      (prisma.period.findMany as any).mockResolvedValue([]); // No other periods
      (prisma.period.update as any).mockResolvedValue(updatedPeriod);

      const result = await periodService.updatePeriod(1, validUpdateData);

      expect(prisma.period.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { booking: true }
      });
      expect(prisma.period.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          start: validUpdateData.start,
          end: validUpdateData.end,
          isRecurring: false,
          recurrenceRule: null,
        },
        include: {
          booking: {
            include: {
              customer: true,
              resources: true
            }
          },
          parentPeriod: true,
          childPeriods: true
        }
      });
      expect(result.start).toEqual(validUpdateData.start);
      expect(result.end).toEqual(validUpdateData.end);
    });

    it('should reject updates for non-existent periods', async () => {
      (prisma.period.findUnique as any).mockResolvedValue(null);

      await expect(periodService.updatePeriod(999, validUpdateData)).rejects.toThrow('Period not found');
    });

    it('should reject updates that would create overlaps', async () => {
      const otherPeriod = {
        id: 2,
        bookingId: 1,
        start: new Date(futureDate2.getTime() + 30 * 60 * 1000), // 30 minutes after update start
        end: new Date(futureDate2.getTime() + 90 * 60 * 1000) // 90 minutes after update start
      };

      (prisma.period.findUnique as any).mockResolvedValue(mockPeriod);
      (prisma.period.findMany as any).mockResolvedValue([otherPeriod]);

      await expect(periodService.updatePeriod(1, validUpdateData)).rejects.toThrow('Updated period would overlap with existing periods in the same booking');
    });
  });

  describe('deletePeriod', () => {
    it('should delete a single period', async () => {
      (prisma.period.findUnique as any).mockResolvedValue(mockPeriod);
      (prisma.period.delete as any).mockResolvedValue(mockPeriod);

      await periodService.deletePeriod(1);

      expect(prisma.period.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { 
          childPeriods: true,
          parentPeriod: true
        }
      });
      expect(prisma.period.delete).toHaveBeenCalledWith({
        where: { id: 1 }
      });
    });

    it('should reject deletion of non-existent periods', async () => {
      (prisma.period.findUnique as any).mockResolvedValue(null);

      await expect(periodService.deletePeriod(999)).rejects.toThrow('Period not found');
    });

    it('should delete entire recurring series when requested', async () => {
      const recurringPeriod = {
        ...mockPeriod,
        isRecurring: true,
        childPeriods: [
          { id: 2, parentPeriodId: 1 },
          { id: 3, parentPeriodId: 1 }
        ]
      };

      (prisma.period.findUnique as any).mockResolvedValue(recurringPeriod);
      (prisma.$transaction as any).mockImplementation(async (callback) => {
        return await callback({
          period: {
            deleteMany: vi.fn(),
            delete: vi.fn()
          }
        });
      });

      await periodService.deletePeriod(1, true);

      expect(prisma.$transaction).toHaveBeenCalled();
    });

    it('should delete child periods and parent when deleting from recurring series', async () => {
      const childPeriod = {
        ...mockPeriod,
        id: 2,
        parentPeriodId: 1,
        isRecurring: true,
        childPeriods: []
      };

      (prisma.period.findUnique as any).mockResolvedValue(childPeriod);
      (prisma.$transaction as any).mockImplementation(async (callback) => {
        return await callback({
          period: {
            deleteMany: vi.fn(),
            delete: vi.fn()
          }
        });
      });

      await periodService.deletePeriod(2, true);

      expect(prisma.$transaction).toHaveBeenCalled();
    });
  });

  describe('getPeriodById', () => {
    it('should return a period by ID', async () => {
      (prisma.period.findUnique as any).mockResolvedValue(mockPeriod);

      const result = await periodService.getPeriodById(1);

      expect(prisma.period.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: {
          booking: {
            include: {
              customer: true,
              resources: true
            }
          },
          parentPeriod: true,
          childPeriods: true
        }
      });
      expect(result).toEqual(mockPeriod);
    });

    it('should return null for non-existent period', async () => {
      (prisma.period.findUnique as any).mockResolvedValue(null);

      const result = await periodService.getPeriodById(999);

      expect(result).toBeNull();
    });
  });

  describe('getPeriodsByBookingId', () => {
    it('should return all periods for a booking', async () => {
      const mockPeriods = [
        { ...mockPeriod, id: 1 },
        { ...mockPeriod, id: 2, start: futureDate3, end: futureDate4 }
      ];

      (prisma.period.findMany as any).mockResolvedValue(mockPeriods);

      const result = await periodService.getPeriodsByBookingId(1);

      expect(prisma.period.findMany).toHaveBeenCalledWith({
        where: { bookingId: 1 },
        include: {
          booking: {
            include: {
              customer: true,
              resources: true
            }
          },
          parentPeriod: true,
          childPeriods: true
        },
        orderBy: { start: 'asc' }
      });
      expect(result).toHaveLength(2);
    });

    it('should return empty array for booking with no periods', async () => {
      (prisma.period.findMany as any).mockResolvedValue([]);

      const result = await periodService.getPeriodsByBookingId(1);

      expect(result).toEqual([]);
    });
  });

  describe('findConflictingPeriods', () => {
    it('should find periods that conflict with given time range and resources', async () => {
      const conflictingPeriod = {
        ...mockPeriod,
        id: 2,
        start: new Date(futureDate1.getTime() + 30 * 60 * 1000), // 30 minutes after start
        end: new Date(futureDate2.getTime() + 30 * 60 * 1000) // 30 minutes after end
      };

      (prisma.period.findMany as any).mockResolvedValue([conflictingPeriod]);

      const result = await periodService.findConflictingPeriods(
        [1, 2],
        futureDate1,
        futureDate2
      );

      expect(prisma.period.findMany).toHaveBeenCalledWith({
        where: {
          booking: {
            id: undefined,
            status: { in: ['PENDING', 'CONFIRMED'] },
            resources: {
              some: {
                id: { in: [1, 2] }
              }
            }
          },
          AND: [
            { start: { lt: futureDate2 } },   // period.start < new_end
            { end: { gt: futureDate1 } }      // period.end > new_start
          ]
        },
        include: {
          booking: {
            include: {
              customer: true,
              resources: true
            }
          },
          parentPeriod: true,
          childPeriods: true
        }
      });
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(2);
    });

    it('should exclude specified booking from conflict check', async () => {
      (prisma.period.findMany as any).mockResolvedValue([]);

      await periodService.findConflictingPeriods(
        [1, 2],
        futureDate1,
        futureDate2,
        1 // Exclude booking ID 1
      );

      expect(prisma.period.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            booking: expect.objectContaining({
              id: { not: 1 }
            })
          })
        })
      );
    });

    it('should only consider periods from active bookings', async () => {
      (prisma.period.findMany as any).mockResolvedValue([]);
      
      await periodService.findConflictingPeriods(
        [1, 2],
        futureDate1,
        futureDate2
      );

      expect(prisma.period.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            booking: expect.objectContaining({
              status: { in: ['PENDING', 'CONFIRMED'] }
            })
          })
        })
      );
    });
  });

  describe('getPeriodsInRange', () => {
    it('should return periods within date range', async () => {
      const mockPeriods = [mockPeriod];
      (prisma.period.findMany as any).mockResolvedValue(mockPeriods);

      const rangeStart = new Date(futureDate1.getTime() - 60 * 60 * 1000); // 1 hour before
      const rangeEnd = new Date(futureDate2.getTime() + 60 * 60 * 1000); // 1 hour after
      
      const result = await periodService.getPeriodsInRange(rangeStart, rangeEnd);

      expect(prisma.period.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { AND: [{ start: { gte: rangeStart } }, { start: { lte: rangeEnd } }] },
            { AND: [{ end: { gte: rangeStart } }, { end: { lte: rangeEnd } }] },
            { AND: [{ start: { lte: rangeStart } }, { end: { gte: rangeEnd } }] }
          ]
        },
        include: {
          booking: {
            include: {
              customer: true,
              resources: true
            }
          },
          parentPeriod: true,
          childPeriods: true
        },
        orderBy: { start: 'asc' }
      });
      expect(result).toHaveLength(1);
    });

    it('should filter by resource IDs when provided', async () => {
      (prisma.period.findMany as any).mockResolvedValue([]);

      const rangeStart2 = new Date(futureDate1.getTime() - 60 * 60 * 1000);
      const rangeEnd2 = new Date(futureDate2.getTime() + 60 * 60 * 1000);
      
      await periodService.getPeriodsInRange(rangeStart2, rangeEnd2, [1, 2]);

      expect(prisma.period.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            booking: {
              resources: {
                some: {
                  id: { in: [1, 2] }
                }
              }
            }
          })
        })
      );
    });
  });

  describe('mapPrismaToType', () => {
    it('should correctly map Prisma result to Period type', async () => {
      const prismaResult = {
        ...mockPeriod,
        recurrenceRule: JSON.stringify({
          type: 'weekly',
          interval: 1,
          daysOfWeek: [1, 3, 5]
        })
      };

      (prisma.period.findUnique as any).mockResolvedValue(prismaResult);

      const result = await periodService.getPeriodById(1);

      expect(result?.recurrenceRule).toEqual({
        type: 'weekly',
        interval: 1,
        daysOfWeek: [1, 3, 5]
      });
    });

    it('should handle null recurrence rule', async () => {
      (prisma.period.findUnique as any).mockResolvedValue(mockPeriod);

      const result = await periodService.getPeriodById(1);

      expect(result?.recurrenceRule).toBeNull();
    });
  });
});