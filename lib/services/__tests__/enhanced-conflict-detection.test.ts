import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedConflictDetectionService } from '../enhanced-conflict-detection';
import { periodService } from '../period-service';
import { PeriodFormData, Period, BookingStatus } from '@/lib/types';

// Mock the period service
vi.mock('../period-service', () => ({
  periodService: {
    findConflictingPeriods: vi.fn(),
    getPeriodById: vi.fn(),
    getPeriodsByBookingId: vi.fn(),
    getPeriodsInRange: vi.fn(),
  },
}));

describe('EnhancedConflictDetectionService', () => {
  let service: EnhancedConflictDetectionService;
  
  beforeEach(() => {
    service = new EnhancedConflictDetectionService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('detectConflicts', () => {
    const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000);
    const tomorrowPlus2Hours = new Date(tomorrow.getTime() + 2 * 60 * 60 * 1000);
    const tomorrowPlus1Hour = new Date(tomorrow.getTime() + 1 * 60 * 60 * 1000);
    const tomorrowPlus3Hours = new Date(tomorrow.getTime() + 3 * 60 * 60 * 1000);

    it('should return no conflicts when no periods provided', async () => {
      const result = await service.detectConflicts([], [1, 2]);
      
      expect(result).toEqual({
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No periods to check',
        summary: {
          totalOverlapDuration: 0,
          highSeverityConflicts: 0,
          mediumSeverityConflicts: 0,
          lowSeverityConflicts: 0,
          averageOverlapMinutes: 0
        },
        resolutionSuggestions: {
          alternativeTimeSlots: [],
          alternativeResources: [],
          mitigationStrategies: []
        }
      });
    });

    it('should return no conflicts when no resources provided', async () => {
      const periods: PeriodFormData[] = [{
        start: tomorrow,
        end: tomorrowPlus2Hours,
        isRecurring: false
      }];

      const result = await service.detectConflicts(periods, []);
      
      expect(result.hasConflicts).toBe(false);
      expect(result.message).toBe('No resources to check');
    });

    it('should validate period integrity and throw error for invalid periods', async () => {
      const invalidPeriods: PeriodFormData[] = [{
        start: tomorrowPlus2Hours,
        end: tomorrow, // End before start
        isRecurring: false
      }];

      await expect(service.detectConflicts(invalidPeriods, [1])).rejects.toThrow(
        'Period validation failed: Period 1: End time must be after start time'
      );
    });

    it('should detect HIGH severity conflicts with confirmed bookings', async () => {
      const periods: PeriodFormData[] = [{
        start: tomorrow,
        end: tomorrowPlus2Hours,
        isRecurring: false
      }];

      const conflictingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: tomorrowPlus1Hour,
        end: tomorrowPlus3Hours,
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'John Doe' } as any,
          resources: [{ id: 1, name: 'Conference Room A' } as any],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([conflictingPeriod]);

      const result = await service.detectConflicts(periods, [1]);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts).toHaveLength(1);
      expect(result.conflicts[0].severity).toBe('HIGH');
      expect(result.conflicts[0].overlapDurationMinutes).toBe(60); // 1 hour overlap
      expect(result.summary.highSeverityConflicts).toBe(1);
      expect(result.summary.mediumSeverityConflicts).toBe(0);
      expect(result.summary.lowSeverityConflicts).toBe(0);
    });

    it('should detect MEDIUM severity conflicts with short confirmed booking overlaps', async () => {
      const periods: PeriodFormData[] = [{
        start: tomorrow,
        end: tomorrowPlus1Hour,
        isRecurring: false
      }];

      const conflictingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date(tomorrow.getTime() + 30 * 60 * 1000), // 30 min after start
        end: new Date(tomorrow.getTime() + 90 * 60 * 1000), // 30 min overlap
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'Jane Smith' } as any,
          resources: [{ id: 1, name: 'Meeting Room B' } as any],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([conflictingPeriod]);

      const result = await service.detectConflicts(periods, [1]);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts[0].severity).toBe('MEDIUM');
      expect(result.conflicts[0].overlapDurationMinutes).toBe(30);
      expect(result.summary.mediumSeverityConflicts).toBe(1);
    });

    it('should detect LOW severity conflicts with pending bookings', async () => {
      const periods: PeriodFormData[] = [{
        start: tomorrow,
        end: tomorrowPlus1Hour,
        isRecurring: false
      }];

      const conflictingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: new Date(tomorrow.getTime() + 45 * 60 * 1000), // 45 min after start
        end: new Date(tomorrow.getTime() + 75 * 60 * 1000), // 15 min overlap
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'PENDING' as BookingStatus,
          customer: { id: 1, name: 'Bob Johnson' } as any,
          resources: [{ id: 1, name: 'Study Room C' } as any],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([conflictingPeriod]);

      const result = await service.detectConflicts(periods, [1]);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts[0].severity).toBe('LOW');
      expect(result.conflicts[0].overlapDurationMinutes).toBe(15);
      expect(result.summary.lowSeverityConflicts).toBe(1);
    });

    it('should generate appropriate resolution suggestions', async () => {
      const periods: PeriodFormData[] = [{
        start: tomorrow,
        end: tomorrowPlus2Hours,
        isRecurring: false
      }];

      const conflictingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: tomorrowPlus1Hour,
        end: tomorrowPlus3Hours,
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'Alice Cooper' } as any,
          resources: [{ id: 1, name: 'Main Hall' } as any],
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods)
        .mockResolvedValueOnce([conflictingPeriod]) // Initial conflict check
        .mockResolvedValueOnce([]); // Alternative time slot check (no conflicts)

      const result = await service.detectConflicts(periods, [1]);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts[0].suggestedResolution).toContain('CRITICAL');
      expect(result.conflicts[0].suggestedResolution).toContain('Main Hall');
      expect(result.resolutionSuggestions.mitigationStrategies).toContain(
        'Prioritize alternative time slots over resource changes'
      );
    });

    it('should handle multiple conflicts with different severities', async () => {
      const periods: PeriodFormData[] = [
        {
          start: tomorrow,
          end: tomorrowPlus1Hour,
          isRecurring: false
        },
        {
          start: tomorrowPlus2Hours,
          end: tomorrowPlus3Hours,
          isRecurring: false
        }
      ];

      const highSeverityConflict: Period = {
        id: 1,
        bookingId: 2,
        start: new Date(tomorrow.getTime() + 30 * 60 * 1000),
        end: new Date(tomorrow.getTime() + 90 * 60 * 1000),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'High Priority User' } as any,
          resources: [{ id: 1, name: 'Room A' }, { id: 2, name: 'Room B' }, { id: 3, name: 'Room C' }] as any,
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      const lowSeverityConflict: Period = {
        id: 2,
        bookingId: 3,
        start: new Date(tomorrowPlus2Hours.getTime() + 15 * 60 * 1000),
        end: new Date(tomorrowPlus2Hours.getTime() + 45 * 60 * 1000),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 3,
          customerId: 2,
          status: 'PENDING' as BookingStatus,
          customer: { id: 2, name: 'Low Priority User' } as any,
          resources: [{ id: 1, name: 'Room A' }] as any,
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods)
        .mockResolvedValueOnce([highSeverityConflict])
        .mockResolvedValueOnce([lowSeverityConflict]);

      const result = await service.detectConflicts(periods, [1, 2, 3]);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts).toHaveLength(2);
      expect(result.summary.highSeverityConflicts).toBe(1);
      expect(result.summary.lowSeverityConflicts).toBe(1);
      expect(result.message).toContain('2 conflict(s)');
      expect(result.message).toContain('1 critical, 1 minor');
    });

    it('should detect HIGH severity for multiple resource conflicts', async () => {
      const periods: PeriodFormData[] = [{
        start: tomorrow,
        end: tomorrowPlus1Hour,
        isRecurring: false
      }];

      const multiResourceConflict: Period = {
        id: 1,
        bookingId: 2,
        start: new Date(tomorrow.getTime() + 15 * 60 * 1000),
        end: new Date(tomorrow.getTime() + 45 * 60 * 1000),
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'PENDING' as BookingStatus, // Even pending becomes HIGH when many resources affected
          customer: { id: 1, name: 'Resource Hog' } as any,
          resources: [
            { id: 1, name: 'Room A' },
            { id: 2, name: 'Room B' },
            { id: 3, name: 'Room C' }
          ] as any,
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([multiResourceConflict]);

      const result = await service.detectConflicts(periods, [1, 2, 3]);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts[0].severity).toBe('HIGH');
      expect(result.conflicts[0].conflictingResources).toHaveLength(3);
      expect(result.summary.highSeverityConflicts).toBe(1);
    });

    it('should calculate accurate summary statistics', async () => {
      const periods: PeriodFormData[] = [{
        start: tomorrow,
        end: tomorrowPlus2Hours,
        isRecurring: false
      }];

      const conflict1: Period = {
        id: 1,
        bookingId: 2,
        start: new Date(tomorrow.getTime() + 30 * 60 * 1000),
        end: new Date(tomorrow.getTime() + 90 * 60 * 1000), // 60 min overlap
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'User 1' } as any,
          resources: [{ id: 1, name: 'Room A' }] as any,
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      const conflict2: Period = {
        id: 2,
        bookingId: 3,
        start: new Date(tomorrow.getTime() + 90 * 60 * 1000),
        end: new Date(tomorrow.getTime() + 150 * 60 * 1000), // 30 min overlap
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 3,
          customerId: 2,
          status: 'PENDING' as BookingStatus,
          customer: { id: 2, name: 'User 2' } as any,
          resources: [{ id: 1, name: 'Room A' }] as any,
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([conflict1, conflict2]);

      const result = await service.detectConflicts(periods, [1]);

      expect(result.hasConflicts).toBe(true);
      expect(result.conflicts).toHaveLength(2);
      expect(result.summary.totalOverlapDuration).toBe(90 * 60 * 1000); // 90 minutes in ms
      expect(result.summary.averageOverlapMinutes).toBe(45); // (60 + 30) / 2
      expect(result.summary.mediumSeverityConflicts).toBe(1); // Short confirmed booking
      expect(result.summary.lowSeverityConflicts).toBe(1); // Pending booking
    });

    it('should handle edge case of exactly touching periods (no overlap)', async () => {
      const periods: PeriodFormData[] = [{
        start: tomorrow,
        end: tomorrowPlus1Hour,
        isRecurring: false
      }];

      // Period that starts exactly when our period ends
      const touchingPeriod: Period = {
        id: 1,
        bookingId: 2,
        start: tomorrowPlus1Hour, // Starts exactly when our period ends
        end: tomorrowPlus2Hours,
        isRecurring: false,
        recurrenceRule: null,
        parentPeriodId: null,
        booking: {
          id: 2,
          customerId: 1,
          status: 'CONFIRMED' as BookingStatus,
          customer: { id: 1, name: 'Touching User' } as any,
          resources: [{ id: 1, name: 'Room A' }] as any,
          periods: [],
          caterings: [],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any,
        parentPeriod: null,
        childPeriods: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdById: null,
        updatedById: null
      };

      // Since periods only touch but don't overlap, findConflictingPeriods should return empty
      vi.mocked(periodService.findConflictingPeriods).mockResolvedValue([]);

      const result = await service.detectConflicts(periods, [1]);

      expect(result.hasConflicts).toBe(false);
      expect(result.conflicts).toHaveLength(0);
    });
  });

  describe('period validation', () => {
    it('should reject periods that are too short', async () => {
      const shortPeriod: PeriodFormData[] = [{
        start: new Date(Date.now() + 60 * 60 * 1000),
        end: new Date(Date.now() + 60 * 60 * 1000 + 10 * 60 * 1000), // Only 10 minutes
        isRecurring: false
      }];

      await expect(service.detectConflicts(shortPeriod, [1])).rejects.toThrow(
        'Period validation failed: Period 1: Duration must be at least 15 minutes'
      );
    });

    it('should reject overlapping periods within the same booking', async () => {
      const overlappingPeriods: PeriodFormData[] = [
        {
          start: new Date(Date.now() + 60 * 60 * 1000),
          end: new Date(Date.now() + 120 * 60 * 1000),
          isRecurring: false
        },
        {
          start: new Date(Date.now() + 90 * 60 * 1000), // Overlaps with first period
          end: new Date(Date.now() + 150 * 60 * 1000),
          isRecurring: false
        }
      ];

      await expect(service.detectConflicts(overlappingPeriods, [1])).rejects.toThrow(
        'Period validation failed: Period 1 overlaps with period 2'
      );
    });
  });
});