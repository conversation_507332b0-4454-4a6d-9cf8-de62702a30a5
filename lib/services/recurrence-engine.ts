import { RecurrenceRule, PeriodFormData } from '@/lib/types';
import { recurrenceRuleSchema } from '@/lib/validations/period';
import { z } from 'zod';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface GeneratedPeriod {
  start: Date;
  end: Date;
}

export class RecurrenceEngine {
  /**
   * Generate recurring periods based on a base period and recurrence rule
   */
  generatePeriods(
    basePeriod: { start: Date; end: Date },
    rule: RecurrenceRule
  ): GeneratedPeriod[] {
    // Validate the recurrence rule first
    const validation = this.validateRecurrenceRule(rule);
    if (!validation.isValid) {
      throw new Error(`Invalid recurrence rule: ${validation.errors.join(', ')}`);
    }

    switch (rule.type) {
      case 'daily':
        return this.generateDailyPeriods(basePeriod, rule);
      case 'weekly':
        return this.generateWeeklyPeriods(basePeriod, rule);
      case 'monthly':
        return this.generateMonthlyPeriods(basePeriod, rule);
      case 'yearly':
        return this.generateYearlyPeriods(basePeriod, rule);
      default:
        throw new Error(`Unsupported recurrence type: ${rule.type}`);
    }
  }

  /**
   * Generate periods for daily recurrence
   */
  private generateDailyPeriods(
    basePeriod: { start: Date; end: Date },
    rule: RecurrenceRule
  ): GeneratedPeriod[] {
    const periods: GeneratedPeriod[] = [];
    const duration = basePeriod.end.getTime() - basePeriod.start.getTime();
    
    let currentStart = new Date(basePeriod.start);
    let count = 0;

    while (this.shouldContinueGeneration(currentStart, rule, count)) {
      const currentEnd = new Date(currentStart.getTime() + duration);
      
      periods.push({
        start: new Date(currentStart),
        end: new Date(currentEnd)
      });

      // Move to next occurrence
      currentStart.setDate(currentStart.getDate() + rule.interval);
      count++;
    }

    return periods;
  }

  /**
   * Generate periods for weekly recurrence
   */
  private generateWeeklyPeriods(
    basePeriod: { start: Date; end: Date },
    rule: RecurrenceRule
  ): GeneratedPeriod[] {
    if (!rule.daysOfWeek || rule.daysOfWeek.length === 0) {
      throw new Error('Days of week must be specified for weekly recurrence');
    }

    const periods: GeneratedPeriod[] = [];
    const duration = basePeriod.end.getTime() - basePeriod.start.getTime();
    
    // Start from the beginning of the week containing the base period
    const weekStart = new Date(basePeriod.start);
    weekStart.setDate(weekStart.getDate() - weekStart.getDay()); // Go to Sunday
    weekStart.setHours(basePeriod.start.getHours(), basePeriod.start.getMinutes(), basePeriod.start.getSeconds(), basePeriod.start.getMilliseconds());

    let currentWeek = new Date(weekStart);
    let totalCount = 0;

    while (this.shouldContinueGeneration(currentWeek, rule, totalCount)) {
      // Generate periods for each specified day of the week
      for (const dayOfWeek of rule.daysOfWeek) {
        const periodStart = new Date(currentWeek);
        periodStart.setDate(currentWeek.getDate() + dayOfWeek);
        
        // Skip if this period is before the original base period
        if (periodStart < basePeriod.start) {
          continue;
        }

        // Check if we should still generate periods
        if (!this.shouldContinueGeneration(periodStart, rule, totalCount)) {
          break;
        }

        const periodEnd = new Date(periodStart.getTime() + duration);
        
        periods.push({
          start: new Date(periodStart),
          end: new Date(periodEnd)
        });

        totalCount++;
      }

      // Move to next week interval
      currentWeek.setDate(currentWeek.getDate() + (7 * rule.interval));
    }

    return periods;
  }

  /**
   * Generate periods for monthly recurrence
   */
  private generateMonthlyPeriods(
    basePeriod: { start: Date; end: Date },
    rule: RecurrenceRule
  ): GeneratedPeriod[] {
    const periods: GeneratedPeriod[] = [];
    const duration = basePeriod.end.getTime() - basePeriod.start.getTime();
    
    let currentDate = new Date(basePeriod.start);
    let count = 0;

    while (this.shouldContinueGeneration(currentDate, rule, count)) {
      let periodStart: Date;

      if (rule.dayOfMonth !== undefined) {
        // Monthly by specific day of month
        periodStart = this.getMonthlyByDayOfMonth(currentDate, rule.dayOfMonth, basePeriod.start) ?? new Date();
      } else if (rule.weekOfMonth !== undefined && rule.dayOfWeek !== undefined) {
        // Monthly by relative day (e.g., first Monday, last Friday)
        periodStart = this.getMonthlyByRelativeDay(currentDate, rule.weekOfMonth, rule.dayOfWeek, basePeriod.start) ?? new Date();
      } else {
        throw new Error('Monthly recurrence must specify either dayOfMonth or weekOfMonth/dayOfWeek');
      }

      // Skip if we couldn't generate a valid date for this month
      if (!periodStart) {
        currentDate.setMonth(currentDate.getMonth() + rule.interval);
        continue;
      }

      // Check if we should still generate periods
      if (!this.shouldContinueGeneration(periodStart, rule, count)) {
        break;
      }

      const periodEnd = new Date(periodStart.getTime() + duration);
      
      periods.push({
        start: new Date(periodStart),
        end: new Date(periodEnd)
      });

      count++;
      currentDate.setMonth(currentDate.getMonth() + rule.interval);
    }

    return periods;
  }

  /**
   * Generate periods for yearly recurrence
   */
  private generateYearlyPeriods(
    basePeriod: { start: Date; end: Date },
    rule: RecurrenceRule
  ): GeneratedPeriod[] {
    if (!rule.month) {
      throw new Error('Month must be specified for yearly recurrence');
    }

    const periods: GeneratedPeriod[] = [];
    const duration = basePeriod.end.getTime() - basePeriod.start.getTime();
    
    let currentYear = basePeriod.start.getFullYear();
    let count = 0;

    while (true) {
      const periodStart = new Date(basePeriod.start);
      periodStart.setFullYear(currentYear);
      periodStart.setMonth(rule.month - 1); // Month is 1-based in rule, 0-based in Date

      // If dayOfYear is specified, use it instead of the original day
      if (rule.dayOfYear !== undefined) {
        const yearStart = new Date(currentYear, 0, 1);
        periodStart.setTime(yearStart.getTime() + (rule.dayOfYear - 1) * 24 * 60 * 60 * 1000);
        periodStart.setHours(basePeriod.start.getHours(), basePeriod.start.getMinutes(), basePeriod.start.getSeconds(), basePeriod.start.getMilliseconds());
      }

      // Check if we should still generate periods
      if (!this.shouldContinueGeneration(periodStart, rule, count)) {
        break;
      }

      const periodEnd = new Date(periodStart.getTime() + duration);
      
      periods.push({
        start: new Date(periodStart),
        end: new Date(periodEnd)
      });

      count++;
      currentYear += rule.interval;
    }

    return periods;
  }

  /**
   * Get monthly period by specific day of month
   */
  private getMonthlyByDayOfMonth(
    currentDate: Date,
    dayOfMonth: number,
    baseTime: Date
  ): Date | null {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Create a date with the desired day
    const periodStart = new Date(year, month, dayOfMonth);
    
    // Check if the day exists in this month (e.g., February 30th doesn't exist)
    // If the month rolled over or the date is different, the day doesn't exist
    if (periodStart.getMonth() !== month || periodStart.getDate() !== dayOfMonth) {
      return null;
    }

    // Set the time components
    periodStart.setHours(baseTime.getHours(), baseTime.getMinutes(), baseTime.getSeconds(), baseTime.getMilliseconds());

    return periodStart;
  }

  /**
   * Get monthly period by relative day (e.g., first Monday, last Friday)
   */
  private getMonthlyByRelativeDay(
    currentDate: Date,
    weekOfMonth: number,
    dayOfWeek: number,
    baseTime: Date
  ): Date | null {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    let periodStart: Date;

    if (weekOfMonth === -1) {
      // Last occurrence of the day in the month
      const lastDayOfMonth = new Date(year, month + 1, 0);
      const lastDayOfWeek = lastDayOfMonth.getDay();
      
      let daysBack = (lastDayOfWeek - dayOfWeek + 7) % 7;
      periodStart = new Date(year, month, lastDayOfMonth.getDate() - daysBack);
    } else {
      // Nth occurrence of the day in the month (1-4)
      const firstDayOfMonth = new Date(year, month, 1);
      const firstDayOfWeek = firstDayOfMonth.getDay();
      
      let daysForward = (dayOfWeek - firstDayOfWeek + 7) % 7;
      let targetDate = 1 + daysForward + ((weekOfMonth - 1) * 7);
      
      periodStart = new Date(year, month, targetDate);
      
      // Check if this date exists in the month
      if (periodStart.getMonth() !== month) {
        return null;
      }
    }

    periodStart.setHours(baseTime.getHours(), baseTime.getMinutes(), baseTime.getSeconds(), baseTime.getMilliseconds());
    return periodStart;
  }

  /**
   * Check if we should continue generating periods based on end conditions
   */
  private shouldContinueGeneration(
    currentDate: Date,
    rule: RecurrenceRule,
    count: number
  ): boolean {
    // Check count limit
    if (rule.count !== undefined && count >= rule.count) {
      return false;
    }

    // Check end date
    if (rule.endDate !== undefined && currentDate > rule.endDate) {
      return false;
    }

    // Prevent infinite loops - set a reasonable maximum
    if (count >= 1000) {
      return false;
    }

    return true;
  }

  /**
   * Validate a recurrence rule
   */
  validateRecurrenceRule(rule: RecurrenceRule): ValidationResult {
    try {
      recurrenceRuleSchema.parse(rule);
      return { isValid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          isValid: false,
          errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        };
      }
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : 'Unknown validation error']
      };
    }
  }

  /**
   * Preview generated recurring periods without creating them
   * This is useful for showing users what periods will be created
   */
  previewRecurringPeriods(
    basePeriod: PeriodFormData,
    maxPreview: number = 10
  ): GeneratedPeriod[] {
    if (!basePeriod.isRecurring || !basePeriod.recurrenceRule) {
      return [{ start: basePeriod.start, end: basePeriod.end }];
    }

    // Create a limited version of the rule for preview
    const previewRule: RecurrenceRule = {
      ...basePeriod.recurrenceRule,
      count: Math.min(basePeriod.recurrenceRule.count || maxPreview, maxPreview)
    };

    // Remove endDate for preview to avoid cutting off early
    if (previewRule.endDate) {
      delete previewRule.endDate;
    }

    return this.generatePeriods(basePeriod, previewRule);
  }

  /**
   * Generate periods for form data, handling both single and recurring periods
   */
  generatePeriodsFromFormData(periodData: PeriodFormData): GeneratedPeriod[] {
    if (!periodData.isRecurring || !periodData.recurrenceRule) {
      return [{ start: periodData.start, end: periodData.end }];
    }

    return this.generatePeriods(periodData, periodData.recurrenceRule);
  }

  /**
   * Calculate the total number of periods that would be generated
   * Useful for validation and user feedback
   */
  calculateTotalPeriods(
    basePeriod: { start: Date; end: Date },
    rule: RecurrenceRule
  ): number {
    // For performance, we'll estimate rather than generate all periods
    if (rule.count !== undefined) {
      return Math.min(rule.count, 1000); // Cap at reasonable maximum
    }

    if (rule.endDate !== undefined) {
      const totalDays = Math.ceil((rule.endDate.getTime() - basePeriod.start.getTime()) / (1000 * 60 * 60 * 24));
      
      switch (rule.type) {
        case 'daily':
          return Math.min(Math.ceil(totalDays / rule.interval), 1000);
        case 'weekly':
          const weeksCount = Math.ceil(totalDays / (7 * rule.interval));
          return Math.min(weeksCount * (rule.daysOfWeek?.length || 1), 1000);
        case 'monthly':
          const monthsCount = Math.ceil(totalDays / (30 * rule.interval)); // Rough estimate
          return Math.min(monthsCount, 1000);
        case 'yearly':
          const yearsCount = Math.ceil(totalDays / (365 * rule.interval)); // Rough estimate
          return Math.min(yearsCount, 1000);
        default:
          return 1;
      }
    }

    // If no end condition is specified, return a reasonable default
    return 10;
  }
}

// Export a singleton instance
export const recurrenceEngine = new RecurrenceEngine();