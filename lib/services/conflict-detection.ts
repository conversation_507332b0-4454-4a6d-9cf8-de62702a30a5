import { prisma } from '@/lib/db';
import { 
  Period, 
  PeriodFormData, 
  PeriodConflict, 
  ConflictCheckResult,
  BookingStatus 
} from '@/lib/types';
import { periodService } from './period-service';

export class ConflictDetectionService {
  /**
   * Detect conflicts for multiple periods against shared resources
   */
  async detectConflicts(
    periods: PeriodFormData[],
    resourceIds: number[],
    excludeBookingId?: number
  ): Promise<ConflictCheckResult> {
    if (periods.length === 0) {
      return {
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No periods to check'
      };
    }

    if (resourceIds.length === 0) {
      return {
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No resources to check'
      };
    }

    const conflicts: PeriodConflict[] = [];
    const affectedResourcesMap = new Map<number, string>();

    // Check each period for conflicts
    for (let i = 0; i < periods.length; i++) {
      const period = periods[i];
      
      // Find conflicting periods for this time range and resources
      const conflictingPeriods = await this.findConflictingPeriods(
        resourceIds,
        period.start,
        period.end,
        excludeBookingId
      );

      // Process each conflicting period
      for (const conflictingPeriod of conflictingPeriods) {
        const overlap = this.calculateOverlap(period, conflictingPeriod);
        
        // Find which resources are in conflict (intersection of resource sets)
        const conflictingResources = conflictingPeriod.booking.resources.filter(
          resource => resourceIds.includes(resource.id)
        );

        if (conflictingResources.length > 0) {
          conflicts.push({
            periodId: 0, // New period, no ID yet
            conflictingPeriodId: conflictingPeriod.id,
            conflictingResources: conflictingResources.map(r => ({
              id: r.id,
              name: r.name
            })),
            overlapStart: overlap.start,
            overlapEnd: overlap.end,
            conflictingBooking: {
              id: conflictingPeriod.booking.id,
              customerName: conflictingPeriod.booking.customer.name,
              status: conflictingPeriod.booking.status as BookingStatus
            }
          });

          // Track affected resources
          conflictingResources.forEach(resource => {
            affectedResourcesMap.set(resource.id, resource.name);
          });
        }
      }
    }

    return {
      hasConflicts: conflicts.length > 0,
      conflicts,
      affectedResources: Array.from(affectedResourcesMap.entries()).map(([id, name]) => ({
        id: Number(id),
        name
      })),
      message: conflicts.length > 0 
        ? `Found ${conflicts.length} conflict(s) across ${affectedResourcesMap.size} resource(s)`
        : 'No conflicts detected'
    };
  }

  /**
   * Check conflicts for a single period
   */
  async checkSinglePeriodConflicts(
    period: PeriodFormData,
    resourceIds: number[],
    excludeBookingId?: number
  ): Promise<ConflictCheckResult> {
    return this.detectConflicts([period], resourceIds, excludeBookingId);
  }

  /**
   * Check conflicts for existing periods when updating a booking
   */
  async checkExistingPeriodConflicts(
    periodId: number,
    updatedPeriod: PeriodFormData,
    resourceIds: number[]
  ): Promise<ConflictCheckResult> {
    // Get the existing period to find its booking
    const existingPeriod = await periodService.getPeriodById(periodId);
    if (!existingPeriod) {
      throw new Error('Period not found');
    }

    return this.detectConflicts(
      [updatedPeriod], 
      resourceIds, 
      existingPeriod.bookingId
    );
  }

  /**
   * Find periods that conflict with given time range and resources
   */
  private async findConflictingPeriods(
    resourceIds: number[],
    start: Date,
    end: Date,
    excludeBookingId?: number
  ): Promise<Period[]> {
    return periodService.findConflictingPeriods(
      resourceIds,
      start,
      end,
      excludeBookingId
    );
  }

  /**
   * Calculate the overlap between two time periods
   */
  private calculateOverlap(
    period1: { start: Date; end: Date },
    period2: { start: Date; end: Date }
  ): { start: Date; end: Date } {
    const overlapStart = new Date(Math.max(period1.start.getTime(), period2.start.getTime()));
    const overlapEnd = new Date(Math.min(period1.end.getTime(), period2.end.getTime()));

    return {
      start: overlapStart,
      end: overlapEnd
    };
  }

  /**
   * Check if two periods overlap
   */
  private periodsOverlap(
    period1: { start: Date; end: Date },
    period2: { start: Date; end: Date }
  ): boolean {
    return period1.start < period2.end && period2.start < period1.end;
  }

  /**
   * Assess the severity of a conflict based on multiple factors
   */
  private assessConflictSeverity(
    overlapDuration: number,
    conflictingBookingStatus: BookingStatus,
    conflictingResourceCount: number
  ): 'HIGH' | 'MEDIUM' | 'LOW' {
    const overlapHours = overlapDuration / (60 * 60 * 1000);
    
    // High severity: Confirmed bookings with significant overlap
    if (conflictingBookingStatus === 'CONFIRMED' && overlapHours >= 1) {
      return 'HIGH';
    }
    
    // High severity: Multiple resources affected regardless of status
    if (conflictingResourceCount >= 3) {
      return 'HIGH';
    }
    
    // Medium severity: Confirmed bookings with short overlap or pending with long overlap
    if (
      (conflictingBookingStatus === 'CONFIRMED' && overlapHours < 1) ||
      (conflictingBookingStatus === 'PENDING' && overlapHours >= 2)
    ) {
      return 'MEDIUM';
    }
    
    // Low severity: Pending bookings with short overlap
    return 'LOW';
  }

  /**
   * Generate resolution suggestions based on conflict characteristics
   */
  private generateResolutionSuggestion(
    severity: 'HIGH' | 'MEDIUM' | 'LOW',
    overlapDuration: number,
    conflictingBookingStatus: BookingStatus,
    conflictingResources: { id: number; name: string }[]
  ): string {
    const overlapMinutes = Math.round(overlapDuration / (60 * 1000));
    const resourceNames = conflictingResources.map(r => r.name).join(', ');
    
    switch (severity) {
      case 'HIGH':
        if (conflictingBookingStatus === 'CONFIRMED') {
          return `CRITICAL: Confirmed booking conflict (${overlapMinutes} min overlap on ${resourceNames}). Consider alternative time slots or different resources.`;
        }
        return `HIGH: Multiple resource conflict (${resourceNames}). Contact conflicting booking customer or select different resources.`;
        
      case 'MEDIUM':
        if (conflictingBookingStatus === 'CONFIRMED') {
          return `MODERATE: Brief overlap with confirmed booking (${overlapMinutes} min on ${resourceNames}). Adjust timing by ${overlapMinutes + 15} minutes.`;
        }
        return `MODERATE: Long overlap with pending booking (${overlapMinutes} min on ${resourceNames}). Contact customer or adjust schedule.`;
        
      case 'LOW':
        return `MINOR: Brief overlap with pending booking (${overlapMinutes} min on ${resourceNames}). Small timing adjustment recommended.`;
        
      default:
        return 'Contact administration for conflict resolution assistance.';
    }
  }

  /**
   * Generate comprehensive conflict message
   */
  private generateConflictMessage(
    totalConflicts: number,
    affectedResourceCount: number,
    highSeverity: number,
    mediumSeverity: number,
    lowSeverity: number
  ): string {
    if (totalConflicts === 0) {
      return 'No conflicts detected - booking can proceed';
    }
    
    const severityParts = [];
    if (highSeverity > 0) severityParts.push(`${highSeverity} critical`);
    if (mediumSeverity > 0) severityParts.push(`${mediumSeverity} moderate`);
    if (lowSeverity > 0) severityParts.push(`${lowSeverity} minor`);
    
    const severityText = severityParts.length > 0 ? ` (${severityParts.join(', ')})` : '';
    
    return `Found ${totalConflicts} conflict(s) across ${affectedResourceCount} resource(s)${severityText}`;
  }

  /**
   * Get detailed conflict information for a specific booking
   */
  async getBookingConflictDetails(
    bookingId: number,
    resourceIds?: number[]
  ): Promise<ConflictCheckResult> {
    // Get all periods for the booking
    const periods = await periodService.getPeriodsByBookingId(bookingId);
    
    if (periods.length === 0) {
      return {
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No periods found for booking'
      };
    }

    // Get booking details to determine resources to check
    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: { resources: true }
    });

    if (!booking) {
      throw new Error('Booking not found');
    }

    const resourcesToCheck = resourceIds || booking.resources.map(r => r.id);
    
    // Convert periods to PeriodFormData format for conflict checking
    const periodFormData: PeriodFormData[] = periods.map(period => ({
      start: period.start,
      end: period.end,
      isRecurring: period.isRecurring,
      recurrenceRule: period.recurrenceRule || undefined
    }));

    return this.detectConflicts(periodFormData, resourcesToCheck, bookingId);
  }

  /**
   * Get resource utilization data for conflict analysis
   */
  async getResourceUtilization(
    resourceIds: number[],
    startDate: Date,
    endDate: Date
  ): Promise<{
    resourceId: number;
    resourceName: string;
    totalBookedTime: number;
    totalAvailableTime: number;
    utilizationPercentage: number;
    conflictingBookings: {
      bookingId: number;
      customerName: string;
      periods: { start: Date; end: Date }[];
    }[];
  }[]> {
    const utilization = [];

    for (const resourceId of resourceIds) {
      // Get resource details
      const resource = await prisma.resource.findUnique({
        where: { id: resourceId }
      });

      if (!resource) {
        continue;
      }

      // Get all periods for this resource in the date range
      const periods = await periodService.getPeriodsInRange(
        startDate,
        endDate,
        [resourceId]
      );

      // Calculate total booked time
      let totalBookedTime = 0;
      const conflictingBookingsMap = new Map();

      for (const period of periods) {
        // Calculate overlap with the requested date range
        const overlapStart = new Date(Math.max(period.start.getTime(), startDate.getTime()));
        const overlapEnd = new Date(Math.min(period.end.getTime(), endDate.getTime()));
        
        if (overlapStart < overlapEnd) {
          const bookedTime = overlapEnd.getTime() - overlapStart.getTime();
          totalBookedTime += bookedTime;

          // Track booking details
          const bookingKey = period.booking.id;
          if (!conflictingBookingsMap.has(bookingKey)) {
            conflictingBookingsMap.set(bookingKey, {
              bookingId: period.booking.id,
              customerName: period.booking.customer.name,
              periods: []
            });
          }
          
          conflictingBookingsMap.get(bookingKey).periods.push({
            start: overlapStart,
            end: overlapEnd
          });
        }
      }

      // Calculate total available time
      const totalAvailableTime = endDate.getTime() - startDate.getTime();
      const utilizationPercentage = totalAvailableTime > 0 
        ? (totalBookedTime / totalAvailableTime) * 100 
        : 0;

      utilization.push({
        resourceId,
        resourceName: resource.name,
        totalBookedTime,
        totalAvailableTime,
        utilizationPercentage,
        conflictingBookings: Array.from(conflictingBookingsMap.values())
      });
    }

    return utilization;
  }

  /**
   * Validate that periods within a booking don't overlap with each other
   */
  validateInternalPeriodOverlaps(periods: PeriodFormData[]): {
    hasOverlaps: boolean;
    overlaps: {
      period1Index: number;
      period2Index: number;
      period1: PeriodFormData;
      period2: PeriodFormData;
      overlapStart: Date;
      overlapEnd: Date;
    }[];
  } {
    const overlaps = [];

    for (let i = 0; i < periods.length; i++) {
      for (let j = i + 1; j < periods.length; j++) {
        const period1 = periods[i];
        const period2 = periods[j];

        if (this.periodsOverlap(period1, period2)) {
          const overlap = this.calculateOverlap(period1, period2);
          overlaps.push({
            period1Index: i,
            period2Index: j,
            period1,
            period2,
            overlapStart: overlap.start,
            overlapEnd: overlap.end
          });
        }
      }
    }

    return {
      hasOverlaps: overlaps.length > 0,
      overlaps
    };
  }

  /**
   * Get conflict summary for multiple bookings
   */
  async getMultiBookingConflictSummary(
    bookingIds: number[]
  ): Promise<{
    totalConflicts: number;
    conflictsByBooking: {
      bookingId: number;
      customerName: string;
      conflictCount: number;
      affectedResources: string[];
    }[];
    mostConflictedResources: {
      resourceId: number;
      resourceName: string;
      conflictCount: number;
    }[];
  }> {
    const conflictsByBooking = [];
    const resourceConflictCounts = new Map<number, { name: string; count: number }>();
    let totalConflicts = 0;

    for (const bookingId of bookingIds) {
      const conflictResult = await this.getBookingConflictDetails(bookingId);
      
      // Get booking details
      const booking = await prisma.booking.findUnique({
        where: { id: bookingId },
        include: { customer: true }
      });

      if (booking) {
        conflictsByBooking.push({
          bookingId,
          customerName: booking.customer.name,
          conflictCount: conflictResult.conflicts.length,
          affectedResources: conflictResult.affectedResources.map(r => r.name)
        });

        totalConflicts += conflictResult.conflicts.length;

        // Count resource conflicts
        for (const resource of conflictResult.affectedResources) {
          const current = resourceConflictCounts.get(resource.id) || { name: resource.name, count: 0 };
          resourceConflictCounts.set(resource.id, { 
            name: current.name, 
            count: current.count + 1 
          });
        }
      }
    }

    // Sort resources by conflict count
    const mostConflictedResources = Array.from(resourceConflictCounts.entries())
      .map(([resourceId, data]) => ({
        resourceId: Number(resourceId),
        resourceName: data.name,
        conflictCount: data.count
      }))
      .sort((a, b) => b.conflictCount - a.conflictCount);

    return {
      totalConflicts,
      conflictsByBooking,
      mostConflictedResources
    };
  }
}

// Export a singleton instance
export const conflictDetectionService = new ConflictDetectionService();