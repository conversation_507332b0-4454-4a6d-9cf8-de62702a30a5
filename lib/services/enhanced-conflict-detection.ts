import { 
  Period, 
  PeriodFormData, 
  PeriodConflict, 
  BookingStatus 
} from '@/lib/types';
import { periodService } from './period-service';
import { prisma } from '@/lib/db';

// Enhanced conflict check result interface
export interface EnhancedConflictCheckResult {
  hasConflicts: boolean;
  conflicts: {
    periodId: number;
    conflictingPeriodId: number;
    conflictingResources: { id: number; name: string }[];
    overlapStart: Date;
    overlapEnd: Date;
    conflictingBooking: {
      id: number;
      customerName: string;
      status: BookingStatus;
    };
    severity: 'HIGH' | 'MEDIUM' | 'LOW';
    suggestedResolution: string;
    overlapDurationMinutes: number;
  }[];
  affectedResources: { id: number; name: string }[];
  message: string;
  summary: {
    totalOverlapDuration: number; // in milliseconds
    highSeverityConflicts: number;
    mediumSeverityConflicts: number;
    lowSeverityConflicts: number;
    averageOverlapMinutes: number;
  };
  resolutionSuggestions: {
    alternativeTimeSlots: { start: Date; end: Date; reason: string }[];
    alternativeResources: { resourceId: number; resourceName: string; available: boolean }[];
    mitigationStrategies: string[];
  };
}

export class EnhancedConflictDetectionService {
  /**
   * Detect conflicts with enhanced error handling and resolution suggestions
   */
  async detectConflicts(
    periods: PeriodFormData[],
    resourceIds: number[],
    excludeBookingId?: number
  ): Promise<EnhancedConflictCheckResult> {
    try {
      // Input validation
      if (periods.length === 0) {
        return this.createEmptyResult('No periods to check');
      }

      if (resourceIds.length === 0) {
        return this.createEmptyResult('No resources to check');
      }

      // Validate periods integrity
      const periodValidation = this.validatePeriodIntegrity(periods);
      if (!periodValidation.isValid) {
        throw new Error(`Period validation failed: ${periodValidation.errors.join(', ')}`);
      }

      const conflicts = [];
      const affectedResourcesMap = new Map<string, string>();
      let totalOverlapDuration = 0;
      let highSeverityConflicts = 0;
      let mediumSeverityConflicts = 0;
      let lowSeverityConflicts = 0;

      // Check each period for conflicts
      for (let i = 0; i < periods.length; i++) {
        const period = periods[i];
        
        // Find conflicting periods for this time range and resources
        const conflictingPeriods = await this.findConflictingPeriods(
          resourceIds,
          period.start,
          period.end,
          excludeBookingId
        );

        // Process each conflicting period
        for (const conflictingPeriod of conflictingPeriods) {
          const overlap = this.calculateOverlap(period, conflictingPeriod);
          const overlapDuration = overlap.end.getTime() - overlap.start.getTime();
          const overlapMinutes = Math.round(overlapDuration / (60 * 1000));
          totalOverlapDuration += overlapDuration;
          
          // Find which resources are in conflict (intersection of resource sets)
          const conflictingResources = conflictingPeriod.booking.resources.filter(
            resource => resourceIds.includes(resource.id)
          );

          if (conflictingResources.length > 0) {
            // Determine conflict severity
            const severity = this.assessConflictSeverity(
              overlapDuration,
              conflictingPeriod.booking.status as BookingStatus,
              conflictingResources.length
            );
            
            // Generate resolution suggestion
            const suggestedResolution = this.generateResolutionSuggestion(
              severity,
              overlapDuration,
              conflictingPeriod.booking.status as BookingStatus,
              conflictingResources
            );
            
            // Count severity types
            switch (severity) {
              case 'HIGH': highSeverityConflicts++; break;
              case 'MEDIUM': mediumSeverityConflicts++; break;
              case 'LOW': lowSeverityConflicts++; break;
            }

            conflicts.push({
              periodId: 0, // New period, no ID yet
              conflictingPeriodId: conflictingPeriod.id,
              conflictingResources: conflictingResources.map(r => ({
                id: r.id,
                name: r.name
              })),
              overlapStart: overlap.start,
              overlapEnd: overlap.end,
              conflictingBooking: {
                id: conflictingPeriod.booking.id,
                customerName: conflictingPeriod.booking.customer.name,
                status: conflictingPeriod.booking.status as BookingStatus
              },
              severity,
              suggestedResolution,
              overlapDurationMinutes: overlapMinutes
            });

            // Track affected resources
            conflictingResources.forEach(resource => {
              affectedResourcesMap.set(resource.id.toString(), resource.name);
            });
          }
        }
      }

      const averageOverlapMinutes = conflicts.length > 0 
        ? Math.round(totalOverlapDuration / (conflicts.length * 60 * 1000))
        : 0;

      const message = this.generateConflictMessage(
        conflicts.length,
        affectedResourcesMap.size,
        highSeverityConflicts,
        mediumSeverityConflicts,
        lowSeverityConflicts
      );

      // Generate resolution suggestions if conflicts exist
      const resolutionSuggestions = conflicts.length > 0 
        ? await this.generateResolutionSuggestions(periods, resourceIds, conflicts)
        : { alternativeTimeSlots: [], alternativeResources: [], mitigationStrategies: [] };

      return {
        hasConflicts: conflicts.length > 0,
        conflicts,
        affectedResources: Array.from(affectedResourcesMap.entries()).map(([id, name]) => ({
          id: Number(id),
          name
        })),
        message,
        summary: {
          totalOverlapDuration,
          highSeverityConflicts,
          mediumSeverityConflicts,
          lowSeverityConflicts,
          averageOverlapMinutes
        },
        resolutionSuggestions
      };
    } catch (error) {
      console.error('Error in enhanced conflict detection:', error);
      throw new Error(`Conflict detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate period integrity before conflict checking
   */
  private validatePeriodIntegrity(periods: PeriodFormData[]): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check individual periods
    periods.forEach((period, index) => {
      if (period.end <= period.start) {
        errors.push(`Period ${index + 1}: End time must be after start time`);
      }

      const duration = period.end.getTime() - period.start.getTime();
      if (duration < 15 * 60 * 1000) {
        errors.push(`Period ${index + 1}: Duration must be at least 15 minutes`);
      }

      if (duration > 24 * 60 * 60 * 1000) {
        warnings.push(`Period ${index + 1}: Duration exceeds 24 hours`);
      }
    });

    // Check for overlaps within the same booking
    for (let i = 0; i < periods.length; i++) {
      for (let j = i + 1; j < periods.length; j++) {
        const period1 = periods[i];
        const period2 = periods[j];
        
        if (period1.start < period2.end && period2.start < period1.end) {
          errors.push(`Period ${i + 1} overlaps with period ${j + 1}`);
        }
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Assess the severity of a conflict based on multiple factors
   */
  private assessConflictSeverity(
    overlapDuration: number,
    conflictingBookingStatus: BookingStatus,
    conflictingResourceCount: number
  ): 'HIGH' | 'MEDIUM' | 'LOW' {
    const overlapHours = overlapDuration / (60 * 60 * 1000);
    
    // High severity: Confirmed bookings with significant overlap
    if (conflictingBookingStatus === 'CONFIRMED' && overlapHours >= 1) {
      return 'HIGH';
    }
    
    // High severity: Multiple resources affected regardless of status
    if (conflictingResourceCount >= 3) {
      return 'HIGH';
    }
    
    // Medium severity: Confirmed bookings with short overlap or pending with long overlap
    if (
      (conflictingBookingStatus === 'CONFIRMED' && overlapHours < 1) ||
      (conflictingBookingStatus === 'PENDING' && overlapHours >= 2)
    ) {
      return 'MEDIUM';
    }
    
    // Low severity: Pending bookings with short overlap
    return 'LOW';
  }

  /**
   * Generate resolution suggestions based on conflict characteristics
   */
  private generateResolutionSuggestion(
    severity: 'HIGH' | 'MEDIUM' | 'LOW',
    overlapDuration: number,
    conflictingBookingStatus: BookingStatus,
    conflictingResources: { id: number; name: string }[]
  ): string {
    const overlapMinutes = Math.round(overlapDuration / (60 * 1000));
    const resourceNames = conflictingResources.map(r => r.name).join(', ');
    
    switch (severity) {
      case 'HIGH':
        if (conflictingBookingStatus === 'CONFIRMED') {
          return `CRITICAL: Confirmed booking conflict (${overlapMinutes} min overlap on ${resourceNames}). Consider alternative time slots or different resources.`;
        }
        return `HIGH: Multiple resource conflict (${resourceNames}). Contact conflicting booking customer or select different resources.`;
        
      case 'MEDIUM':
        if (conflictingBookingStatus === 'CONFIRMED') {
          return `MODERATE: Brief overlap with confirmed booking (${overlapMinutes} min on ${resourceNames}). Adjust timing by ${overlapMinutes + 15} minutes.`;
        }
        return `MODERATE: Long overlap with pending booking (${overlapMinutes} min on ${resourceNames}). Contact customer or adjust schedule.`;
        
      case 'LOW':
        return `MINOR: Brief overlap with pending booking (${overlapMinutes} min on ${resourceNames}). Small timing adjustment recommended.`;
        
      default:
        return 'Contact administration for conflict resolution assistance.';
    }
  }

  /**
   * Generate comprehensive conflict message
   */
  private generateConflictMessage(
    totalConflicts: number,
    affectedResourceCount: number,
    highSeverity: number,
    mediumSeverity: number,
    lowSeverity: number
  ): string {
    if (totalConflicts === 0) {
      return 'No conflicts detected - booking can proceed';
    }
    
    const severityParts = [];
    if (highSeverity > 0) severityParts.push(`${highSeverity} critical`);
    if (mediumSeverity > 0) severityParts.push(`${mediumSeverity} moderate`);
    if (lowSeverity > 0) severityParts.push(`${lowSeverity} minor`);
    
    const severityText = severityParts.length > 0 ? ` (${severityParts.join(', ')})` : '';
    
    return `Found ${totalConflicts} conflict(s) across ${affectedResourceCount} resource(s)${severityText}`;
  }

  /**
   * Generate resolution suggestions
   */
  private async generateResolutionSuggestions(
    periods: PeriodFormData[],
    resourceIds: number[],
    conflicts: any[]
  ): Promise<{
    alternativeTimeSlots: { start: Date; end: Date; reason: string }[];
    alternativeResources: { resourceId: number; resourceName: string; available: boolean }[];
    mitigationStrategies: string[];
  }> {
    const alternativeTimeSlots = [];
    const alternativeResources: { resourceId: number; resourceName: string; available: boolean }[] = [];
    const mitigationStrategies = [];

    // Generate alternative time slots
    for (const period of periods) {
      const timeShifts = [30, 60, 120, 180, -30, -60, -120]; // minutes
      
      for (const shift of timeShifts) {
        const shiftMs = shift * 60 * 1000;
        const newStart = new Date(period.start.getTime() + shiftMs);
        const newEnd = new Date(period.end.getTime() + shiftMs);
        
        // Skip past dates
        if (newStart < new Date()) continue;
        
        try {
          // Check if this alternative slot has conflicts
          const altConflicts = await this.detectConflicts([{ ...period, start: newStart, end: newEnd }], resourceIds);
          
          if (!altConflicts.hasConflicts) {
            const direction = shift > 0 ? 'later' : 'earlier';
            const hours = Math.abs(shift) >= 60 ? `${Math.abs(shift) / 60}h` : `${Math.abs(shift)}min`;
            alternativeTimeSlots.push({
              start: newStart,
              end: newEnd,
              reason: `${hours} ${direction} - no conflicts detected`
            });
          }
        } catch (error) {
          // Skip this alternative if checking fails
          continue;
        }
      }
    }

    // Generate mitigation strategies based on conflict severity
    const hasCriticalConflicts = conflicts.some(c => c.severity === 'HIGH');
    const hasConfirmedConflicts = conflicts.some(c => c.conflictingBooking.status === 'CONFIRMED');

    if (hasCriticalConflicts) {
      mitigationStrategies.push('Consider splitting booking into multiple smaller time slots');
      mitigationStrategies.push('Contact conflicting booking parties to negotiate resolution');
    }

    if (hasConfirmedConflicts) {
      mitigationStrategies.push('Prioritize alternative time slots over resource changes');
      mitigationStrategies.push('Escalate to management for confirmed booking conflicts');
    } else {
      mitigationStrategies.push('Contact pending booking customers to coordinate schedules');
    }

    if (conflicts.length > 3) {
      mitigationStrategies.push('Consider booking during off-peak hours or different days');
    }

    return {
      alternativeTimeSlots: alternativeTimeSlots.slice(0, 3), // Limit to 3 suggestions
      alternativeResources,
      mitigationStrategies
    };
  }

  /**
   * Create empty result for no-conflict scenarios
   */
  private createEmptyResult(message: string): EnhancedConflictCheckResult {
    return {
      hasConflicts: false,
      conflicts: [],
      affectedResources: [],
      message,
      summary: {
        totalOverlapDuration: 0,
        highSeverityConflicts: 0,
        mediumSeverityConflicts: 0,
        lowSeverityConflicts: 0,
        averageOverlapMinutes: 0
      },
      resolutionSuggestions: {
        alternativeTimeSlots: [],
        alternativeResources: [],
        mitigationStrategies: []
      }
    };
  }

  /**
   * Find periods that conflict with given time range and resources
   */
  private async findConflictingPeriods(
    resourceIds: number[],
    start: Date,
    end: Date,
    excludeBookingId?: number
  ): Promise<Period[]> {
    return periodService.findConflictingPeriods(
      resourceIds,
      start,
      end,
      excludeBookingId
    );
  }

  /**
   * Calculate the overlap between two time periods
   */
  private calculateOverlap(
    period1: { start: Date; end: Date },
    period2: { start: Date; end: Date }
  ): { start: Date; end: Date } {
    const overlapStart = new Date(Math.max(period1.start.getTime(), period2.start.getTime()));
    const overlapEnd = new Date(Math.min(period1.end.getTime(), period2.end.getTime()));
    
    return {
      start: overlapStart,
      end: overlapEnd
    };
  }
}

// Export a singleton instance
export const enhancedConflictDetectionService = new EnhancedConflictDetectionService();