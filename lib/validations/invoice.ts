import { z } from "zod"

// Invoice status enum to match Prisma schema
export const InvoiceStatus = z.enum(['PENDING', 'PARTIALLY_PAID', 'PAID', 'CANCELLED'])

// Payment method enum to match Prisma schema
export const PaymentMethod = z.enum([
  'CASH',
  'CREDIT_CARD', 
  'DEBIT_CARD',
  'BANK_TRANSFER',
  'CHECK',
  'ONLINE',
  'OTHER'
])

// Payment status enum to match Prisma schema
export const PaymentStatus = z.enum(['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED'])

// Booking ID validation
const bookingIdSchema = z.number()
  .int("Booking ID must be a whole number")
  .positive("Booking ID must be a positive number")
  .refine(val => val > 0, "Please select a valid booking")

// Amount validation for monetary values (IQD)
const amountSchema = z.number()
  .min(0.01, "Amount must be at least 0.01 IQD")
  .max(9999999.99, "Amount cannot exceed 9,999,999.99 IQD")
  .refine(val => Number.isFinite(val), "Amount must be a valid number")
  .refine(val => !Number.isNaN(val), "Amount cannot be NaN")
  .transform(val => Math.round(val * 100) / 100) // Round to 2 decimal places

// Quantity validation
const quantitySchema = z.number()
  .int("Quantity must be a whole number")
  .min(1, "Quantity must be at least 1")
  .max(1000, "Quantity cannot exceed 1000")

// Description validation
const descriptionSchema = z.string()
  .min(1, "Description is required")
  .max(255, "Description must be less than 255 characters")
  .transform(val => val.trim())
  .refine(val => val.length > 0, "Description cannot be empty or only spaces")

// Line item validation
const lineItemSchema = z.object({
  description: descriptionSchema,
  amount: amountSchema,
  quantity: quantitySchema,
  isCatering: z.boolean().default(false),
  cateringId: z.number().int().positive().optional()
}).refine(data => {
  // If isCatering is true, cateringId must be provided
  if (data.isCatering && !data.cateringId) {
    return false
  }
  // If isCatering is false, cateringId should not be provided
  if (!data.isCatering && data.cateringId) {
    return false
  }
  return true
}, {
  message: "Catering ID is required when line item is marked as catering",
  path: ["cateringId"]
})

// Line items array validation
const lineItemsSchema = z.array(lineItemSchema)
  .min(1, "At least one line item is required")
  .max(50, "Cannot have more than 50 line items")
  .refine(items => {
    // Validate that total amount is reasonable
    const total = items.reduce((sum, item) => sum + (item.amount * item.quantity), 0)
    return total <= 9999999.99
  }, "Total invoice amount cannot exceed 9,999,999.99 IQD")

// Schema for creating a new invoice
export const invoiceCreateSchema = z.object({
  bookingId: bookingIdSchema,
  lineItems: lineItemsSchema
})

// Schema for updating an invoice
export const invoiceUpdateSchema = z.object({
  bookingId: bookingIdSchema,
  lineItems: lineItemsSchema,
  status: InvoiceStatus.optional()
})

// Schema for invoice search and filtering
export const invoiceSearchSchema = z.object({
  search: z.string().optional(),
  status: InvoiceStatus.optional(),
  customerId: z.coerce.number().int().positive().optional(),
  bookingId: z.coerce.number().int().positive().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
})

// Schema for invoice ID parameter validation
export const invoiceIdSchema = z.object({
  id: z.coerce.number().int().positive("Invalid invoice ID")
})

// Payment validation schemas
const paymentAmountSchema = z.number()
  .min(0.01, "Payment amount must be at least 0.01 IQD")
  .max(9999999.99, "Payment amount cannot exceed 9,999,999.99 IQD")
  .refine(val => Number.isFinite(val), "Payment amount must be a valid number")
  .refine(val => !Number.isNaN(val), "Payment amount cannot be NaN")
  .transform(val => Math.round(val * 100) / 100) // Round to 2 decimal places

const paymentReferenceSchema = z.string()
  .max(100, "Payment reference must be less than 100 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

const paymentNotesSchema = z.string()
  .max(500, "Payment notes must be less than 500 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Schema for creating a new payment
export const paymentCreateSchema = z.object({
  amount: paymentAmountSchema,
  method: PaymentMethod.refine(val => val !== undefined, {
    message: "Please select a payment method"
  }),
  reference: paymentReferenceSchema,
  notes: paymentNotesSchema,
  paidAt: z.union([z.date(), z.string().datetime()]).optional().transform((val) => {
    if (typeof val === 'string') {
      return new Date(val);
    }
    return val;
  })
})

// Schema for updating a payment
export const paymentUpdateSchema = z.object({
  amount: paymentAmountSchema,
  method: PaymentMethod,
  status: PaymentStatus,
  reference: paymentReferenceSchema,
  notes: paymentNotesSchema,
  paidAt: z.union([z.date(), z.string().datetime()]).optional().transform((val) => {
    if (typeof val === 'string') {
      return new Date(val);
    }
    return val;
  })
})

// Schema for payment search and filtering
export const paymentSearchSchema = z.object({
  invoiceId: z.coerce.number().int().positive().optional(),
  method: PaymentMethod.optional(),
  status: PaymentStatus.optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
})

// Schema for payment ID parameter validation
export const paymentIdSchema = z.object({
  id: z.coerce.number().int().positive("Invalid payment ID")
})

// Type exports for use in components
export type InvoiceCreateInput = z.infer<typeof invoiceCreateSchema>
export type InvoiceUpdateInput = z.infer<typeof invoiceUpdateSchema>
export type InvoiceSearchInput = z.infer<typeof invoiceSearchSchema>
export type InvoiceIdInput = z.infer<typeof invoiceIdSchema>
export type InvoiceStatusType = z.infer<typeof InvoiceStatus>

export type PaymentCreateInput = z.infer<typeof paymentCreateSchema>
export type PaymentUpdateInput = z.infer<typeof paymentUpdateSchema>
export type PaymentSearchInput = z.infer<typeof paymentSearchSchema>
export type PaymentIdInput = z.infer<typeof paymentIdSchema>
export type PaymentMethodType = z.infer<typeof PaymentMethod>
export type PaymentStatusType = z.infer<typeof PaymentStatus>

export type LineItemInput = z.infer<typeof lineItemSchema>

// Validation helper functions
export const validateInvoiceStatus = (status: string): status is InvoiceStatusType => {
  return InvoiceStatus.safeParse(status).success
}

export const validatePaymentMethod = (method: string): method is PaymentMethodType => {
  return PaymentMethod.safeParse(method).success
}

export const validatePaymentStatus = (status: string): status is PaymentStatusType => {
  return PaymentStatus.safeParse(status).success
}

export const getInvoiceFormErrors = (error: z.ZodError) => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    if (!errors[path]) {
      errors[path] = err.message
    }
  })
  
  return errors
}

export const getPaymentFormErrors = (error: z.ZodError) => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    if (!errors[path]) {
      errors[path] = err.message
    }
  })
  
  return errors
}

// Invoice calculation helpers
// IMPORTANT: Line item total is ALWAYS calculated as: quantity × unit price (amount)
// This formula is consistently applied throughout the application:
// - For regular items: quantity = number of units, amount = price per unit
// - For catering items: quantity = number of people, amount = price per person
// - Line total = quantity × amount (for both types: quantity × unit price)
// - Invoice total = sum of all line totals
export const calculateInvoiceTotal = (lineItems: LineItemInput[]): number => {
  return lineItems.reduce((total, item) => {
    const amount = Number(item.amount) || 0;
    const quantity = Number(item.quantity) || 1;
    const lineTotal = amount * quantity; // quantity × unit price
    
    // Ensure we don't add NaN values
    if (isNaN(lineTotal) || !isFinite(lineTotal)) {
      return total;
    }
    
    return total + lineTotal;
  }, 0);
}

export const calculateInvoiceStatus = (total: number, paid: number): InvoiceStatusType => {
  if (paid === 0) return 'PENDING'
  if (paid >= total) return 'PAID'
  return 'PARTIALLY_PAID'
}

export const calculateRemainingBalance = (total: number, paid: number): number => {
  return Math.max(0, total - paid)
}

// Payment validation helpers
export const validatePaymentAmount = (
  paymentAmount: number, 
  invoiceTotal: number, 
  alreadyPaid: number
): boolean => {
  const remainingBalance = invoiceTotal - alreadyPaid
  return paymentAmount > 0 && paymentAmount <= remainingBalance
}

export const validatePaymentAgainstInvoice = (
  payment: PaymentCreateInput,
  invoice: { total: number; paid: number }
): { isValid: boolean; error?: string } => {
  const remainingBalance = Number(invoice.total) - Number(invoice.paid)
  
  if (payment.amount > remainingBalance) {
    return {
      isValid: false,
      error: `Payment amount (${payment.amount} IQD) exceeds remaining balance (${remainingBalance} IQD)`
    }
  }
  
  if (payment.amount <= 0) {
    return {
      isValid: false,
      error: "Payment amount must be greater than 0"
    }
  }
  
  return { isValid: true }
}

// Line item validation helpers
export const validateLineItemCatering = (
  lineItem: LineItemInput,
  availableCaterings: { id: number; offerName: string; pricePerPerson: number }[]
): { isValid: boolean; error?: string } => {
  if (!lineItem.isCatering) {
    return { isValid: true }
  }
  
  if (!lineItem.cateringId) {
    return {
      isValid: false,
      error: "Catering ID is required for catering line items"
    }
  }
  
  const catering = availableCaterings.find(c => c.id === lineItem.cateringId)
  if (!catering) {
    return {
      isValid: false,
      error: "Selected catering offer not found"
    }
  }
  
  // For catering items, we now use the same logic as normal items:
  // - quantity = number of people
  // - amount = price per person (unit price)
  // - line total = quantity × amount (same as normal items)
  
  // Validate that the amount matches the price per person
  const expectedAmount = catering.pricePerPerson;
  const actualAmount = Number(lineItem.amount);
  
  if (Math.abs(actualAmount - expectedAmount) > 0.01) {
    return {
      isValid: false,
      error: `Catering unit price (${actualAmount} IQD) must match the price per person (${expectedAmount} IQD)`
    }
  }
  
  // Validate quantity is at least 1 (number of people)
  const quantity = Number(lineItem.quantity);
  if (quantity < 1) {
    return {
      isValid: false,
      error: "Catering quantity (number of people) must be at least 1"
    }
  }
  
  return { isValid: true }
}

// Validation helper to ensure line totals follow quantity × unit price formula
export const validateLineItemTotalFormula = (
  lineItem: LineItemInput
): { isValid: boolean; error?: string; expectedTotal: number } => {
  const amount = Number(lineItem.amount) || 0;
  const quantity = Number(lineItem.quantity) || 1;
  const expectedTotal = amount * quantity;
  
  if (isNaN(expectedTotal) || !isFinite(expectedTotal)) {
    return {
      isValid: false,
      error: "Invalid line item calculation: amount or quantity contains invalid values",
      expectedTotal: 0
    };
  }
  
  if (amount <= 0) {
    return {
      isValid: false,
      error: "Unit price (amount) must be greater than 0",
      expectedTotal
    };
  }
  
  if (quantity <= 0) {
    return {
      isValid: false,
      error: "Quantity must be at least 1",
      expectedTotal
    };
  }
  
  return {
    isValid: true,
    expectedTotal
  };
};