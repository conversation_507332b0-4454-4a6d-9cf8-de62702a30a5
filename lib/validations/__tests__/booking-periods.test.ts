import { describe, it, expect } from 'vitest'
import {
  bookingCreateSchema,
  bookingUpdateSchema,
  validateBookingPeriods,
  validateBookingConflict
} from '../booking'

describe('Booking Validation with Periods', () => {
  describe('bookingCreateSchema', () => {
    it('should validate booking with valid periods', () => {
      const validBooking = {
        customerId: 1,
        resourceIds: [1, 2],
        status: 'PENDING' as const,
        periods: [
          {
            start: new Date('2025-08-07T10:00:00Z'),
            end: new Date('2025-08-07T12:00:00Z'),
            isRecurring: false
          },
          {
            start: new Date('2025-08-07T14:00:00Z'),
            end: new Date('2025-08-07T16:00:00Z'),
            isRecurring: false
          }
        ],
        caterings: []
      }
      
      const result = bookingCreateSchema.safeParse(validBooking)
      expect(result.success).toBe(true)
    })

    it('should reject booking with overlapping periods', () => {
      const invalidBooking = {
        customerId: 1,
        resourceIds: [1, 2],
        status: 'PENDING' as const,
        periods: [
          {
            start: new Date('2025-08-07T10:00:00Z'),
            end: new Date('2025-08-07T12:00:00Z'),
            isRecurring: false
          },
          {
            start: new Date('2025-08-07T11:00:00Z'), // Overlaps with first period
            end: new Date('2025-08-07T13:00:00Z'),
            isRecurring: false
          }
        ],
        caterings: []
      }
      
      const result = bookingCreateSchema.safeParse(invalidBooking)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Periods within the same booking cannot overlap')
      }
    })

    it('should require at least one period', () => {
      const invalidBooking = {
        customerId: 1,
        resourceIds: [1, 2],
        status: 'PENDING' as const,
        periods: [], // Empty periods array
        caterings: []
      }
      
      const result = bookingCreateSchema.safeParse(invalidBooking)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('At least one period is required')
      }
    })

    it('should limit maximum number of periods', () => {
      const periods = Array.from({ length: 51 }, (_, i) => ({
        start: new Date(`2025-08-07T${String(i % 24).padStart(2, '0')}:00:00Z`),
        end: new Date(`2025-08-07T${String(i % 24).padStart(2, '0')}:30:00Z`),
        isRecurring: false
      }))

      const invalidBooking = {
        customerId: 1,
        resourceIds: [1, 2],
        status: 'PENDING' as const,
        periods, // Too many periods
        caterings: []
      }
      
      const result = bookingCreateSchema.safeParse(invalidBooking)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Cannot have more than 50 periods per booking')
      }
    })

    it('should validate periods are not too far in the future', () => {
      const futureDate = new Date()
      futureDate.setFullYear(futureDate.getFullYear() + 2) // 2 years in the future

      const invalidBooking = {
        customerId: 1,
        resourceIds: [1, 2],
        status: 'PENDING' as const,
        periods: [
          {
            start: futureDate,
            end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000), // 2 hours later
            isRecurring: false
          }
        ],
        caterings: []
      }
      
      const result = bookingCreateSchema.safeParse(invalidBooking)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Booking periods cannot be scheduled more than 1 year in advance')
      }
    })

    it('should allow catering only for confirmed bookings', () => {
      const invalidBooking = {
        customerId: 1,
        resourceIds: [1, 2],
        status: 'PENDING' as const,
        periods: [
          {
            start: new Date('2025-08-07T10:00:00Z'),
            end: new Date('2025-08-07T12:00:00Z'),
            isRecurring: false
          }
        ],
        caterings: [
          {
            cateringId: 1,
            quantity: 10
          }
        ]
      }
      
      const result = bookingCreateSchema.safeParse(invalidBooking)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Catering options are only available for confirmed bookings')
      }
    })

    it('should allow catering for confirmed bookings', () => {
      const validBooking = {
        customerId: 1,
        resourceIds: [1, 2],
        status: 'CONFIRMED' as const,
        periods: [
          {
            start: new Date('2025-08-07T10:00:00Z'),
            end: new Date('2025-08-07T12:00:00Z'),
            isRecurring: false
          }
        ],
        caterings: [
          {
            cateringId: 1,
            quantity: 10
          }
        ]
      }
      
      const result = bookingCreateSchema.safeParse(validBooking)
      expect(result.success).toBe(true)
    })
  })

  describe('bookingUpdateSchema', () => {
    it('should allow past dates for updates', () => {
      const pastDate = new Date('2024-01-01T10:00:00Z')
      
      const validBooking = {
        customerId: 1,
        resourceIds: [1, 2],
        status: 'CONFIRMED' as const,
        periods: [
          {
            start: pastDate,
            end: new Date(pastDate.getTime() + 2 * 60 * 60 * 1000), // 2 hours later
            isRecurring: false
          }
        ],
        caterings: []
      }
      
      const result = bookingUpdateSchema.safeParse(validBooking)
      expect(result.success).toBe(true)
    })
  })

  describe('Helper functions', () => {
    it('should validate booking periods correctly', () => {
      const validPeriods = [
        {
          start: new Date('2025-08-07T10:00:00Z'),
          end: new Date('2025-08-07T12:00:00Z')
        },
        {
          start: new Date('2025-08-07T14:00:00Z'),
          end: new Date('2025-08-07T16:00:00Z')
        }
      ]
      
      const invalidPeriods = [
        {
          start: new Date('2025-08-07T10:00:00Z'),
          end: new Date('2025-08-07T12:00:00Z')
        },
        {
          start: new Date('2025-08-07T11:00:00Z'), // Overlaps
          end: new Date('2025-08-07T13:00:00Z')
        }
      ]
      
      expect(validateBookingPeriods(validPeriods)).toBe(true)
      expect(validateBookingPeriods(invalidPeriods)).toBe(false)
    })

    it('should validate booking conflicts', () => {
      const periods = [
        {
          start: new Date('2025-08-07T10:00:00Z'),
          end: new Date('2025-08-07T12:00:00Z')
        }
      ]
      
      const resourceIds = [1, 2]
      
      // This is a basic test - in real implementation, this would check against database
      expect(validateBookingConflict(periods, resourceIds)).toBe(true)
    })
  })
})