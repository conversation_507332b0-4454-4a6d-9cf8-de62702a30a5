import { describe, it, expect } from 'vitest'
import {
  periodFormDataSchema,
  periodCreateSchema,
  periodUpdateSchema,
  recurrenceRuleSchema,
  bulkPeriodCreateSchema,
  periodConflictCheckSchema,
  validatePeriodDates,
  validatePeriodOverlap,
  validatePeriodsArray,
  validateRecurrenceRule,
  getPeriodFormErrors
} from '../period'

describe('Period Validation', () => {
  describe('periodFormDataSchema', () => {
    it('should validate valid period data', () => {
      const validPeriod = {
        start: new Date('2025-08-07T10:00:00Z'),
        end: new Date('2025-08-07T12:00:00Z'),
        isRecurring: false
      }
      
      const result = periodFormDataSchema.safeParse(validPeriod)
      expect(result.success).toBe(true)
    })

    it('should reject periods with end before start', () => {
      const invalidPeriod = {
        start: new Date('2025-08-07T12:00:00Z'),
        end: new Date('2025-08-07T10:00:00Z'),
        isRecurring: false
      }
      
      const result = periodFormDataSchema.safeParse(invalidPeriod)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('End time must be after start time')
      }
    })

    it('should reject periods shorter than 15 minutes', () => {
      const invalidPeriod = {
        start: new Date('2025-08-07T10:00:00Z'),
        end: new Date('2025-08-07T10:10:00Z'), // Only 10 minutes
        isRecurring: false
      }
      
      const result = periodFormDataSchema.safeParse(invalidPeriod)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Period duration must be at least 15 minutes')
      }
    })

    it('should require recurrence rule for recurring periods', () => {
      const invalidPeriod = {
        start: new Date('2025-08-07T10:00:00Z'),
        end: new Date('2025-08-07T12:00:00Z'),
        isRecurring: true
        // Missing recurrenceRule
      }
      
      const result = periodFormDataSchema.safeParse(invalidPeriod)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Recurrence rule is required for recurring periods')
      }
    })

    it('should reject recurrence rule for non-recurring periods', () => {
      const invalidPeriod = {
        start: new Date('2025-08-07T10:00:00Z'),
        end: new Date('2025-08-07T12:00:00Z'),
        isRecurring: false,
        recurrenceRule: {
          type: 'daily' as const,
          interval: 1,
          count: 5
        }
      }
      
      const result = periodFormDataSchema.safeParse(invalidPeriod)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Recurrence rule should not be provided for non-recurring periods')
      }
    })
  })

  describe('recurrenceRuleSchema', () => {
    it('should validate daily recurrence rule', () => {
      const validRule = {
        type: 'daily' as const,
        interval: 2,
        count: 10
      }
      
      const result = recurrenceRuleSchema.safeParse(validRule)
      expect(result.success).toBe(true)
    })

    it('should validate weekly recurrence rule', () => {
      const validRule = {
        type: 'weekly' as const,
        interval: 1,
        daysOfWeek: [1, 3, 5], // Monday, Wednesday, Friday
        count: 12
      }
      
      const result = recurrenceRuleSchema.safeParse(validRule)
      expect(result.success).toBe(true)
    })

    it('should require daysOfWeek for weekly recurrence', () => {
      const invalidRule = {
        type: 'weekly' as const,
        interval: 1,
        count: 12
        // Missing daysOfWeek
      }
      
      const result = recurrenceRuleSchema.safeParse(invalidRule)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Days of week must be specified for weekly recurrence')
      }
    })

    it('should validate monthly recurrence rule with dayOfMonth', () => {
      const validRule = {
        type: 'monthly' as const,
        interval: 1,
        dayOfMonth: 15,
        count: 6
      }
      
      const result = recurrenceRuleSchema.safeParse(validRule)
      expect(result.success).toBe(true)
    })

    it('should validate monthly recurrence rule with weekOfMonth and dayOfWeek', () => {
      const validRule = {
        type: 'monthly' as const,
        interval: 1,
        weekOfMonth: 2, // Second week
        dayOfWeek: 1,   // Monday
        count: 6
      }
      
      const result = recurrenceRuleSchema.safeParse(validRule)
      expect(result.success).toBe(true)
    })

    it('should require month for yearly recurrence', () => {
      const invalidRule = {
        type: 'yearly' as const,
        interval: 1,
        count: 5
        // Missing month
      }
      
      const result = recurrenceRuleSchema.safeParse(invalidRule)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Month must be specified for yearly recurrence')
      }
    })

    it('should reject both endDate and count', () => {
      const invalidRule = {
        type: 'daily' as const,
        interval: 1,
        endDate: new Date('2025-12-31'),
        count: 10 // Cannot have both
      }
      
      const result = recurrenceRuleSchema.safeParse(invalidRule)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Either end date or count must be provided, but not both')
      }
    })
  })

  describe('bulkPeriodCreateSchema', () => {
    it('should validate non-overlapping periods', () => {
      const validData = {
        bookingId: 1,
        periods: [
          {
            start: new Date('2025-08-07T10:00:00Z'),
            end: new Date('2025-08-07T12:00:00Z'),
            isRecurring: false
          },
          {
            start: new Date('2025-08-07T14:00:00Z'),
            end: new Date('2025-08-07T16:00:00Z'),
            isRecurring: false
          }
        ]
      }
      
      const result = bulkPeriodCreateSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })

    it('should reject overlapping periods', () => {
      const invalidData = {
        bookingId: 1,
        periods: [
          {
            start: new Date('2025-08-07T10:00:00Z'),
            end: new Date('2025-08-07T12:00:00Z'),
            isRecurring: false
          },
          {
            start: new Date('2025-08-07T11:00:00Z'), // Overlaps with first period
            end: new Date('2025-08-07T13:00:00Z'),
            isRecurring: false
          }
        ]
      }
      
      const result = bulkPeriodCreateSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toContain('Periods within the same booking cannot overlap')
      }
    })
  })

  describe('Helper functions', () => {
    it('should validate period dates correctly', () => {
      const validStart = new Date('2025-08-07T10:00:00Z')
      const validEnd = new Date('2025-08-07T12:00:00Z')
      
      expect(validatePeriodDates(validStart, validEnd)).toBe(true)
      expect(validatePeriodDates(validEnd, validStart)).toBe(false) // Reversed
    })

    it('should detect period overlaps correctly', () => {
      const period1 = {
        start: new Date('2025-08-07T10:00:00Z'),
        end: new Date('2025-08-07T12:00:00Z')
      }
      const period2 = {
        start: new Date('2025-08-07T11:00:00Z'),
        end: new Date('2025-08-07T13:00:00Z')
      }
      const period3 = {
        start: new Date('2025-08-07T14:00:00Z'),
        end: new Date('2025-08-07T16:00:00Z')
      }
      
      expect(validatePeriodOverlap(period1, period2)).toBe(true)  // Overlapping
      expect(validatePeriodOverlap(period1, period3)).toBe(false) // Non-overlapping
    })

    it('should validate periods array for overlaps', () => {
      const validPeriods = [
        {
          start: new Date('2025-08-07T10:00:00Z'),
          end: new Date('2025-08-07T12:00:00Z')
        },
        {
          start: new Date('2025-08-07T14:00:00Z'),
          end: new Date('2025-08-07T16:00:00Z')
        }
      ]
      
      const invalidPeriods = [
        {
          start: new Date('2025-08-07T10:00:00Z'),
          end: new Date('2025-08-07T12:00:00Z')
        },
        {
          start: new Date('2025-08-07T11:00:00Z'),
          end: new Date('2025-08-07T13:00:00Z')
        }
      ]
      
      expect(validatePeriodsArray(validPeriods)).toBe(true)
      expect(validatePeriodsArray(invalidPeriods)).toBe(false)
    })
  })
})