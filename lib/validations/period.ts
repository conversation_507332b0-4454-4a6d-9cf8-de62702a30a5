import { z } from "zod"

// Recurrence type enum
export const RecurrenceType = z.enum(['daily', 'weekly', 'monthly', 'yearly'])

// Recurrence rule validation schema
export const recurrenceRuleSchema = z.object({
  type: RecurrenceType,
  interval: z.number()
    .int("Interval must be a whole number")
    .min(1, "Interval must be at least 1")
    .max(365, "Interval cannot exceed 365"),
  endDate: z.date().optional(),
  count: z.number()
    .int("Count must be a whole number")
    .min(1, "Count must be at least 1")
    .max(1000, "Count cannot exceed 1000")
    .optional(),
  
  // Weekly specific fields
  daysOfWeek: z.array(z.number().int().min(0).max(6))
    .optional()
    .refine(days => !days || days.length > 0, "At least one day must be selected for weekly recurrence"),
  
  // Monthly specific fields
  dayOfMonth: z.number()
    .int("Day of month must be a whole number")
    .min(1, "Day of month must be between 1 and 31")
    .max(31, "Day of month must be between 1 and 31")
    .optional(),
  weekOfMonth: z.number()
    .int("Week of month must be a whole number")
    .min(-1, "Week of month must be between -1 and 4")
    .max(4, "Week of month must be between -1 and 4")
    .optional(),
  dayOfWeek: z.number()
    .int("Day of week must be a whole number")
    .min(0, "Day of week must be between 0 and 6")
    .max(6, "Day of week must be between 0 and 6")
    .optional(),
  
  // Yearly specific fields
  month: z.number()
    .int("Month must be a whole number")
    .min(1, "Month must be between 1 and 12")
    .max(12, "Month must be between 1 and 12")
    .optional(),
  dayOfYear: z.number()
    .int("Day of year must be a whole number")
    .min(1, "Day of year must be between 1 and 365")
    .max(365, "Day of year must be between 1 and 365")
    .optional()
}).refine(data => {
  // Validate that either endDate or count is provided, but not both
  if (data.endDate && data.count) {
    return false
  }
  return true
}, {
  message: "Either end date or count must be provided, but not both",
  path: ["endDate"]
}).refine(data => {
  // Validate weekly recurrence has daysOfWeek
  if (data.type === 'weekly' && (!data.daysOfWeek || data.daysOfWeek.length === 0)) {
    return false
  }
  return true
}, {
  message: "Days of week must be specified for weekly recurrence",
  path: ["daysOfWeek"]
}).refine(data => {
  // Validate monthly recurrence has either dayOfMonth or (weekOfMonth + dayOfWeek)
  if (data.type === 'monthly') {
    const hasDayOfMonth = data.dayOfMonth !== undefined
    const hasWeekAndDay = data.weekOfMonth !== undefined && data.dayOfWeek !== undefined
    
    if (!hasDayOfMonth && !hasWeekAndDay) {
      return false
    }
    
    // Cannot have both dayOfMonth and weekOfMonth/dayOfWeek
    if (hasDayOfMonth && hasWeekAndDay) {
      return false
    }
  }
  return true
}, {
  message: "Monthly recurrence must specify either day of month or week/day combination",
  path: ["dayOfMonth"]
}).refine(data => {
  // Validate yearly recurrence has month
  if (data.type === 'yearly' && !data.month) {
    return false
  }
  return true
}, {
  message: "Month must be specified for yearly recurrence",
  path: ["month"]
})

// Date validation helpers
const dateSchema = z.date()
  .refine(date => date instanceof Date && !isNaN(date.getTime()), "Invalid date")

const startDateSchema = dateSchema
  .refine(date => date > new Date(), "Start date must be in the future")

const endDateSchema = dateSchema

// Period form data validation schema
export const periodFormDataSchema = z.object({
  start: startDateSchema,
  end: endDateSchema,
  isRecurring: z.boolean().default(false),
  recurrenceRule: recurrenceRuleSchema.optional()
}).refine(data => {
  // Validate that end date is after start date
  return data.end > data.start
}, {
  message: "End time must be after start time",
  path: ["end"]
}).refine(data => {
  // Validate minimum period duration (15 minutes)
  const duration = data.end.getTime() - data.start.getTime()
  const minDuration = 15 * 60 * 1000 // 15 minutes in milliseconds
  return duration >= minDuration
}, {
  message: "Period duration must be at least 15 minutes",
  path: ["end"]
}).refine(data => {
  // Validate that recurrence rule is provided for recurring periods
  if (data.isRecurring && !data.recurrenceRule) {
    return false
  }
  return true
}, {
  message: "Recurrence rule is required for recurring periods",
  path: ["recurrenceRule"]
}).refine(data => {
  // Validate that non-recurring periods don't have recurrence rules
  if (!data.isRecurring && data.recurrenceRule) {
    return false
  }
  return true
}, {
  message: "Recurrence rule should not be provided for non-recurring periods",
  path: ["recurrenceRule"]
})

// Period creation schema (for API endpoints)
export const periodCreateSchema = z.object({
  bookingId: z.number()
    .int("Booking ID must be a whole number")
    .positive("Booking ID must be a positive number"),
  start: startDateSchema,
  end: endDateSchema,
  isRecurring: z.boolean().default(false),
  recurrenceRule: recurrenceRuleSchema.optional()
}).refine(data => {
  return data.end > data.start
}, {
  message: "End time must be after start time",
  path: ["end"]
}).refine(data => {
  const duration = data.end.getTime() - data.start.getTime()
  const minDuration = 15 * 60 * 1000 // 15 minutes
  return duration >= minDuration
}, {
  message: "Period duration must be at least 15 minutes",
  path: ["end"]
}).refine(data => {
  if (data.isRecurring && !data.recurrenceRule) {
    return false
  }
  return true
}, {
  message: "Recurrence rule is required for recurring periods",
  path: ["recurrenceRule"]
})

// Period update schema (allows past dates for existing periods)
export const periodUpdateSchema = z.object({
  start: dateSchema,
  end: dateSchema,
  isRecurring: z.boolean().default(false),
  recurrenceRule: recurrenceRuleSchema.optional()
}).refine(data => {
  return data.end > data.start
}, {
  message: "End time must be after start time",
  path: ["end"]
}).refine(data => {
  const duration = data.end.getTime() - data.start.getTime()
  const minDuration = 15 * 60 * 1000 // 15 minutes
  return duration >= minDuration
}, {
  message: "Period duration must be at least 15 minutes",
  path: ["end"]
}).refine(data => {
  if (data.isRecurring && !data.recurrenceRule) {
    return false
  }
  return true
}, {
  message: "Recurrence rule is required for recurring periods",
  path: ["recurrenceRule"]
})

// Bulk period creation schema
export const bulkPeriodCreateSchema = z.object({
  bookingId: z.number()
    .int("Booking ID must be a whole number")
    .positive("Booking ID must be a positive number"),
  periods: z.array(periodFormDataSchema)
    .min(1, "At least one period is required")
    .max(100, "Cannot create more than 100 periods at once")
}).refine(data => {
  // Validate no overlapping periods within the same booking
  for (let i = 0; i < data.periods.length; i++) {
    for (let j = i + 1; j < data.periods.length; j++) {
      const period1 = data.periods[i]
      const period2 = data.periods[j]
      
      // Check for overlap: period1.start < period2.end && period2.start < period1.end
      if (period1.start < period2.end && period2.start < period1.end) {
        return false
      }
    }
  }
  return true
}, {
  message: "Periods within the same booking cannot overlap",
  path: ["periods"]
})

// Period conflict check schema
export const periodConflictCheckSchema = z.object({
  resourceIds: z.array(z.number().int().positive())
    .min(1, "At least one resource must be specified"),
  periods: z.array(z.object({
    start: dateSchema,
    end: dateSchema
  })).min(1, "At least one period must be specified"),
  excludeBookingId: z.number().int().positive().optional()
})

// Period ID parameter validation
export const periodIdSchema = z.object({
  id: z.coerce.number().int().positive("Invalid period ID")
})

// Recurring period deletion schema
export const recurringPeriodDeleteSchema = z.object({
  deleteType: z.enum(['single', 'series', 'future']).default('single')
})

// Type exports for use in components and services
export type RecurrenceTypeEnum = z.infer<typeof RecurrenceType>
export type RecurrenceRuleInput = z.infer<typeof recurrenceRuleSchema>
export type PeriodFormDataInput = z.infer<typeof periodFormDataSchema>
export type PeriodCreateInput = z.infer<typeof periodCreateSchema>
export type PeriodUpdateInput = z.infer<typeof periodUpdateSchema>
export type BulkPeriodCreateInput = z.infer<typeof bulkPeriodCreateSchema>
export type PeriodConflictCheckInput = z.infer<typeof periodConflictCheckSchema>
export type PeriodIdInput = z.infer<typeof periodIdSchema>
export type RecurringPeriodDeleteInput = z.infer<typeof recurringPeriodDeleteSchema>

// Validation helper functions
export const validateRecurrenceType = (type: string): type is RecurrenceTypeEnum => {
  return RecurrenceType.safeParse(type).success
}

export const getPeriodFormErrors = (error: z.ZodError) => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    if (!errors[path]) {
      errors[path] = err.message
    }
  })
  
  return errors
}

// Period validation helpers
export const validatePeriodDates = (start: Date, end: Date): boolean => {
  if (end <= start) return false
  
  const duration = end.getTime() - start.getTime()
  const minDuration = 15 * 60 * 1000 // 15 minutes
  
  return duration >= minDuration
}

export const validatePeriodOverlap = (
  period1: { start: Date; end: Date },
  period2: { start: Date; end: Date }
): boolean => {
  // Returns true if periods overlap
  return period1.start < period2.end && period2.start < period1.end
}

export const validatePeriodsArray = (periods: { start: Date; end: Date }[]): boolean => {
  // Check for overlaps within the array
  for (let i = 0; i < periods.length; i++) {
    for (let j = i + 1; j < periods.length; j++) {
      if (validatePeriodOverlap(periods[i], periods[j])) {
        return false
      }
    }
  }
  return true
}

// Recurrence rule validation helpers
export const validateRecurrenceRule = (rule: RecurrenceRuleInput): boolean => {
  return recurrenceRuleSchema.safeParse(rule).success
}

export const getRecurrenceRuleErrors = (rule: any): Record<string, string> => {
  const result = recurrenceRuleSchema.safeParse(rule)
  if (result.success) return {}
  
  return getPeriodFormErrors(result.error)
}