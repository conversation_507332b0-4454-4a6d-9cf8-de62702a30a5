import { z } from "zod"
import { periodFormDataSchema } from "./period"

// Booking status enum to match Prisma schema
export const BookingStatus = z.enum(['PENDING', 'CONFIRMED', 'CANCELLED'])

// Customer ID validation
const customerIdSchema = z.number()
  .int("Customer ID must be a whole number")
  .positive("Customer ID must be a positive number")
  .refine(val => val > 0, "Please select a valid customer")

// Resource IDs validation
const resourceIdsSchema = z.array(z.number().int().positive())
  .min(1, "At least one resource must be selected")
  .max(10, "Cannot select more than 10 resources")
  .refine(ids => new Set(ids).size === ids.length, "Duplicate resources are not allowed")

// Date validation helpers
const dateSchema = z.date()
  .refine(date => date instanceof Date && !isNaN(date.getTime()), "Invalid date")

const startDateSchema = dateSchema
  .refine(date => date > new Date(), "Start date must be in the future")

const endDateSchema = dateSchema

// Catering validation
const cateringItemSchema = z.object({
  cateringId: z.number().int().positive("Invalid catering ID"),
  quantity: z.number()
    .int("Quantity must be a whole number")
    .min(1, "Quantity must be at least 1")
    .max(1000, "Quantity cannot exceed 1000")
})

const cateringsSchema = z.array(cateringItemSchema)
  .default([])
  .refine(caterings => {
    const cateringIds = caterings.map(c => c.cateringId)
    return new Set(cateringIds).size === cateringIds.length
  }, "Duplicate catering items are not allowed")

// Schema for creating a new booking with periods
export const bookingCreateSchema = z.object({
  customerId: customerIdSchema,
  resourceIds: resourceIdsSchema,
  status: BookingStatus.default('PENDING'),
  periods: z.array(periodFormDataSchema)
    .min(1, "At least one period is required")
    .max(50, "Cannot have more than 50 periods per booking"),
  caterings: cateringsSchema
}).refine(data => {
  // Validate no overlapping periods within the same booking
  for (let i = 0; i < data.periods.length; i++) {
    for (let j = i + 1; j < data.periods.length; j++) {
      const period1 = data.periods[i]
      const period2 = data.periods[j]
      
      // Check for overlap: period1.start < period2.end && period2.start < period1.end
      if (period1.start < period2.end && period2.start < period1.end) {
        return false
      }
    }
  }
  return true
}, {
  message: "Periods within the same booking cannot overlap",
  path: ["periods"]
}).refine(data => {
  // Validate that booking periods are not too far in the future (1 year)
  const oneYearFromNow = new Date()
  oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1)
  
  return data.periods.every(period => period.start <= oneYearFromNow)
}, {
  message: "Booking periods cannot be scheduled more than 1 year in advance",
  path: ["periods"]
}).refine(data => {
  // Validate that all periods are properly sorted by start time
  const sortedPeriods = [...data.periods].sort((a, b) => a.start.getTime() - b.start.getTime())
  return data.periods.every((period, index) => 
    period.start.getTime() === sortedPeriods[index].start.getTime()
  )
}, {
  message: "Periods should be provided in chronological order",
  path: ["periods"]
}).refine(data => {
  // Validate minimum gap between consecutive periods (15 minutes)
  const sortedPeriods = [...data.periods].sort((a, b) => a.start.getTime() - b.start.getTime())
  for (let i = 0; i < sortedPeriods.length - 1; i++) {
    const currentEnd = sortedPeriods[i].end.getTime()
    const nextStart = sortedPeriods[i + 1].start.getTime()
    const gap = nextStart - currentEnd
    const minGap = 15 * 60 * 1000 // 15 minutes
    
    if (gap < minGap && gap !== 0) { // Allow touching periods (gap = 0) but enforce minimum gap otherwise
      return false
    }
  }
  return true
}, {
  message: "There must be at least 15 minutes between consecutive periods or they must be touching",
  path: ["periods"]
}).refine(data => {
  // Validate that catering is only allowed for confirmed bookings
  if (data.caterings.length > 0 && data.status !== 'CONFIRMED') {
    return false
  }
  return true
}, {
  message: "Catering options are only available for confirmed bookings",
  path: ["caterings"]
})

// Schema for updating a booking with periods
export const bookingUpdateSchema = z.object({
  customerId: customerIdSchema,
  resourceIds: resourceIdsSchema,
  status: BookingStatus,
  periods: z.array(z.object({
    start: dateSchema, // Allow past dates for updates
    end: dateSchema,
    isRecurring: z.boolean().default(false),
    recurrenceRule: z.any().optional() // Will be validated by periodFormDataSchema if needed
  })).min(1, "At least one period is required")
    .max(50, "Cannot have more than 50 periods per booking"),
  caterings: cateringsSchema
}).refine(data => {
  // Validate that each period has valid duration
  return data.periods.every(period => {
    const duration = period.end.getTime() - period.start.getTime()
    const minDuration = 15 * 60 * 1000 // 15 minutes
    return duration >= minDuration
  })
}, {
  message: "Each period must be at least 15 minutes long",
  path: ["periods"]
}).refine(data => {
  // Validate no overlapping periods within the same booking
  for (let i = 0; i < data.periods.length; i++) {
    for (let j = i + 1; j < data.periods.length; j++) {
      const period1 = data.periods[i]
      const period2 = data.periods[j]
      
      // Check for overlap: period1.start < period2.end && period2.start < period1.end
      if (period1.start < period2.end && period2.start < period1.end) {
        return false
      }
    }
  }
  return true
}, {
  message: "Periods within the same booking cannot overlap",
  path: ["periods"]
}).refine(data => {
  // Validate that periods are not scheduled too far in the past (more than 1 year)
  const oneYearAgo = new Date()
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
  
  return data.periods.every(period => period.start >= oneYearAgo)
}, {
  message: "Periods cannot be scheduled more than 1 year in the past",
  path: ["periods"]
}).refine(data => {
  // Validate that catering is only allowed for confirmed bookings
  if (data.caterings.length > 0 && data.status !== 'CONFIRMED') {
    return false
  }
  return true
}, {
  message: "Catering options are only available for confirmed bookings",
  path: ["caterings"]
})

// Schema for booking search and filtering
export const bookingSearchSchema = z.object({
  search: z.string().optional(),
  customerId: z.coerce.number().int().positive().optional(),
  status: BookingStatus.optional(),
  resourceId: z.coerce.number().int().positive().optional(),
  invoiceStatus: z.string().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
}).refine(data => {
  // If both start and end dates are provided, validate the range
  if (data.startDate && data.endDate) {
    return data.endDate >= data.startDate
  }
  return true
}, {
  message: "End date must be after or equal to start date",
  path: ["endDate"]
})

// Schema for booking ID parameter validation
export const bookingIdSchema = z.object({
  id: z.coerce.number().int().positive("Invalid booking ID")
})

// Schema for calendar event formatting
export const calendarEventSchema = z.object({
  id: z.string(),
  title: z.string(),
  start: z.date(),
  end: z.date(),
  backgroundColor: z.string().optional(),
  borderColor: z.string().optional(),
  textColor: z.string().optional(),
  extendedProps: z.object({
    bookingId: z.number(),
    customerName: z.string(),
    status: BookingStatus,
    resourceNames: z.array(z.string())
  })
})

// Type exports for use in components
export type BookingCreateInput = z.infer<typeof bookingCreateSchema>
export type BookingUpdateInput = z.infer<typeof bookingUpdateSchema>
export type BookingSearchInput = z.infer<typeof bookingSearchSchema>
export type BookingIdInput = z.infer<typeof bookingIdSchema>
export type BookingStatusType = z.infer<typeof BookingStatus>
export type CalendarEventInput = z.infer<typeof calendarEventSchema>

// Additional type exports for enhanced validation
export type BookingPeriodIntegrityResult = ReturnType<typeof validateBookingPeriodIntegrity>
export type BookingResourceConsistencyResult = ReturnType<typeof validateBookingResourceConsistency>

// Validation helper functions
export const validateBookingStatus = (status: string): status is BookingStatusType => {
  return BookingStatus.safeParse(status).success
}

export const getBookingFormErrors = (error: z.ZodError) => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    if (!errors[path]) {
      errors[path] = err.message
    }
  })
  
  return errors
}

// Enhanced validation helper for period conflicts
export const validateBookingPeriodIntegrity = (periods: { start: Date; end: Date }[]): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = []
  const warnings: string[] = []
  
  if (periods.length === 0) {
    errors.push("At least one period is required")
    return { isValid: false, errors, warnings }
  }
  
  // Sort periods by start time for validation
  const sortedPeriods = [...periods].sort((a, b) => a.start.getTime() - b.start.getTime())
  
  // Check individual period validity
  periods.forEach((period, index) => {
    const duration = period.end.getTime() - period.start.getTime()
    if (duration < 15 * 60 * 1000) {
      errors.push(`Period ${index + 1} is too short (minimum 15 minutes required)`)
    }
    if (duration > 24 * 60 * 60 * 1000) {
      warnings.push(`Period ${index + 1} is longer than 24 hours`)
    }
  })
  
  // Check for overlaps
  for (let i = 0; i < sortedPeriods.length; i++) {
    for (let j = i + 1; j < sortedPeriods.length; j++) {
      const period1 = sortedPeriods[i]
      const period2 = sortedPeriods[j]
      
      if (period1.start < period2.end && period2.start < period1.end) {
        errors.push(`Period ${i + 1} overlaps with period ${j + 1}`)
      }
    }
  }
  
  // Check gaps between consecutive periods
  for (let i = 0; i < sortedPeriods.length - 1; i++) {
    const currentEnd = sortedPeriods[i].end.getTime()
    const nextStart = sortedPeriods[i + 1].start.getTime()
    const gap = nextStart - currentEnd
    
    if (gap > 0 && gap < 15 * 60 * 1000) {
      warnings.push(`Small gap (${Math.round(gap / 60000)} minutes) between period ${i + 1} and ${i + 2}`)
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Booking validation helpers
export const validateBookingPeriods = (periods: { start: Date; end: Date }[]): boolean => {
  // Check each period individually
  for (const period of periods) {
    if (period.end <= period.start) return false
    
    const duration = period.end.getTime() - period.start.getTime()
    const minDuration = 15 * 60 * 1000 // 15 minutes
    
    if (duration < minDuration) return false
  }
  
  // Check for overlaps between periods
  for (let i = 0; i < periods.length; i++) {
    for (let j = i + 1; j < periods.length; j++) {
      const period1 = periods[i]
      const period2 = periods[j]
      
      // Check for overlap: period1.start < period2.end && period2.start < period1.end
      if (period1.start < period2.end && period2.start < period1.end) {
        return false
      }
    }
  }
  
  return true
}

export const validateBookingConflict = (
  periods: { start: Date; end: Date }[], 
  resourceIds: number[], 
  excludeBookingId?: number
): boolean => {
  // This would typically check against the database for conflicts
  // For now, just validate the basic period logic
  return validateBookingPeriods(periods)
}

// Enhanced validation for booking-resource consistency
export const validateBookingResourceConsistency = (data: {
  periods: { start: Date; end: Date }[];
  resourceIds: number[];
  caterings: { cateringId: number; quantity: number }[];
}): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = []
  const warnings: string[] = []
  
  // Validate resource allocation
  if (data.resourceIds.length === 0) {
    errors.push("At least one resource must be selected")
  }
  
  if (data.resourceIds.length > 10) {
    warnings.push("Large number of resources selected - consider splitting into multiple bookings")
  }
  
  // Validate catering consistency
  if (data.caterings.length > 0 && data.periods.length === 0) {
    errors.push("Cannot add catering without booking periods")
  }
  
  const totalDuration = data.periods.reduce((total, period) => {
    return total + (period.end.getTime() - period.start.getTime())
  }, 0)
  
  const totalHours = totalDuration / (60 * 60 * 1000)
  
  if (data.caterings.length > 0 && totalHours < 1) {
    warnings.push("Catering ordered for booking less than 1 hour")
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Legacy helper for backward compatibility
export const validateBookingDates = (start: Date, end: Date): boolean => {
  if (end <= start) return false
  
  const duration = end.getTime() - start.getTime()
  const minDuration = 15 * 60 * 1000 // 15 minutes
  
  return duration >= minDuration
}

// Calendar event formatting helper (updated for periods)
export const formatBookingForCalendar = (booking: {
  id: number
  customer: { name: string }
  resources: { name: string }[]
  status: BookingStatusType
  periods: { start: Date; end: Date }[]
}): CalendarEventInput[] => {
  const statusColors = {
    PENDING: { backgroundColor: '#fbbf24', borderColor: '#f59e0b' },
    CONFIRMED: { backgroundColor: '#10b981', borderColor: '#059669' },
    CANCELLED: { backgroundColor: '#ef4444', borderColor: '#dc2626' }
  }

  // Create a calendar event for each period
  return booking.periods.map((period, index) => {
    const isMultiPeriod = booking.periods.length > 1
    const periodLabel = isMultiPeriod ? ` (${index + 1}/${booking.periods.length})` : ''
    
    return {
      id: `${booking.id}-${index}`,
      title: `${booking.customer.name}${periodLabel} - ${booking.resources.map(r => r.name).join(', ')}`,
      start: period.start,
      end: period.end,
      ...statusColors[booking.status],
      textColor: '#ffffff',
      extendedProps: {
        bookingId: booking.id,
        customerName: booking.customer.name,
        status: booking.status,
        resourceNames: booking.resources.map(r => r.name),
        periodIndex: index,
        totalPeriods: booking.periods.length,
        isMultiPeriod
      }
    }
  })
}

// Legacy helper for backward compatibility
export const formatBookingForCalendarLegacy = (booking: {
  id: number
  customer: { name: string }
  resources: { name: string }[]
  status: BookingStatusType
  start: Date
  end: Date
}): CalendarEventInput => {
  const statusColors = {
    PENDING: { backgroundColor: '#fbbf24', borderColor: '#f59e0b' },
    CONFIRMED: { backgroundColor: '#10b981', borderColor: '#059669' },
    CANCELLED: { backgroundColor: '#ef4444', borderColor: '#dc2626' }
  }

  return {
    id: booking.id.toString(),
    title: `${booking.customer.name} - ${booking.resources.map(r => r.name).join(', ')}`,
    start: booking.start,
    end: booking.end,
    ...statusColors[booking.status],
    textColor: '#ffffff',
    extendedProps: {
      bookingId: booking.id,
      customerName: booking.customer.name,
      status: booking.status,
      resourceNames: booking.resources.map(r => r.name)
    }
  }
}