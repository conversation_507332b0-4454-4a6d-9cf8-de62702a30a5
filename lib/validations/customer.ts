import { z } from "zod"

// Enhanced name validation with comprehensive rules
const customerNameSchema = z.string()
  .min(1, "Customer name is required")
  .max(100, "Customer name must be less than 100 characters")
  .regex(/^[a-zA-Z\s\-'\.]+$/, "Name can only contain letters, spaces, hyphens, apostrophes, and periods")
  .transform(val => val.trim())
  .refine(val => val.length > 0, "Cannot be empty or only spaces")
  .refine(val => !val.toLowerCase().includes('test'), "Name cannot contain 'test'")
  .refine(val => !/^\d+$/.test(val), "Name cannot be only numbers")

// Enhanced email validation (optional)
const emailSchema = z.string()
  .transform(val => val.trim())
  .optional()
  .refine(val => !val || val.length === 0 || z.string().email().safeParse(val).success, "Please enter a valid email address")
  .refine(val => !val || val.length <= 255, "Email must be less than 255 characters")
  .refine(val => !val || !val.includes('..'), "Email cannot contain consecutive dots")
  .refine(val => !val || (!val.startsWith('.') && !val.endsWith('.')), "Email cannot start or end with a dot")
  .transform(val => val && val.length > 0 ? val.toLowerCase() : undefined)

// Phone number validation (required)
const phoneNumberSchema = z.string()
  .min(1, "Phone number is required")
  .max(20, "Phone number must be less than 20 characters")
  .regex(/^[\+]?[0-9\s\-\(\)]+$/, "Phone number can only contain numbers, spaces, hyphens, parentheses, and plus sign")
  .transform(val => val.trim())
  .refine(val => val.length > 0, "Cannot be empty or only spaces")

// Company name validation (optional)
const companyNameSchema = z.string()
  .transform(val => val.trim())
  .optional()
  .refine(val => !val || val.length === 0 || val.length <= 100, "Company name must be less than 100 characters")
  .refine(val => !val || val.length === 0 || /^[a-zA-Z0-9\s\-_&()\.]+$/.test(val), "Company name can only contain letters, numbers, spaces, and basic punctuation")
  .transform(val => val && val.length > 0 ? val : undefined)

// Specialization validation (optional)
const specializationSchema = z.string()
  .max(100, "Specialization must be less than 100 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Industry validation (optional)
const industrySchema = z.string()
  .max(100, "Industry must be less than 100 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Website URL validation (optional)
const websiteSchema = z.string()
  .transform(val => val.trim())
  .optional()
  .refine(val => !val || val.length === 0 || val.length <= 255, "Website URL must be less than 255 characters")
  .refine(val => !val || val.length === 0 || z.string().url().safeParse(val).success, "Please enter a valid website URL")
  .refine(val => !val || val.length === 0 || val.startsWith('http'), "Website URL must start with http:// or https://")
  .transform(val => val && val.length > 0 ? val : undefined)

// LinkedIn URL validation (optional)
const linkedInSchema = z.string()
  .transform(val => val.trim())
  .optional()
  .refine(val => !val || val.length === 0 || val.length <= 255, "LinkedIn URL must be less than 255 characters")
  .refine(val => !val || val.length === 0 || z.string().url().safeParse(val).success, "Please enter a valid LinkedIn URL")
  .refine(val => !val || val.length === 0 || val.includes('linkedin.com'), "Must be a valid LinkedIn URL")
  .transform(val => val && val.length > 0 ? val : undefined)

// Social media validation (optional)
const socialMediaSchema = z.string()
  .max(255, "Social media must be less than 255 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Notes validation (optional)
const notesSchema = z.string()
  .max(1000, "Notes must be less than 1000 characters")
  .optional()
  .transform(val => val?.trim() || undefined)

// Schema for creating a new customer
export const customerCreateSchema = z.object({
  name: customerNameSchema,
  email: emailSchema,
  phoneNumber: phoneNumberSchema,
  companyName: companyNameSchema,
  specialization: specializationSchema,
  industry: industrySchema,
  website: websiteSchema,
  linkedIn: linkedInSchema,
  socialMedia: socialMediaSchema,
  notes: notesSchema
})

// Schema for updating a customer (same as create)
export const customerUpdateSchema = customerCreateSchema

// Schema for customer search and filtering
export const customerSearchSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10)
})

// Schema for customer ID parameter validation
export const customerIdSchema = z.object({
  id: z.coerce.number().int().positive("Invalid customer ID")
})

// Type exports for use in components
export type CustomerCreateInput = z.infer<typeof customerCreateSchema>
export type CustomerUpdateInput = z.infer<typeof customerUpdateSchema>
export type CustomerSearchInput = z.infer<typeof customerSearchSchema>
export type CustomerIdInput = z.infer<typeof customerIdSchema>

// Validation helper functions
export const getCustomerFormErrors = (error: z.ZodError) => {
  const errors: Record<string, string> = {}
  
  error.errors.forEach((err) => {
    const path = err.path.join('.')
    if (!errors[path]) {
      errors[path] = err.message
    }
  })
  
  return errors
}

// Customer validation helper
export const validateCustomerEmail = async (email: string, excludeId?: number): Promise<boolean> => {
  // This would typically check against the database
  // For now, just validate the format
  return emailSchema.safeParse(email).success
}