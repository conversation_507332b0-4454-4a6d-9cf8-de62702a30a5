import { z } from "zod";

// Organization Settings Schema
export const organizationSettingsSchema = z.object({
  organizationName: z.string().min(1, "Organization name is required").max(200),
  address: z.string().optional().nullable(),
  email: z.string().optional().nullable(),
  phoneNumber: z.string().optional().nullable(),
  website: z.string().optional().nullable(),
  linkedin: z.string().optional().nullable(),
  facebook: z.string().optional().nullable(),
  instagram: z.string().optional().nullable(),
  paymentTerms: z.string().optional().nullable(), // rich text allowed
  paymentMethods: z.string().optional().nullable(), // rich text allowed
  support: z.string().optional().nullable(), // rich text allowed
  invoiceFooter: z.string().optional().nullable(),
  invoiceSubfooter: z.string().optional().nullable(),
  invoiceTitle: z.string().optional().nullable(),
  invoiceSubtitle: z.string().optional().nullable(),
  logoUrl: z.string().refine((val) => {
    if (!val) return true; // Allow empty/null values
    // Allow relative URLs (starting with /), full URLs, or Vercel Blob URLs
    return val.startsWith('/') ||
           /^https?:\/\/.+/.test(val) ||
           val.includes('vercel-storage.com') ||
           val.includes('blob.vercel-storage.com');
  }, "Must be a valid URL, relative path, or Vercel Blob URL").optional().nullable(),
  mainColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Must be a valid hex color code").optional().nullable(),
  secondaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Must be a valid hex color code").optional().nullable(),
});

export type OrganizationSettingsInput = z.infer<typeof organizationSettingsSchema>;


