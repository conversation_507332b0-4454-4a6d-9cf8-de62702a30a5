# Next.js Business Application

A full-stack business management application built with Next.js, featuring user authentication, booking management, and more.

## Database Configuration

This application supports both **local development** and **production deployment** with different database configurations:

### Local Development (SQLite)

For local development, the application uses a local SQLite database file:

```bash
# Copy the example environment file
cp .env.example .env.local

# The default configuration already uses local SQLite
# DATABASE_URL="file:./dev.db"

# Set up the database
pnpm db:setup
```

### Production Deployment (Turso)

For production deployment on Vercel, the application uses Turso database:

1. **Create a Turso Database**:
   ```bash
   # Install Turso CLI
   curl -sSfL https://get.turso.tech | bash

   # Create a new database
   turso db create your-app-name

   # Get the database URL and auth token
   turso db show your-app-name --url
   turso db tokens create your-app-name
   ```

2. **Configure Environment Variables in Vercel**:
   ```
   DATABASE_URL=libsql://your-app-name.turso.io
   TURSO_DATABASE_URL=libsql://your-app-name.turso.io
   TURSO_AUTH_TOKEN=your-auth-token-here
   BLOB_READ_WRITE_TOKEN=vercel_blob_rw_your-token-here
   NEXTAUTH_URL=https://your-app-name.vercel.app
   NEXTAUTH_SECRET=your-nextauth-secret-here
   NODE_ENV=production
   ```

3. **Deploy to Vercel**:
   ```bash
   vercel --prod
   ```

## Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm db:setup` - Set up database and run migrations (loads .env.local automatically)
- `pnpm prisma:studio` - Open Prisma Studio (loads .env.local automatically)
- `pnpm prisma:migrate` - Run database migrations (loads .env.local automatically)
- `pnpm prisma:push` - Push schema changes to database (loads .env.local automatically)
- `pnpm prisma:generate` - Generate Prisma client (loads .env.local automatically)

## Environment Variables

See `.env.example` for all required environment variables and their descriptions.

### Development vs Production

- **Development**: Uses local SQLite (`file:./dev.db`)
- **Production**: Uses Turso database via `DATABASE_URL` environment variable

The application automatically detects the environment and uses the appropriate database configuration.

### Prisma Commands

All Prisma commands now automatically load environment variables from `.env.local`:
- `pnpm prisma:migrate` - Create and apply database migrations
- `pnpm prisma:studio` - Open database management UI
- `pnpm prisma:push` - Push schema changes to database
- `pnpm prisma:generate` - Generate Prisma client

No need to manually set `DATABASE_URL` - the scripts handle it automatically!
