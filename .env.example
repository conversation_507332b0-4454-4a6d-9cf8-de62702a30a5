# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-here

# Database Configuration
# For local development (SQLite)
DATABASE_URL="file:./dev.db"

# For production (Turso) - uncomment and set these for production
# DATABASE_URL="libsql://your-database-name.turso.io"
# TURSO_DATABASE_URL="libsql://your-database-name.turso.io"
# TURSO_AUTH_TOKEN=your-turso-auth-token-here

# Vercel Blob Storage Configuration (for production)
BLOB_READ_WRITE_TOKEN=vercel_blob_rw_your-token-here

# Build and Environment Configuration
NODE_ENV=development
NEXT_PUBLIC_BUILD_VERSION=1.0.0

# Optional: Analytics and Monitoring
NEXT_PUBLIC_VERCEL_ANALYTICS=true
VERCEL_ENV=development

# Optional: Custom Domain (if using custom domain)
# CUSTOM_DOMAIN=https://your-custom-domain.com

# Optional: API Keys (add any external service API keys here)
# API_KEY_SERVICE_NAME=your-api-key-here

# Production Configuration (for Vercel deployment)
# NEXTAUTH_URL=https://your-app-name.vercel.app
# NODE_ENV=production
# VERCEL_ENV=production
