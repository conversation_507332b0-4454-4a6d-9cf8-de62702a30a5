/** @type {import('next').NextConfig} */
const nextConfig = {
  // Vercel-specific optimizations
  experimental: {
    // Enable skew protection for better cache consistency
    useDeploymentId: true,
    useDeploymentIdServerActions: true,
  },

  // Build optimizations
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: false,
  },

  // Image optimization (enabled for Vercel)
  images: {
    // Allow images from common domains
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    // Enable image optimization
    unoptimized: false,
    // Image formats for better performance
    formats: ['image/webp', 'image/avif'],
  },

  // Headers for better caching and security
  async headers() {
    return [
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

  // Webpack optimizations for serverless
  webpack: (config, { isServer }) => {
    // Optimize bundle size
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    // Exclude problematic files from libSQL
    config.externals.push({
      '@libsql/client': '@libsql/client',
      '@libsql/darwin-arm64': '@libsql/darwin-arm64',
      '@libsql/darwin-x64': '@libsql/darwin-x64',
      '@libsql/linux-arm64-gnu': '@libsql/linux-arm64-gnu',
      '@libsql/linux-x64-gnu': '@libsql/linux-x64-gnu',
      '@libsql/win32-x64-msvc': '@libsql/win32-x64-msvc',
    });

    return config;
  },
};

module.exports = nextConfig;
