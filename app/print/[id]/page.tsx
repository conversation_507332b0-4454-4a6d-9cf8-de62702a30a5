import { Suspense } from "react";
import { InvoicePrintPage } from "../invoice-print-page";
import { Loader2 } from "lucide-react";

interface InvoicePrintRouteProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function InvoicePrintRoute({ params }: InvoicePrintRouteProps) {
  const { id } = await params;
  
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      }
    >
      <InvoicePrintPage invoiceId={parseInt(id)} />
    </Suspense>
  );
}

export const metadata = {
  title: "Print Invoice",
  robots: "noindex, nofollow",
};


