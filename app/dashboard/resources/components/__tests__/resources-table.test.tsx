import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { ResourcesTable } from '../resources-table'
import { Resource } from '@/lib/types'

// Mock the shared components
vi.mock('@/components/ui/offline-state-handler', () => ({
  OfflineStateHandler: ({ children }: { children: React.ReactNode }) => <div data-testid="offline-handler">{children}</div>
}))

vi.mock('@/components/ui/filter-error-state', () => ({
  FilterErrorState: ({ error, onRetry }: { error: any, onRetry: () => void }) => (
    <div data-testid="filter-error-state">
      <span>{typeof error === 'string' ? error : error.message}</span>
      <button onClick={onRetry}>Retry</button>
    </div>
  )
}))

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}))

const mockResources: Resource[] = [
  {
    id: 1,
    name: 'Conference Room A',
    type: 'room',
    capacity: 50,
    hourlyRate: 100,
    description: 'Large conference room with projector',
    amenities: [
      { id: 1, name: 'Projector', icon: 'projector' },
      { id: 2, name: 'WiFi', icon: 'wifi' }
    ],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: 2,
    name: 'Meeting Room B',
    type: 'room',
    capacity: 20,
    hourlyRate: 50,
    description: 'Small meeting room for team discussions',
    amenities: [
      { id: 2, name: 'WiFi', icon: 'wifi' },
      { id: 3, name: 'Whiteboard', icon: 'whiteboard' }
    ],
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02')
  },
  {
    id: 3,
    name: 'Event Hall',
    type: 'hall',
    capacity: 200,
    hourlyRate: 300,
    description: 'Large event hall for conferences and parties',
    amenities: [
      { id: 1, name: 'Projector', icon: 'projector' },
      { id: 4, name: 'Sound System', icon: 'sound' },
      { id: 5, name: 'Stage', icon: 'stage' }
    ],
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03')
  }
]

describe('ResourcesTable', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnRefresh = vi.fn()
  const mockOnRetry = vi.fn()
  const mockOnFilterChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders resources table with data', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    expect(screen.getByTestId('offline-handler')).toBeInTheDocument()
    expect(screen.getByText('Conference Room A')).toBeInTheDocument()
    expect(screen.getByText('Meeting Room B')).toBeInTheDocument()
    expect(screen.getByText('Event Hall')).toBeInTheDocument()
  })

  it('shows loading skeleton when loading', () => {
    render(
      <ResourcesTable
        resources={[]}
        loading={true}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    expect(screen.getByTestId('resources-table-skeleton')).toBeInTheDocument()
  })

  it('shows error state when error is present', () => {
    const error = 'Failed to load resources'
    
    render(
      <ResourcesTable
        resources={[]}
        loading={false}
        error={error}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
    expect(screen.getByText(error)).toBeInTheDocument()
  })

  it('calls onRetry when retry button is clicked in error state', () => {
    render(
      <ResourcesTable
        resources={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
        onRetry={mockOnRetry}
      />
    )

    const retryButton = screen.getByText('Retry')
    fireEvent.click(retryButton)

    expect(mockOnRetry).toHaveBeenCalledTimes(1)
  })

  it('shows empty state when no resources', () => {
    render(
      <ResourcesTable
        resources={[]}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    expect(screen.getByText('No resources found')).toBeInTheDocument()
  })

  it('handles sorting by name', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    const nameHeader = screen.getByText('Name')
    fireEvent.click(nameHeader)

    // Check that resources are sorted alphabetically
    const resourceNames = screen.getAllByTestId(/resource-name-/)
    expect(resourceNames[0]).toHaveTextContent('Conference Room A')
    expect(resourceNames[1]).toHaveTextContent('Event Hall')
    expect(resourceNames[2]).toHaveTextContent('Meeting Room B')
  })

  it('handles sorting by type', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    const typeHeader = screen.getByText('Type')
    fireEvent.click(typeHeader)

    // Check that resources are sorted by type
    const resourceTypes = screen.getAllByTestId(/resource-type-/)
    expect(resourceTypes[0]).toHaveTextContent('hall')
    expect(resourceTypes[1]).toHaveTextContent('room')
    expect(resourceTypes[2]).toHaveTextContent('room')
  })

  it('handles sorting by capacity', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    const capacityHeader = screen.getByText('Capacity')
    fireEvent.click(capacityHeader)

    // Check that resources are sorted by capacity
    const resourceCapacities = screen.getAllByTestId(/resource-capacity-/)
    expect(resourceCapacities[0]).toHaveTextContent('20')
    expect(resourceCapacities[1]).toHaveTextContent('50')
    expect(resourceCapacities[2]).toHaveTextContent('200')
  })

  it('handles sorting by hourly rate', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    const rateHeader = screen.getByText('Hourly Rate')
    fireEvent.click(rateHeader)

    // Check that resources are sorted by hourly rate
    const resourceRates = screen.getAllByTestId(/resource-rate-/)
    expect(resourceRates[0]).toHaveTextContent('$50.00')
    expect(resourceRates[1]).toHaveTextContent('$100.00')
    expect(resourceRates[2]).toHaveTextContent('$300.00')
  })

  it('calls onEdit when edit button is clicked', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    const editButtons = screen.getAllByText('Edit')
    fireEvent.click(editButtons[0])

    expect(mockOnEdit).toHaveBeenCalledWith(mockResources[0])
  })

  it('calls onDelete when delete button is clicked', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    const deleteButtons = screen.getAllByText('Delete')
    fireEvent.click(deleteButtons[0])

    expect(mockOnDelete).toHaveBeenCalledWith(mockResources[0].id)
  })

  it('displays resource type badges with correct styling', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    const roomBadges = screen.getAllByText('room')
    const hallBadge = screen.getByText('hall')

    expect(roomBadges[0]).toHaveClass('badge-secondary')
    expect(roomBadges[1]).toHaveClass('badge-secondary')
    expect(hallBadge).toHaveClass('badge-primary')
  })

  it('formats currency rates correctly', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    expect(screen.getByText('$100.00')).toBeInTheDocument()
    expect(screen.getByText('$50.00')).toBeInTheDocument()
    expect(screen.getByText('$300.00')).toBeInTheDocument()
  })

  it('displays amenities correctly', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    expect(screen.getByText('Projector')).toBeInTheDocument()
    expect(screen.getByText('WiFi')).toBeInTheDocument()
    expect(screen.getByText('Whiteboard')).toBeInTheDocument()
    expect(screen.getByText('Sound System')).toBeInTheDocument()
    expect(screen.getByText('Stage')).toBeInTheDocument()
  })

  it('handles resource type filtering', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
        selectedResourceType="room"
      />
    )

    // Should only show room type resources
    expect(screen.getByText('Conference Room A')).toBeInTheDocument()
    expect(screen.getByText('Meeting Room B')).toBeInTheDocument()
    expect(screen.queryByText('Event Hall')).not.toBeInTheDocument()
  })

  it('calls onFilterChange when resource type filter changes', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    const typeFilter = screen.getByTestId('resource-type-filter')
    fireEvent.change(typeFilter, { target: { value: 'hall' } })

    expect(mockOnFilterChange).toHaveBeenCalledWith('hall')
  })

  it('displays mobile card layout on small screens', () => {
    // Mock window.innerWidth to simulate mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 640,
    })

    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    expect(screen.getByTestId('resources-mobile-view')).toBeInTheDocument()
    expect(screen.getAllByTestId(/resource-card-/)).toHaveLength(3)
  })

  it('handles retry count and retrying state', () => {
    render(
      <ResourcesTable
        resources={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
        onRetry={mockOnRetry}
        retryCount={2}
        isRetrying={true}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
  })

  it('applies proper accessibility attributes', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    const table = screen.getByRole('table')
    expect(table).toHaveAttribute('aria-label', 'Resources table')

    const columnHeaders = screen.getAllByRole('columnheader')
    expect(columnHeaders).toHaveLength(6) // Name, Type, Capacity, Rate, Amenities, Actions

    const sortButtons = screen.getAllByRole('button', { name: /sort by/i })
    expect(sortButtons.length).toBeGreaterThan(0)
  })

  it('memoizes resource cards for performance', () => {
    const { rerender } = render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    // Re-render with same props
    rerender(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    // Resource cards should be memoized and not re-render unnecessarily
    expect(screen.getAllByTestId(/resource-card-/)).toHaveLength(3)
  })

  it('optimizes filtering logic with memoization', () => {
    const { rerender } = render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
        selectedResourceType="room"
      />
    )

    // Re-render with same filter
    rerender(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
        selectedResourceType="room"
      />
    )

    // Filtered results should be memoized
    expect(screen.getByText('Conference Room A')).toBeInTheDocument()
    expect(screen.getByText('Meeting Room B')).toBeInTheDocument()
    expect(screen.queryByText('Event Hall')).not.toBeInTheDocument()
  })

  it('handles complex resource data with amenities optimization', () => {
    render(
      <ResourcesTable
        resources={mockResources}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onFilterChange={mockOnFilterChange}
      />
    )

    // Check that amenities are rendered efficiently
    const amenityElements = screen.getAllByTestId(/amenity-/)
    expect(amenityElements.length).toBeGreaterThan(0)

    // Each resource should display its amenities
    mockResources.forEach(resource => {
      resource.amenities.forEach(amenity => {
        expect(screen.getByText(amenity.name)).toBeInTheDocument()
      })
    })
  })
})