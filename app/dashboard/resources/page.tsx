"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertCircle, Plus, RefreshCw } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";

// Import components
import { ClearFiltersButton } from "@/components/dashboard/clear-filters-button";
import { PaginationControls } from "@/components/dashboard/pagination-controls";
import { ResourceTypeFilter } from "@/components/dashboard/resource-type-filter";
import { SearchInput } from "@/components/dashboard/search-input";
import { DeleteConfirmationDialog } from "./components/delete-confirmation-dialog";
import { ResourceFormDialog } from "./components/resource-form-dialog";
import { ResourceManagementErrorBoundary } from "./components/resource-management-error-boundary";
import { ResourcesTable } from "./components/resources-table";

// Import custom hook
import { useResources } from "@/hooks/use-resources";

// Import types
import {
  Amenity,
  Resource,
  ResourceFormData,
  ResourceType
} from "@/lib/types";

// Import utilities

interface DialogState {
  resourceForm: {
    open: boolean;
    resource: Resource | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    resource: Resource | null;
    loading: boolean;
  };
}

export default function ResourcesPage() {
  const [availableAmenities, setAvailableAmenities] = useState<Amenity[]>([]);
  const [amenitiesLoading, setAmenitiesLoading] = useState(true);

  // Fetch available amenities for the form
  useEffect(() => {
    const fetchAmenities = async () => {
      try {
        setAmenitiesLoading(true);
        const response = await fetch("/api/amenities?limit=100"); // Get all amenities
        const result = await response.json();

        if (result.success && result.data) {
          setAvailableAmenities(result.data.data);
        }
      } catch (error) {
        console.error("Error fetching amenities:", error);
      } finally {
        setAmenitiesLoading(false);
      }
    };

    fetchAmenities();
  }, []);

  // Use custom hook for resource data management
  const {
    resources,
    loading,
    error,
    totalResources,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    selectedType,
    createResource,
    updateResource,
    deleteResource,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    setSelectedType,
    refresh,
    clearError,
  } = useResources();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    resourceForm: {
      open: false,
      resource: null,
      loading: false,
    },
    deleteConfirmation: {
      open: false,
      resource: null,
      loading: false,
    },
  });

  // Resource form handlers
  const handleCreateResource = useCallback(() => {
    setDialogs((prev) => ({
      ...prev,
      resourceForm: { open: true, resource: null, loading: false },
    }));
  }, []);

  const handleEditResource = useCallback((resource: Resource) => {
    setDialogs((prev) => ({
      ...prev,
      resourceForm: { open: true, resource, loading: false },
    }));
  }, []);

  const handleResourceFormSubmit = useCallback(
    async (data: ResourceFormData) => {
      const isEdit = !!dialogs.resourceForm.resource;

      setDialogs((prev) => ({
        ...prev,
        resourceForm: { ...prev.resourceForm, loading: true },
      }));

      try {
        if (isEdit) {
          await updateResource(dialogs.resourceForm.resource!.id, data);
        } else {
          await createResource(data);
        }

        // Close dialog on success
        setDialogs((prev) => ({
          ...prev,
          resourceForm: { open: false, resource: null, loading: false },
        }));
      } catch (error) {
        // Error handling is done in the hook
        console.error("Error submitting resource form:", error);
      } finally {
        setDialogs((prev) => ({
          ...prev,
          resourceForm: { ...prev.resourceForm, loading: false },
        }));
      }
    },
    [dialogs.resourceForm.resource, createResource, updateResource]
  );

  // Delete resource handlers
  const handleDeleteResource = useCallback(
    (resourceId: number) => {
      const resource = resources.find((r) => r.id === resourceId);
      if (resource) {
        setDialogs((prev) => ({
          ...prev,
          deleteConfirmation: { open: true, resource, loading: false },
        }));
      }
    },
    [resources]
  );

  const handleDeleteConfirm = useCallback(async () => {
    const { resource } = dialogs.deleteConfirmation;
    if (!resource) return;

    setDialogs((prev) => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteResource(resource.id);

      // Close dialog on success
      setDialogs((prev) => ({
        ...prev,
        deleteConfirmation: { open: false, resource: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error deleting resource:", error);
    } finally {
      setDialogs((prev) => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.resource, deleteResource]);

  // Dialog close handlers
  const handleResourceFormClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs((prev) => ({
        ...prev,
        resourceForm: { open: false, resource: null, loading: false },
      }));
    }
  }, []);

  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs((prev) => ({
        ...prev,
        deleteConfirmation: { open: false, resource: null, loading: false },
      }));
    }
  }, []);

  // Filter handlers
  const handleTypeChange = useCallback(
    (type: ResourceType | "ALL") => {
      setSelectedType(type);
    },
    [setSelectedType]
  );

  const handleClearFilters = useCallback(() => {
    setSearchQuery("");
    setSelectedType("ALL");
  }, [setSearchQuery, setSelectedType]);

  // Filter state for clear button
  const filterState = useMemo(
    () => ({
      searchQuery,
      selectedType,
    }),
    [searchQuery, selectedType]
  );

  // Calculate active filters for display
  const activeFilters = useMemo(() => {
    const filters = [];
    if (searchQuery.trim()) filters.push("Search");
    if (selectedType !== "ALL") filters.push("Type");
    return filters;
  }, [searchQuery, selectedType]);

  // Memoized values for better performance
  const totalResourcesText = useMemo(
    () => `${totalResources} total resources`,
    [totalResources]
  );

  const shouldShowPagination = useMemo(
    () => !loading && resources.length > 0,
    [loading, resources.length]
  );

  return (
    <ResourceManagementErrorBoundary onRetry={refresh}>
      <div className="h-full bg-gray-50 mobile-spacing mobile-no-scroll">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Resources</h1>
              <p className="text-muted-foreground">
                Manage facility resources and spaces
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={refresh}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <RefreshCw
                  className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
                />
                Refresh
              </Button>

              <Button
                onClick={handleCreateResource}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Resource
              </Button>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                Resources
                {totalResources > 0 && (
                  <Badge variant="outline" className="ml-2">
                    {totalResources} total
                  </Badge>
                )}
                {activeFilters.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {activeFilters.length} filters
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {activeFilters.length > 0
                  ? `Showing filtered results (${activeFilters.join(", ")})`
                  : "All facility resources and spaces"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Filters Section */}
              <div className="space-y-3 pb-4 border-b">
                {/* First Line: Search */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <SearchInput
                      onSearch={setSearchQuery}
                      placeholder="Search resources by name..."
                      className="max-w-md"
                    />
                  </div>
                </div>

                {/* Second Line: Filters */}
                <div className="flex flex-col gap-4">
                  {/* Filter Columns */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <ResourceTypeFilter
                      selectedType={selectedType}
                      onTypeChange={handleTypeChange}
                      disabled={loading}
                      className="w-full"
                    />
                  </div>

                  {/* Clear Filters - Separate Row */}
                  <div className="flex justify-end">
                    <ClearFiltersButton
                      filters={filterState}
                      onClearFilters={handleClearFilters}
                      disabled={loading}
                      showFilterCount={true}
                    />
                  </div>
                </div>
              </div>

              {/* Table Content */}
              {error ? (
                <Alert variant="destructive" className="mb-6">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-3">
                      <div>
                        <p className="font-medium">Unable to load resources</p>
                        <p className="text-sm">{error}</p>
                      </div>

                      {/* Enhanced error recovery suggestions */}
                      <div className="text-sm space-y-2">
                        {error.toLowerCase().includes("network") && (
                          <div className="bg-blue-50 p-3 rounded-md">
                            <p className="font-medium text-blue-900">
                              Network Issue Detected:
                            </p>
                            <ul className="text-blue-800 space-y-1 ml-4 mt-1">
                              <li>• Check your internet connection</li>
                              <li>• Try refreshing the page</li>
                              <li>
                                • Contact IT support if the problem persists
                              </li>
                            </ul>
                          </div>
                        )}

                        {error.toLowerCase().includes("server") && (
                          <div className="bg-orange-50 p-3 rounded-md">
                            <p className="font-medium text-orange-900">
                              Server Issue Detected:
                            </p>
                            <ul className="text-orange-800 space-y-1 ml-4 mt-1">
                              <li>
                                • Our team has been automatically notified
                              </li>
                              <li>• Please try again in a few minutes</li>
                              <li>• The issue is likely temporary</li>
                            </ul>
                          </div>
                        )}

                        {error.toLowerCase().includes("timeout") && (
                          <div className="bg-yellow-50 p-3 rounded-md">
                            <p className="font-medium text-yellow-900">
                              Request Timeout:
                            </p>
                            <ul className="text-yellow-800 space-y-1 ml-4 mt-1">
                              <li>• The request took too long to complete</li>
                              <li>• This could be due to high server load</li>
                              <li>• Try again with a smaller page size</li>
                            </ul>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            clearError();
                            refresh();
                          }}
                          className="touch-target focus-enhanced"
                          aria-label="Try loading resources again"
                        >
                          <RefreshCw
                            className="mr-2 h-3 w-3"
                            aria-hidden="true"
                          />
                          Try Again
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            clearError();
                            setSearchQuery("");
                            setSelectedType("ALL");
                            setCurrentPage(1);
                            setPageSize(10); // Reset to smaller page size
                            refresh();
                          }}
                          className="touch-target focus-enhanced"
                          aria-label="Reset search and retry loading resources"
                        >
                          Reset & Retry
                        </Button>
                        {error.toLowerCase().includes("timeout") && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              clearError();
                              setPageSize(5); // Use smaller page size for timeout issues
                              refresh();
                            }}
                            className="touch-target focus-enhanced"
                            aria-label="Retry with smaller page size"
                          >
                            Load Less Data
                          </Button>
                        )}
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  <ResourcesTable
                    resources={resources}
                    loading={loading}
                    error={error}
                    onEdit={handleEditResource}
                    onDelete={handleDeleteResource}
                    onRefresh={refresh}
                    onTypeFilter={handleTypeChange}
                    selectedType={selectedType || "ALL"}
                    retryCount={0}
                    isRetrying={loading}
                    onRetry={refresh}
                  />

                  {shouldShowPagination && (
                    <div className="mt-4 sm:mt-6">
                      <PaginationControls
                        currentPage={currentPage}
                        totalPages={totalPages}
                        pageSize={pageSize}
                        totalItems={totalResources}
                        onPageChange={setCurrentPage}
                        onPageSizeChange={setPageSize}
                      />
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>

          {/* Dialogs */}
          <ResourceFormDialog
            open={dialogs.resourceForm.open}
            onOpenChange={handleResourceFormClose}
            resource={dialogs.resourceForm.resource}
            onSubmit={handleResourceFormSubmit}
            loading={dialogs.resourceForm.loading}
            availableAmenities={availableAmenities}
          />

          <DeleteConfirmationDialog
            open={dialogs.deleteConfirmation.open}
            onOpenChange={handleDeleteConfirmationClose}
            resource={dialogs.deleteConfirmation.resource}
            onConfirm={handleDeleteConfirm}
            loading={dialogs.deleteConfirmation.loading}
          />
        </div>
      </div>
    </ResourceManagementErrorBoundary>
  );
}
