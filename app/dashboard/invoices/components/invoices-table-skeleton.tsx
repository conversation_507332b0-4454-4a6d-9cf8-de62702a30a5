"use client"

import { memo, useEffect, useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Loader2, Receipt, Clock } from "lucide-react"

interface InvoicesTableSkeletonProps {
  rows?: number
  showLoadingMessage?: boolean
  loadingText?: string
}

export const InvoicesTableSkeleton = memo(({ 
  rows = 5, 
  showLoadingMessage = true,
  loadingText = "Loading invoices..."
}: InvoicesTableSkeletonProps) => {
  const [loadingDots, setLoadingDots] = useState("");
  const [elapsedTime, setElapsedTime] = useState(0);

  // Animated loading dots
  useEffect(() => {
    const interval = setInterval(() => {
      setLoadingDots(prev => {
        if (prev === "...") return "";
        return prev + ".";
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  // Track loading time
  useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const getVariableWidth = (index: number, baseWidth: number) => {
    // Create varied skeleton widths for more realistic loading appearance
    const variations = [0.8, 1.0, 0.9, 1.1, 0.85];
    const variation = variations[index % variations.length];
    return Math.floor(baseWidth * variation);
  };

  return (
    <div className="space-y-4" role="status" aria-live="polite" aria-label="Loading invoices data">
      {/* Loading Status Header */}
      {showLoadingMessage && (
        <Card className="border-dashed">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                  <Loader2 className="w-4 h-4 text-blue-600 animate-spin" aria-hidden="true" />
                </div>
                <div>
                  <p className="font-medium text-sm">
                    {loadingText}{loadingDots}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Fetching invoice data from server
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" aria-hidden="true" />
                  <span aria-label={`Loading for ${elapsedTime} seconds`}>{elapsedTime}s</span>
                </Badge>
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Receipt className="h-3 w-3" aria-hidden="true" />
                  Loading
                </Badge>
              </div>
            </div>
          </CardHeader>
        </Card>
      )}

      {/* Mobile loading cards */}
      <div className="block md:hidden space-y-4" aria-label="Loading invoice cards for mobile view">
        {Array.from({ length: rows }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-3">
                  {/* Invoice ID and status */}
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-8 w-8 rounded-md" aria-label="Invoice icon placeholder" />
                    <div className="flex-1 space-y-2">
                      <Skeleton 
                        className="h-5" 
                        style={{ width: `${getVariableWidth(index, 120)}px` }}
                        aria-label="Invoice ID placeholder"
                      />
                      <Skeleton className="h-4 w-20" aria-label="Invoice status placeholder" />
                    </div>
                  </div>
                  
                  {/* Invoice details */}
                  <div className="space-y-2 ml-11">
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-12" aria-label="Customer label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 140)}px` }}
                        aria-label="Customer name placeholder"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-16" aria-label="Date label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 100)}px` }}
                        aria-label="Booking date placeholder"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-12" aria-label="Total label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 80)}px` }}
                        aria-label="Total amount placeholder"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-10" aria-label="Paid label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 70)}px` }}
                        aria-label="Paid amount placeholder"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-14" aria-label="Created label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 90)}px` }}
                        aria-label="Created date placeholder"
                      />
                    </div>
                  </div>
                </div>
                <Skeleton className="h-9 w-9 rounded-md" aria-label="Actions menu placeholder" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Desktop loading table */}
      <div className="hidden md:block rounded-md border" aria-label="Loading invoices table for desktop view">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[100px]">
                  <div className="flex items-center space-x-2">
                    <span>Invoice ID</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[200px]">
                  <div className="flex items-center space-x-2">
                    <span>Customer</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <div className="flex items-center space-x-2">
                    <span>Booking Date</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[120px] text-right">
                  <div className="flex items-center space-x-2">
                    <span>Total Amount</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[120px] text-right">
                  <div className="flex items-center space-x-2">
                    <span>Paid Amount</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <div className="flex items-center space-x-2">
                    <span>Status</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: rows }).map((_, index) => (
                <TableRow key={index} className="animate-pulse">
                  <TableCell>
                    <Skeleton 
                      className="h-4" 
                      style={{ width: `${getVariableWidth(index, 60)}px` }}
                      aria-label="Invoice ID placeholder"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-3 w-3 rounded" aria-label="User icon placeholder" />
                      <div className="space-y-1">
                        <Skeleton 
                          className="h-4" 
                          style={{ width: `${getVariableWidth(index, 120)}px` }}
                          aria-label="Customer name placeholder"
                        />
                        <Skeleton 
                          className="h-3" 
                          style={{ width: `${getVariableWidth(index, 140)}px` }}
                          aria-label="Customer email placeholder"
                        />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-3 rounded" aria-label="Calendar icon placeholder" />
                      <Skeleton 
                        className="h-4" 
                        style={{ width: `${getVariableWidth(index, 100)}px` }}
                        aria-label="Booking date placeholder"
                      />
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Skeleton 
                      className="h-4 ml-auto" 
                      style={{ width: `${getVariableWidth(index, 80)}px` }}
                      aria-label="Total amount placeholder"
                    />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="space-y-1">
                      <Skeleton 
                        className="h-4 ml-auto" 
                        style={{ width: `${getVariableWidth(index, 70)}px` }}
                        aria-label="Paid amount placeholder"
                      />
                      <Skeleton 
                        className="h-3 ml-auto" 
                        style={{ width: `${getVariableWidth(index, 60)}px` }}
                        aria-label="Balance placeholder"
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-20 rounded-full" aria-label="Status badge placeholder" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-8 w-8 rounded-md" aria-label="Actions menu placeholder" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Loading Progress Indicator */}
      {elapsedTime > 3 && (
        <Card className="border-yellow-200 bg-yellow-50" role="alert">
          <CardContent className="p-3">
            <div className="flex items-center space-x-2 text-sm text-yellow-800">
              <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
              <span>
                Taking longer than expected... 
                {elapsedTime > 10 && " Please check your connection."}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
})

InvoicesTableSkeleton.displayName = "InvoicesTableSkeleton"