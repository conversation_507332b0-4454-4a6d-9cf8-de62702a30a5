"use client"

import { useState, memo, useMemo, useCallback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Receipt,
  User,
  Calendar,
  DollarSign,
  CreditCard,
} from "lucide-react"
import { Invoice, InvoiceStatus, INVOICE_STATUS_LABELS } from "@/lib/types"
import { format } from "date-fns"
import { formatCurrency } from "@/lib/utils/catering"
import { InvoicesTableSkeleton } from "./invoices-table-skeleton"
import { FilterErrorState } from "@/components/ui/filter-error-state"
import { OfflineStateHandler } from "@/components/ui/offline-state-handler"

interface InvoicesTableProps {
  invoices: Invoice[]
  loading: boolean
  error?: Error | string | null
  onEdit: (invoice: Invoice) => void
  onDelete: (invoice: Invoice) => void
  onRefresh: () => void
  retryCount?: number
  isRetrying?: boolean
  onRetry?: () => void
}

type SortField = 'id' | 'customerName' | 'bookingDate' | 'total' | 'paid' | 'status' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

const getStatusBadgeVariant = (status: InvoiceStatus) => {
  switch (status) {
    case 'PAID':
      return 'default'
    case 'PARTIALLY_PAID':
      return 'secondary'
    case 'PENDING':
      return 'outline'
    case 'CANCELLED':
      return 'destructive'
    default:
      return 'outline'
  }
}

// Custom comparison function for InvoiceCard to prevent unnecessary re-renders
const areInvoiceCardsEqual = (
  prevProps: { invoice: Invoice; onEdit: (invoice: Invoice) => void; onDelete: (invoice: Invoice) => void },
  nextProps: { invoice: Invoice; onEdit: (invoice: Invoice) => void; onDelete: (invoice: Invoice) => void }
) => {
  // Compare invoice data that affects rendering
  return (
    prevProps.invoice.id === nextProps.invoice.id &&
    prevProps.invoice.total === nextProps.invoice.total &&
    prevProps.invoice.paid === nextProps.invoice.paid &&
    prevProps.invoice.status === nextProps.invoice.status &&
    prevProps.invoice.createdAt === nextProps.invoice.createdAt &&
    prevProps.invoice.booking.customer.name === nextProps.invoice.booking.customer.name &&
    prevProps.invoice.booking.computedStart === nextProps.invoice.booking.computedStart &&
    prevProps.onEdit === nextProps.onEdit &&
    prevProps.onDelete === nextProps.onDelete
  )
}

// Mobile card component for individual invoices - memoized for performance with custom comparison
const InvoiceCard = memo(({ 
  invoice, 
  onEdit, 
  onDelete 
}: { 
  invoice: Invoice
  onEdit: (invoice: Invoice) => void
  onDelete: (invoice: Invoice) => void
}) => {
  // Memoize expensive calculations
  const balance = useMemo(() => Number(invoice.total) - Number(invoice.paid), [invoice.total, invoice.paid])
  
  // Memoize event handlers to prevent unnecessary re-renders
  const handleEdit = useCallback(() => onEdit(invoice), [onEdit, invoice])
  const handleDelete = useCallback(() => onDelete(invoice), [onDelete, invoice])
  
  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <div 
                className="flex items-center justify-center w-10 h-10 rounded-md bg-muted"
                aria-hidden="true"
              >
                <Receipt className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-medium text-base" id={`invoice-${invoice.id}`}>
                  Invoice #{invoice.id}
                </h3>
                <Badge variant={getStatusBadgeVariant(invoice.status)} className="text-xs">
                  {INVOICE_STATUS_LABELS[invoice.status]}
                </Badge>
              </div>
            </div>
            
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <User className="h-3 w-3" aria-hidden="true" />
                <span className="break-all">{invoice.booking.customer.name}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar className="h-3 w-3" aria-hidden="true" />
                <span>
                  {invoice.booking.computedStart 
                    ? format(new Date(invoice.booking.computedStart), 'MMM dd, yyyy')
                    : 'No periods'
                  }
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-3 w-3" aria-hidden="true" />
                  <span className="font-medium">Total:</span>
                  <span className="font-semibold text-foreground">
                    {formatCurrency(invoice.total)}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-3 w-3" aria-hidden="true" />
                  <span className="font-medium">Paid:</span>
                  <span className="font-semibold text-green-600">
                    {formatCurrency(invoice.paid)}
                  </span>
                </div>
              </div>
              
              {balance > 0 && (
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Balance:</span>
                  <span className="font-semibold text-orange-600">
                    {formatCurrency(balance)}
                  </span>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Created:</span>
                <span>{format(new Date(invoice.createdAt), 'MMM dd, yyyy')}</span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="h-10 w-10 p-0"
                aria-label={`Actions for Invoice #${invoice.id}`}
              >
                <span className="sr-only">Open actions menu for Invoice #{invoice.id}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={handleEdit}
                className="h-10 cursor-pointer"
                role="menuitem"
              >
                <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                Edit Invoice #{invoice.id}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleDelete}
                className="h-10 text-destructive cursor-pointer"
                role="menuitem"
              >
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                Delete Invoice #{invoice.id}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
}, areInvoiceCardsEqual)

export const InvoicesTable = memo(({
  invoices,
  loading,
  error,
  onEdit,
  onDelete,
  onRefresh,
  retryCount = 0,
  isRetrying = false,
  onRetry,
}: InvoicesTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdAt',
    direction: 'desc',
  })

  const handleSort = useCallback((field: SortField) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }, [])

  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  // Memoize event handlers for performance optimization
  const handleEdit = useCallback((invoice: Invoice) => {
    onEdit(invoice)
  }, [onEdit])

  const handleDelete = useCallback((invoice: Invoice) => {
    onDelete(invoice)
  }, [onDelete])

  const handleRefresh = useCallback(() => {
    onRefresh()
  }, [onRefresh])

  // Optimized sorting with performance monitoring for large datasets
  const sortedInvoices = useMemo(() => {
    const startTime = performance.now()
    
    const sorted = [...invoices].sort((a, b) => {
      const { field, direction } = sortConfig
      let aValue: any
      let bValue: any

      switch (field) {
        case 'id':
          aValue = a.id
          bValue = b.id
          break
        case 'customerName':
          // Pre-lowercase for better performance on large datasets
          aValue = a.booking.customer.name.toLowerCase()
          bValue = b.booking.customer.name.toLowerCase()
          break
        case 'bookingDate':
          // Use getTime() for numeric comparison - more efficient than Date comparison
          aValue = a.booking.computedStart ? new Date(a.booking.computedStart).getTime() : 0
          bValue = b.booking.computedStart ? new Date(b.booking.computedStart).getTime() : 0
          break
        case 'total':
          // Ensure numeric comparison for currency values
          aValue = Number(a.total)
          bValue = Number(b.total)
          break
        case 'paid':
          // Ensure numeric comparison for currency values
          aValue = Number(a.paid)
          bValue = Number(b.paid)
          break
        case 'status':
          aValue = a.status
          bValue = b.status
          break
        case 'createdAt':
          // Use getTime() for numeric comparison
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
          break
        default:
          aValue = a.id
          bValue = b.id
      }

      // Handle null/undefined values efficiently
      if (aValue == null && bValue == null) return 0
      if (aValue == null) return direction === 'asc' ? -1 : 1
      if (bValue == null) return direction === 'asc' ? 1 : -1

      // Optimized comparison
      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })

    // Performance monitoring for large datasets
    const endTime = performance.now()
    const sortTime = endTime - startTime
    
    // Log performance warning for large datasets or slow sorts
    if (invoices.length > 1000 || sortTime > 50) {
      console.warn(`Invoice table sort performance: ${sortTime.toFixed(2)}ms for ${invoices.length} items (field: ${sortConfig.field})`)
    }

    return sorted
  }, [invoices, sortConfig])

  if (loading) {
    return <InvoicesTableSkeleton />
  }

  // Handle error state with retry functionality
  if (error && !loading) {
    return (
      <FilterErrorState
        error={error}
        filterType="invoice"
        onRetry={onRetry || onRefresh}
        loading={isRetrying}
        showDetails={true}
        context="invoices"
        retryButtonText={isRetrying ? 'Retrying...' : retryCount > 0 ? `Retry (${retryCount + 1})` : 'Retry'}
      />
    )
  }

  if (invoices.length === 0) {
    return (
      <OfflineStateHandler 
        onConnectionRestored={onRefresh}
        enablePersistence={true}
        storageKey="invoices_offline"
      >
        {/* Mobile empty state */}
        <div className="block md:hidden">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center justify-center space-y-4">
                <Receipt className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">No invoices found</p>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your search or create a new invoice
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  onClick={handleRefresh} 
                  className="h-10 px-6"
                  aria-label="Refresh invoices list"
                >
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop empty state */}
        <div className="hidden md:block rounded-md border">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('id')}
                      className="h-auto p-0 font-semibold"
                    >
                      Invoice ID
                      {getSortIcon('id')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[200px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('customerName')}
                      className="h-auto p-0 font-semibold"
                    >
                      Customer
                      {getSortIcon('customerName')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('bookingDate')}
                      className="h-auto p-0 font-semibold"
                    >
                      Booking Date
                      {getSortIcon('bookingDate')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px] text-right">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('total')}
                      className="h-auto p-0 font-semibold"
                    >
                      Total Amount
                      {getSortIcon('total')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px] text-right">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('paid')}
                      className="h-auto p-0 font-semibold"
                    >
                      Paid Amount
                      {getSortIcon('paid')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('status')}
                      className="h-auto p-0 font-semibold"
                    >
                      Status
                      {getSortIcon('status')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-muted-foreground">No invoices found</p>
                      <Button 
                        variant="outline" 
                        onClick={handleRefresh} 
                        className="h-10 px-6"
                        aria-label="Refresh invoices list"
                      >
                        Refresh
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </OfflineStateHandler>
    )
  }

  return (
    <OfflineStateHandler 
      onConnectionRestored={onRefresh}
      enablePersistence={true}
      storageKey="invoices_offline"
    >
      {/* Mobile card view */}
      <div className="block md:hidden">
        {/* Mobile sort controls */}
        <div className="flex items-center justify-between mb-4 p-4 bg-muted/50 rounded-lg">
          <span className="text-sm font-medium" id="sort-label">Sort by:</span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="h-9 min-w-[120px]"
                aria-labelledby="sort-label"
                aria-expanded="false"
              >
                {sortConfig.field === 'id' && 'Invoice ID'}
                {sortConfig.field === 'customerName' && 'Customer'}
                {sortConfig.field === 'bookingDate' && 'Booking Date'}
                {sortConfig.field === 'total' && 'Total'}
                {sortConfig.field === 'paid' && 'Paid'}
                {sortConfig.field === 'status' && 'Status'}
                {sortConfig.field === 'createdAt' && 'Created'}
                {sortConfig.direction === 'asc' ? (
                  <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                ) : (
                  <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40" role="menu">
              <DropdownMenuItem 
                onClick={() => handleSort('id')}
                className="h-10 cursor-pointer"
                role="menuitem"
              >
                Invoice ID {getSortIcon('id')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('customerName')}
                className="h-10 cursor-pointer"
                role="menuitem"
              >
                Customer {getSortIcon('customerName')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('bookingDate')}
                className="h-10 cursor-pointer"
                role="menuitem"
              >
                Booking Date {getSortIcon('bookingDate')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('total')}
                className="h-10 cursor-pointer"
                role="menuitem"
              >
                Total {getSortIcon('total')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('paid')}
                className="h-10 cursor-pointer"
                role="menuitem"
              >
                Paid {getSortIcon('paid')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('status')}
                className="h-10 cursor-pointer"
                role="menuitem"
              >
                Status {getSortIcon('status')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('createdAt')}
                className="h-10 cursor-pointer"
                role="menuitem"
              >
                Created {getSortIcon('createdAt')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-4">
          {sortedInvoices.map((invoice) => (
            <InvoiceCard
              key={invoice.id}
              invoice={invoice}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          ))}
        </div>
      </div>

      {/* Desktop table view */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[100px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('id')}
                    className="h-auto p-2 font-semibold"
                    aria-label={`Sort by invoice ID ${sortConfig.field === 'id' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Invoice ID
                    {getSortIcon('id')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[200px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('customerName')}
                    className="h-auto p-2 font-semibold"
                    aria-label={`Sort by customer ${sortConfig.field === 'customerName' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Customer
                    {getSortIcon('customerName')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('bookingDate')}
                    className="h-auto p-2 font-semibold"
                    aria-label={`Sort by booking date ${sortConfig.field === 'bookingDate' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Booking Date
                    {getSortIcon('bookingDate')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('total')}
                    className="h-auto p-2 font-semibold"
                    aria-label={`Sort by total amount ${sortConfig.field === 'total' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Total Amount
                    {getSortIcon('total')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('paid')}
                    className="h-auto p-2 font-semibold"
                    aria-label={`Sort by paid amount ${sortConfig.field === 'paid' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Paid Amount
                    {getSortIcon('paid')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('status')}
                    className="h-auto p-2 font-semibold"
                    aria-label={`Sort by status ${sortConfig.field === 'status' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Status
                    {getSortIcon('status')}
                  </Button>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedInvoices.map((invoice) => {
                const balance = Number(invoice.total) - Number(invoice.paid)
                
                return (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">
                      #{invoice.id}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <User className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
                        <div>
                          <div className="font-medium">{invoice.booking.customer.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {invoice.booking.customer.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
                        <span>
                          {invoice.booking.computedStart 
                            ? format(new Date(invoice.booking.computedStart), 'MMM dd, yyyy')
                            : 'No periods'
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(invoice.total)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="space-y-1">
                        <div className="font-medium text-green-600">
                          {formatCurrency(invoice.paid)}
                        </div>
                        {balance > 0 && (
                          <div className="text-xs text-orange-600">
                            Balance: {formatCurrency(balance)}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(invoice.status)}>
                        {INVOICE_STATUS_LABELS[invoice.status]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            className="h-10 w-10 p-0"
                            aria-label={`Actions for Invoice #${invoice.id}`}
                          >
                            <span className="sr-only">Open actions menu for Invoice #{invoice.id}</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48" role="menu">
                          <DropdownMenuItem 
                            onClick={() => handleEdit(invoice)}
                            className="h-10 cursor-pointer"
                            role="menuitem"
                          >
                            <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                            Edit Invoice #{invoice.id}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDelete(invoice)}
                            className="h-10 text-destructive cursor-pointer"
                            role="menuitem"
                          >
                            <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                            Delete Invoice #{invoice.id}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    </OfflineStateHandler>
  )
})

InvoiceCard.displayName = "InvoiceCard"
InvoicesTable.displayName = "InvoicesTable"