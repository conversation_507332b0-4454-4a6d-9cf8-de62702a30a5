import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { InvoicesTable } from '../invoices-table'
import { Invoice } from '@/lib/types'

// Mock the shared components
vi.mock('@/components/ui/offline-state-handler', () => ({
  OfflineStateHandler: ({ children }: { children: React.ReactNode }) => <div data-testid="offline-handler">{children}</div>
}))

vi.mock('@/components/ui/filter-error-state', () => ({
  FilterErrorState: ({ error, onRetry }: { error: any, onRetry: () => void }) => (
    <div data-testid="filter-error-state">
      <span>{typeof error === 'string' ? error : error.message}</span>
      <button onClick={onRetry}>Retry</button>
    </div>
  )
}))

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}))

const mockInvoices: Invoice[] = [
  {
    id: 1,
    invoiceNumber: 'INV-001',
    customerId: 1,
    customer: {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '************',
      address: '123 Main St',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    amount: 1000,
    status: 'pending',
    dueDate: new Date('2024-02-01'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    lineItems: []
  },
  {
    id: 2,
    invoiceNumber: 'INV-002',
    customerId: 2,
    customer: {
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '************',
      address: '456 Oak Ave',
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02')
    },
    amount: 1500,
    status: 'paid',
    dueDate: new Date('2024-02-02'),
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    lineItems: []
  }
]

describe('InvoicesTable', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnRefresh = vi.fn()
  const mockOnRetry = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders invoices table with data', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('offline-handler')).toBeInTheDocument()
    expect(screen.getByText('INV-001')).toBeInTheDocument()
    expect(screen.getByText('INV-002')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
  })

  it('shows loading skeleton when loading', () => {
    render(
      <InvoicesTable
        invoices={[]}
        loading={true}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('invoices-table-skeleton')).toBeInTheDocument()
  })

  it('shows error state when error is present', () => {
    const error = 'Failed to load invoices'
    
    render(
      <InvoicesTable
        invoices={[]}
        loading={false}
        error={error}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
    expect(screen.getByText(error)).toBeInTheDocument()
  })

  it('calls onRetry when retry button is clicked in error state', () => {
    render(
      <InvoicesTable
        invoices={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
      />
    )

    const retryButton = screen.getByText('Retry')
    fireEvent.click(retryButton)

    expect(mockOnRetry).toHaveBeenCalledTimes(1)
  })

  it('shows empty state when no invoices', () => {
    render(
      <InvoicesTable
        invoices={[]}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('No invoices found')).toBeInTheDocument()
  })

  it('handles sorting by invoice number', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const invoiceNumberHeader = screen.getByText('Invoice #')
    fireEvent.click(invoiceNumberHeader)

    // Check that invoices are sorted
    const invoiceNumbers = screen.getAllByTestId(/invoice-number-/)
    expect(invoiceNumbers[0]).toHaveTextContent('INV-001')
    expect(invoiceNumbers[1]).toHaveTextContent('INV-002')
  })

  it('handles sorting by customer name', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const customerHeader = screen.getByText('Customer')
    fireEvent.click(customerHeader)

    // Check that invoices are sorted by customer name
    const customerNames = screen.getAllByTestId(/customer-name-/)
    expect(customerNames[0]).toHaveTextContent('Jane Smith')
    expect(customerNames[1]).toHaveTextContent('John Doe')
  })

  it('handles sorting by amount', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const amountHeader = screen.getByText('Amount')
    fireEvent.click(amountHeader)

    // Check that invoices are sorted by amount
    const amounts = screen.getAllByTestId(/invoice-amount-/)
    expect(amounts[0]).toHaveTextContent('$1,000.00')
    expect(amounts[1]).toHaveTextContent('$1,500.00')
  })

  it('handles sorting by status', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const statusHeader = screen.getByText('Status')
    fireEvent.click(statusHeader)

    // Check that invoices are sorted by status
    const statuses = screen.getAllByTestId(/invoice-status-/)
    expect(statuses[0]).toHaveTextContent('paid')
    expect(statuses[1]).toHaveTextContent('pending')
  })

  it('calls onEdit when edit button is clicked', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const editButtons = screen.getAllByText('Edit')
    fireEvent.click(editButtons[0])

    expect(mockOnEdit).toHaveBeenCalledWith(mockInvoices[0])
  })

  it('calls onDelete when delete button is clicked', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const deleteButtons = screen.getAllByText('Delete')
    fireEvent.click(deleteButtons[0])

    expect(mockOnDelete).toHaveBeenCalledWith(mockInvoices[0].id)
  })

  it('displays mobile card layout on small screens', () => {
    // Mock window.innerWidth to simulate mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 640,
    })

    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('invoices-mobile-view')).toBeInTheDocument()
    expect(screen.getAllByTestId(/invoice-card-/)).toHaveLength(2)
  })

  it('formats currency amounts correctly', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('$1,000.00')).toBeInTheDocument()
    expect(screen.getByText('$1,500.00')).toBeInTheDocument()
  })

  it('displays status badges with correct styling', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const pendingBadge = screen.getByText('pending')
    const paidBadge = screen.getByText('paid')

    expect(pendingBadge).toHaveClass('badge-warning')
    expect(paidBadge).toHaveClass('badge-success')
  })

  it('handles retry count and retrying state', () => {
    render(
      <InvoicesTable
        invoices={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
        retryCount={2}
        isRetrying={true}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
  })

  it('applies proper accessibility attributes', () => {
    render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const table = screen.getByRole('table')
    expect(table).toHaveAttribute('aria-label', 'Invoices table')

    const columnHeaders = screen.getAllByRole('columnheader')
    expect(columnHeaders).toHaveLength(6) // Invoice #, Customer, Amount, Status, Due Date, Actions

    const sortButtons = screen.getAllByRole('button', { name: /sort by/i })
    expect(sortButtons.length).toBeGreaterThan(0)
  })

  it('memoizes invoice cards for performance', () => {
    const { rerender } = render(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    // Re-render with same props
    rerender(
      <InvoicesTable
        invoices={mockInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    // Invoice cards should be memoized and not re-render unnecessarily
    expect(screen.getAllByTestId(/invoice-card-/)).toHaveLength(2)
  })

  it('handles complex sorting with customer relationships', () => {
    const complexInvoices = [
      ...mockInvoices,
      {
        id: 3,
        invoiceNumber: 'INV-003',
        customerId: 3,
        customer: {
          id: 3,
          name: 'Alice Johnson',
          email: '<EMAIL>',
          phone: '************',
          address: '789 Pine St',
          createdAt: new Date('2024-01-03'),
          updatedAt: new Date('2024-01-03')
        },
        amount: 750,
        status: 'overdue',
        dueDate: new Date('2024-01-15'),
        createdAt: new Date('2024-01-03'),
        updatedAt: new Date('2024-01-03'),
        lineItems: []
      }
    ]

    render(
      <InvoicesTable
        invoices={complexInvoices}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    // Test sorting by customer name
    const customerHeader = screen.getByText('Customer')
    fireEvent.click(customerHeader)

    const customerNames = screen.getAllByTestId(/customer-name-/)
    expect(customerNames[0]).toHaveTextContent('Alice Johnson')
    expect(customerNames[1]).toHaveTextContent('Jane Smith')
    expect(customerNames[2]).toHaveTextContent('John Doe')
  })
})