"use client"

import { memo, useCallback } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertTriangle, FileText, User, Building, Calendar } from "lucide-react"
import { Invoice, formatCurrency } from "@/lib/types"

interface DeleteConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  invoice: Invoice | null
  onConfirm: () => Promise<void>
  loading?: boolean
}

export const DeleteConfirmationDialog = memo(({
  open,
  onOpenChange,
  invoice,
  onConfirm,
  loading = false,
}: DeleteConfirmationDialogProps) => {
  if (!invoice) return null

  const handleConfirm = useCallback(async () => {
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Delete confirmation error:', error)
    }
  }, [onConfirm, onOpenChange])

  const hasPayments = invoice.payments && invoice.payments.length > 0
  const paymentsCount = invoice.payments?.length || 0
  const remainingBalance = Number(invoice.total) - Number(invoice.paid)

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent 
        className="mobile-dialog mobile-spacing"
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <AlertDialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" aria-hidden="true" />
            <AlertDialogTitle id="delete-dialog-title">Delete Invoice</AlertDialogTitle>
          </div>
          <AlertDialogDescription asChild>
            <div className="space-y-4" id="delete-dialog-description">
              <p>
                Are you sure you want to delete this invoice? This action cannot be undone.
              </p>
              
              <div className="rounded-lg border p-4 bg-muted/50">
                <h4 className="font-medium mb-2">Invoice Details:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Invoice ID:</span>
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4" />
                      <span className="font-medium">#{invoice.id}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Customer:</span>
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <span className="font-medium">{invoice.booking.customer.name}</span>
                    </div>
                  </div>
                  {invoice.booking.customer.companyName && (
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Company:</span>
                      <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4" />
                        <span className="font-medium">{invoice.booking.customer.companyName}</span>
                      </div>
                    </div>
                  )}
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Event Date:</span>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4" />
                      <span className="font-medium">
                        {invoice.booking.periods && invoice.booking.periods.length > 0 
                          ? new Date(invoice.booking.periods[0].start).toLocaleDateString()
                          : 'No periods'
                        }
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Status:</span>
                    <Badge 
                      variant={
                        invoice.status === 'PAID' ? 'default' : 
                        invoice.status === 'PARTIALLY_PAID' ? 'secondary' : 
                        'outline'
                      }
                    >
                      {invoice.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Total Amount:</span>
                    <span className="font-medium">
                      {formatCurrency(invoice.total)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Paid Amount:</span>
                    <span className="font-medium">
                      {formatCurrency(invoice.paid)}
                    </span>
                  </div>
                  {remainingBalance > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Remaining Balance:</span>
                      <span className="font-medium text-orange-600">
                        {formatCurrency(remainingBalance)}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Line Items:</span>
                    <span className="font-medium">
                      {invoice.lineItems?.length || 0} item{invoice.lineItems?.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                  {paymentsCount > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Payments:</span>
                      <span className="font-medium">
                        {paymentsCount} payment{paymentsCount !== 1 ? 's' : ''}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created:</span>
                    <span className="font-medium">
                      {new Date(invoice.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>

              {hasPayments ? (
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-destructive">
                        Cannot Delete Invoice
                      </p>
                      <p className="text-xs text-destructive/80">
                        This invoice has {paymentsCount} existing payment{paymentsCount !== 1 ? 's' : ''}. 
                        Please remove all payments before deleting the invoice.
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-destructive">
                        Warning: This action is permanent
                      </p>
                      <ul className="text-xs text-destructive/80 space-y-1">
                        <li>• The invoice will be permanently removed from the system</li>
                        <li>• All associated line items will be deleted</li>
                        <li>• This action cannot be reversed</li>
                        <li>• The booking will remain intact</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {!hasPayments && (
                <p className="text-sm text-muted-foreground">
                  Please confirm that you want to permanently delete invoice <strong>#{invoice.id}</strong> for <strong>{invoice.booking.customer.name}</strong>.
                </p>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="mobile-button-group flex flex-col sm:flex-row gap-3">
          <AlertDialogCancel 
            disabled={loading}
            className="touch-target-large focus-enhanced flex-1 sm:flex-none"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading || hasPayments}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 touch-target-large focus-enhanced flex-1 sm:flex-none"
            aria-describedby="delete-dialog-description"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />}
            {hasPayments ? "Cannot Delete" : `Delete Invoice #${invoice.id}`}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
})

DeleteConfirmationDialog.displayName = "DeleteConfirmationDialog"