"use client";

import { useState, useC<PERSON>back, useMemo } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, RefreshCw, AlertCircle, FileText, Filter } from "lucide-react";

// Import components
import { InvoicesTable } from "./components/invoices-table";
import { SearchInput } from "./components/search-input";
import { PaginationControls } from "@/components/dashboard/pagination-controls";
import { DeleteConfirmationDialog } from "./components/delete-confirmation-dialog";
import { InvoiceManagementErrorBoundary } from "./components/invoice-management-error-boundary";
import { InvoiceStatusFilter } from "@/components/dashboard/invoice-status-filter";
import { ClearFiltersButton } from "@/components/dashboard/clear-filters-button";

// Import custom hook
import { useInvoices } from "@/hooks/use-invoices";

// Import types
import { Invoice, InvoiceStatus } from "@/lib/types";

export default function InvoicesPage() {
  const router = useRouter();
  
  // Delete dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [invoiceToDelete, setInvoiceToDelete] = useState<Invoice | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  
  // Use custom hook for invoice data management
  const {
    invoices,
    loading,
    error,
    totalInvoices,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    selectedStatus,
    setSearchQuery,
    setStatusFilter,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
    clearFilters,
    deleteInvoice,
  } = useInvoices();

  // Invoice handlers
  const handleCreateInvoice = useCallback(() => {
    router.push("/dashboard/invoices/create");
  }, [router]);

  const handleEditInvoice = useCallback((invoice: Invoice) => {
    router.push(`/dashboard/invoices/${invoice.id}`);
  }, [router]);

  const handleDeleteInvoice = useCallback((invoice: Invoice) => {
    setInvoiceToDelete(invoice);
    setDeleteDialogOpen(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!invoiceToDelete) return;

    setDeleteLoading(true);
    try {
      await deleteInvoice(invoiceToDelete.id);
      setDeleteDialogOpen(false);
      setInvoiceToDelete(null);
    } catch (error) {
      // Error handling is done in the useInvoices hook
      console.error("Delete invoice error:", error);
    } finally {
      setDeleteLoading(false);
    }
  }, [invoiceToDelete, deleteInvoice]);

  const handleDeleteDialogChange = useCallback((open: boolean) => {
    setDeleteDialogOpen(open);
    if (!open) {
      setInvoiceToDelete(null);
      setDeleteLoading(false);
    }
  }, []);

  // Filter handlers
  const handleStatusChange = useCallback((status: InvoiceStatus | 'ALL') => {
    setStatusFilter(status === 'ALL' ? undefined : status);
  }, [setStatusFilter]);

  const handleClearFilters = useCallback(() => {
    clearFilters();
  }, [clearFilters]);

  // Filter state for clear button
  const filterState = useMemo(() => ({
    searchQuery,
    selectedStatus,
  }), [searchQuery, selectedStatus]);

  // Memoized values for better performance
  const totalInvoicesText = useMemo(() => 
    `${totalInvoices} total invoices`, 
    [totalInvoices]
  );

  const shouldShowPagination = useMemo(() => 
    !loading && invoices.length > 0, 
    [loading, invoices.length]
  );

  // Calculate active filters for display
  const activeFilters = useMemo(() => {
    const filters = [];
    if (searchQuery.trim()) filters.push('Search');
    if (selectedStatus) filters.push('Status');
    return filters;
  }, [searchQuery, selectedStatus]);

  return (
    <InvoiceManagementErrorBoundary onRetry={refresh}>
      <div className="h-full bg-gray-50 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Invoices</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">Manage invoices and payment tracking</p>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
            <Button
              variant="outline"
              onClick={refresh}
              disabled={loading}
              className="min-h-[44px] sm:min-h-auto"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
            <Button onClick={handleCreateInvoice} className="min-h-[44px] sm:min-h-auto">
              <Plus className="mr-2 h-4 w-4" />
              Add Invoice
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col space-y-4">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Invoice Management
                    {activeFilters.length > 0 && (
                      <span className="ml-2 text-sm font-normal text-muted-foreground">
                        ({activeFilters.length} filter{activeFilters.length > 1 ? 's' : ''})
                      </span>
                    )}
                  </CardTitle>
                  {loading ? (
                    <div className="text-sm text-muted-foreground">
                      <Skeleton className="h-4 w-48" />
                    </div>
                  ) : (
                    <CardDescription>
                      {totalInvoicesText}
                      {activeFilters.length > 0 && (
                        <span className="ml-2">
                          • Filtered by: {activeFilters.join(', ')}
                        </span>
                      )}
                    </CardDescription>
                  )}
                </div>
                <div className="w-full lg:w-80">
                  <SearchInput
                    onSearch={setSearchQuery}
                    placeholder="Search invoices by customer name or invoice ID..."
                  />
                </div>
              </div>

              {/* Filters Section */}
              <div className="space-y-3 pt-4 border-t">
                {/* Filter Row */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <InvoiceStatusFilter
                      selectedStatus={selectedStatus || 'ALL'}
                      onStatusChange={handleStatusChange}
                      disabled={loading}
                      className="w-full"
                    />
                  </div>
                </div>
                
                {/* Clear Filters Row */}
                <div className="flex justify-end">
                  <ClearFiltersButton
                    filters={filterState}
                    onClearFilters={handleClearFilters}
                    disabled={loading}
                    showFilterCount={true}
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            {error ? (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {error}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      clearError();
                      refresh();
                    }}
                    className="ml-2"
                  >
                    Try Again
                  </Button>
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <InvoicesTable
                  invoices={invoices}
                  loading={loading}
                  onEdit={handleEditInvoice}
                  onDelete={handleDeleteInvoice}
                  onRefresh={refresh}
                />
                
                {shouldShowPagination && (
                  <div className="mt-4 sm:mt-6">
                    <PaginationControls
                      currentPage={currentPage}
                      totalPages={totalPages}
                      pageSize={pageSize}
                      totalItems={totalInvoices}
                      onPageChange={setCurrentPage}
                      onPageSizeChange={setPageSize}
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Delete Confirmation Dialog */}
        <DeleteConfirmationDialog
          open={deleteDialogOpen}
          onOpenChange={handleDeleteDialogChange}
          invoice={invoiceToDelete}
          onConfirm={handleConfirmDelete}
          loading={deleteLoading}
        />
      </div>
    </InvoiceManagementErrorBoundary>
  );
}