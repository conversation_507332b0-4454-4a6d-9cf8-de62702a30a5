"use client"

import { useState, memo, useMemo, useCallback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Key,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Users,
  User as UserIcon,
} from "lucide-react"
import { User, UserRole } from "@/lib/types"
import { UsersTableSkeleton } from "./users-table-skeleton"
import { FilterErrorState } from "@/components/ui/filter-error-state"
import { OfflineStateHandler } from "@/components/ui/offline-state-handler"
import { 
  useSorting, 
  formatTableDate, 
  getSortButtonAriaLabel,
  TABLE_CLASSES,
  createTableActionHandler 
} from "@/lib/utils/table-utils"
import {
  TOUCH_TARGETS,
  FOCUS_STYLES,
  TABLE_SPACING,
  RESPONSIVE_VISIBILITY,
  TABLE_LAYOUT,
  SORT_STYLES,
  ACTION_STYLES,
  getMobileCardClasses,
  combineTableClasses
} from "@/lib/utils/table-styles"

interface UsersTableProps {
  users: User[]
  loading: boolean
  error?: Error | string | null
  onEdit: (user: User) => void
  onDelete: (userId: number) => void
  onChangePassword: (userId: number) => void
  onRefresh: () => void
  retryCount?: number
  isRetrying?: boolean
  onRetry?: () => void
}

type SortField = 'firstName' | 'lastName' | 'username' | 'role' | 'createdAt'

const getRoleBadgeVariant = (role: UserRole) => {
  switch (role) {
    case 'ADMIN':
      return 'destructive'
    case 'LOGISTICS':
      return 'default'
    case 'RECEIPTION':
      return 'secondary'
    case 'FINANCE':
      return 'default'
    case 'OFFICE':
      return 'outline'
    default:
      return 'outline'
  }
}

// Memoized role badge component for performance
const RoleBadge = memo(({ role }: { role: UserRole }) => (
  <Badge variant={getRoleBadgeVariant(role)} className="text-xs">
    {role}
  </Badge>
))
RoleBadge.displayName = "RoleBadge"

// Memoized user avatar component for performance
const UserAvatar = memo(() => (
  <div 
    className="flex items-center justify-center w-8 h-8 rounded-full bg-muted"
    aria-hidden="true"
  >
    <UserIcon className="h-4 w-4" />
  </div>
))
UserAvatar.displayName = "UserAvatar"

// Mobile card component for individual users - memoized for performance
const UserCard = memo(({ 
  user, 
  onEdit, 
  onDelete, 
  onChangePassword 
}: { 
  user: User
  onEdit: (user: User) => void
  onDelete: (userId: number) => void
  onChangePassword: (userId: number) => void
}) => {
  return (
    <Card className={combineTableClasses("mb-4", getMobileCardClasses())}>
      <CardContent className={TABLE_SPACING.mobileCardPadding}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <UserAvatar />
              <div className="flex items-center space-x-2">
                <h3 className="font-medium text-base">
                  {user.firstName} {user.lastName}
                </h3>
                {user.isSuperUser && (
                  <Badge variant="outline" className="text-xs">Super User</Badge>
                )}
              </div>
            </div>
            
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <span className="font-medium">Username:</span>
                <span>{user.username}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Role:</span>
                <RoleBadge role={user.role} />
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Created:</span>
                <span>
                  {formatTableDate(user.createdAt)}
                </span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className={combineTableClasses(TOUCH_TARGETS.iconButton, FOCUS_STYLES.enhanced)}
                aria-label={`Actions for user ${user.firstName} ${user.lastName}`}
              >
                <span className="sr-only">
                  Open actions menu for user {user.firstName} {user.lastName}
                </span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className={ACTION_STYLES.dropdownContent} role="menu">
              <DropdownMenuItem 
                onClick={() => onEdit(user)}
                className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                role="menuitem"
              >
                <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                Edit user
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onChangePassword(user.id)}
                className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                role="menuitem"
              >
                <Key className="mr-2 h-4 w-4" aria-hidden="true" />
                Change Password
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(user.id)}
                className={combineTableClasses(ACTION_STYLES.dropdownItemDestructive, FOCUS_STYLES.enhanced)}
                role="menuitem"
              >
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                Delete user
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
})
UserCard.displayName = "UserCard"

export const UsersTable = memo(({
  users,
  loading,
  error,
  onEdit,
  onDelete,
  onChangePassword,
  onRefresh,
  retryCount = 0,
  isRetrying = false,
  onRetry,
}: UsersTableProps) => {
  // Memoized role order for consistent sorting performance
  const roleOrder = useMemo(() => ({
    'ADMIN': 1,
    'LOGISTICS': 2, 
    'FINANCE': 3,
    'RECEIPTION': 4,
    'OFFICE': 5
  }), [])

  // Custom sort value extractor for users - optimized for performance
  const userSortValueExtractor = useCallback((user: User, field: keyof User) => {
    switch (field) {
      case 'firstName':
      case 'lastName':
      case 'username':
        return user[field]?.toLowerCase() || ''
      case 'role':
        return roleOrder[user.role] || 6
      case 'createdAt':
        try {
          return user.createdAt ? new Date(user.createdAt).getTime() : 0
        } catch (error) {
          return 0
        }
      default:
        return user[field]
    }
  }, [roleOrder])

  // Use shared sorting hook with performance optimization
  const { sortConfig, handleSort, sortedData: sortedUsers } = useSorting(
    users,
    'createdAt' as keyof User,
    'desc',
    userSortValueExtractor
  )

  // Memoize sorted users to prevent unnecessary re-computations
  const memoizedSortedUsers = useMemo(() => sortedUsers, [sortedUsers])

  // Custom sort icon component
  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  // Memoize handlers to prevent unnecessary re-renders
  const handleRetryAction = useMemo(() => onRetry || onRefresh, [onRetry, onRefresh])
  
  // Performance-optimized action handlers with error handling
  const handleEditUser = useCallback(
    createTableActionHandler(
      (user: User) => onEdit(user),
      (error) => console.error('Failed to edit user:', error)
    ),
    [onEdit]
  )
  
  const handleDeleteUser = useCallback(
    (userId: number) => {
      try {
        onDelete(userId);
      } catch (error) {
        console.error('Failed to delete user:', error);
      }
    },
    [onDelete]
  )
  
  const handleChangePasswordUser = useCallback(
    (userId: number) => {
      try {
        onChangePassword(userId);
      } catch (error) {
        console.error('Failed to change user password:', error);
      }
    },
    [onChangePassword]
  )

  // Performance monitoring for large datasets
  const userCount = users.length
  const isLargeDataset = userCount > 100
  
  // Log performance metrics in development
  if (process.env.NODE_ENV === 'development' && isLargeDataset) {
    console.log(`UsersTable: Rendering ${userCount} users with sorting by ${sortConfig.field}`)
  }

  if (loading) {
    return (
      <OfflineStateHandler onConnectionRestored={onRefresh}>
        <UsersTableSkeleton />
      </OfflineStateHandler>
    )
  }

  if (error) {
    return (
      <OfflineStateHandler onConnectionRestored={onRefresh}>
        <FilterErrorState
          error={error}
          filterType="user"
          onRetry={handleRetryAction}
          loading={isRetrying}
          showDetails={process.env.NODE_ENV === 'development'}
          context="users"
          className="mb-4"
        />
        
        {/* Show empty table structure with error state for context */}
        <div className="opacity-50 pointer-events-none">
          <UsersTableSkeleton />
        </div>
      </OfflineStateHandler>
    )
  }

  if (users.length === 0) {
    return (
      <>
        {/* Mobile empty state */}
        <div className={RESPONSIVE_VISIBILITY.showMobile}>
          <Card>
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center justify-center space-y-4">
                <Users className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">No users found</p>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your search or add a new user
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  onClick={onRefresh} 
                  className={combineTableClasses(TOUCH_TARGETS.button, FOCUS_STYLES.enhanced, "px-6")}
                  aria-label="Refresh users list"
                >
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop empty state */}
        <div className={combineTableClasses(RESPONSIVE_VISIBILITY.hiddenMobile, TABLE_LAYOUT.tableContainer)}>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('firstName')}
                      className={`h-auto p-2 font-semibold ${TABLE_CLASSES.focusEnhanced}`}
                      aria-label={getSortButtonAriaLabel('name', sortConfig.field, sortConfig.direction)}
                    >
                      Name
                      {getSortIcon('firstName')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('username')}
                      className={`h-auto p-2 font-semibold ${TABLE_CLASSES.focusEnhanced}`}
                      aria-label={getSortButtonAriaLabel('username', sortConfig.field, sortConfig.direction)}
                    >
                      Username
                      {getSortIcon('username')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('role')}
                      className={`h-auto p-2 font-semibold ${TABLE_CLASSES.focusEnhanced}`}
                      aria-label={getSortButtonAriaLabel('role', sortConfig.field, sortConfig.direction)}
                    >
                      Role
                      {getSortIcon('role')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px] hidden lg:table-cell">Super User</TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('createdAt')}
                      className={`h-auto p-2 font-semibold ${TABLE_CLASSES.focusEnhanced}`}
                      aria-label={getSortButtonAriaLabel('creation date', sortConfig.field, sortConfig.direction)}
                    >
                      Created
                      {getSortIcon('createdAt')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-muted-foreground">No users found</p>
                      <Button 
                        variant="outline" 
                        onClick={onRefresh} 
                        className={combineTableClasses(TOUCH_TARGETS.button, FOCUS_STYLES.enhanced, "px-6")}
                        aria-label="Refresh users list"
                      >
                        Refresh
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </>
    )
  }

  return (
    <OfflineStateHandler onConnectionRestored={onRefresh}>
      <>
        {/* Mobile card view */}
        <div className={RESPONSIVE_VISIBILITY.showMobile}>
          {/* Mobile sort controls */}
          <div className={SORT_STYLES.mobileSortContainer}>
            <div className="flex items-center space-x-2">
              <span className={SORT_STYLES.mobileSortLabel} id="sort-label">Sort by:</span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className={combineTableClasses(TOUCH_TARGETS.dropdownTrigger, FOCUS_STYLES.enhanced, SORT_STYLES.mobileSortDropdown)}
                    aria-labelledby="sort-label"
                    aria-expanded="false"
                  >
                    {sortConfig.field === 'firstName' && 'Name'}
                    {sortConfig.field === 'username' && 'Username'}
                    {sortConfig.field === 'role' && 'Role'}
                    {sortConfig.field === 'createdAt' && 'Created'}
                    {sortConfig.direction === 'asc' ? (
                      <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                    ) : (
                      <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40" role="menu">
                  <DropdownMenuItem 
                    onClick={() => handleSort('firstName')}
                    className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                    role="menuitem"
                  >
                    Name {getSortIcon('firstName')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleSort('username')}
                    className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                    role="menuitem"
                  >
                    Username {getSortIcon('username')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleSort('role')}
                    className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                    role="menuitem"
                  >
                    Role {getSortIcon('role')}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleSort('createdAt')}
                    className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                    role="menuitem"
                  >
                    Created {getSortIcon('createdAt')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className={TABLE_SPACING.mobileCardGap}>
            {memoizedSortedUsers.map((user) => (
              <UserCard
                key={user.id}
                user={user}
                onEdit={handleEditUser}
                onDelete={handleDeleteUser}
                onChangePassword={handleChangePasswordUser}
              />
            ))}
          </div>
        </div>

        {/* Desktop table view */}
        <div className={combineTableClasses(RESPONSIVE_VISIBILITY.hiddenMobile, TABLE_LAYOUT.tableContainer)}>
          <div className={TABLE_LAYOUT.tableWrapper}>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('firstName')}
                      className={`h-auto p-2 font-semibold ${TABLE_CLASSES.focusEnhanced}`}
                      aria-label={getSortButtonAriaLabel('name', sortConfig.field, sortConfig.direction)}
                    >
                      Name
                      {getSortIcon('firstName')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('username')}
                      className={`h-auto p-2 font-semibold ${TABLE_CLASSES.focusEnhanced}`}
                      aria-label={getSortButtonAriaLabel('username', sortConfig.field, sortConfig.direction)}
                    >
                      Username
                      {getSortIcon('username')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('role')}
                      className={`h-auto p-2 font-semibold ${TABLE_CLASSES.focusEnhanced}`}
                      aria-label={getSortButtonAriaLabel('role', sortConfig.field, sortConfig.direction)}
                    >
                      Role
                      {getSortIcon('role')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px] hidden lg:table-cell">Super User</TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('createdAt')}
                      className={`h-auto p-2 font-semibold ${TABLE_CLASSES.focusEnhanced}`}
                      aria-label={getSortButtonAriaLabel('creation date', sortConfig.field, sortConfig.direction)}
                    >
                      Created
                      {getSortIcon('createdAt')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {memoizedSortedUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-3">
                        <UserAvatar />
                        <span>{user.firstName} {user.lastName}</span>
                      </div>
                    </TableCell>
                    <TableCell>{user.username}</TableCell>
                    <TableCell>
                      <RoleBadge role={user.role} />
                    </TableCell>
                    <TableCell className={RESPONSIVE_VISIBILITY.hiddenTablet}>
                      {user.isSuperUser ? (
                        <Badge variant="outline">Yes</Badge>
                      ) : (
                        <span className="text-muted-foreground">No</span>
                      )}
                    </TableCell>
                    <TableCell className={combineTableClasses("text-muted-foreground", RESPONSIVE_VISIBILITY.hiddenTablet)}>
                      {formatTableDate(user.createdAt)}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            className={combineTableClasses(ACTION_STYLES.dropdownTrigger, FOCUS_STYLES.enhanced)}
                            aria-label={`Actions for user ${user.firstName} ${user.lastName}`}
                          >
                            <span className="sr-only">
                              Open actions menu for user {user.firstName} {user.lastName}
                            </span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className={ACTION_STYLES.dropdownContent} role="menu">
                          <DropdownMenuItem 
                            onClick={() => handleEditUser(user)}
                            className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                            role="menuitem"
                          >
                            <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                            Edit user
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleChangePasswordUser(user.id)}
                            className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                            role="menuitem"
                          >
                            <Key className="mr-2 h-4 w-4" aria-hidden="true" />
                            Change Password
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteUser(user.id)}
                            className={combineTableClasses(ACTION_STYLES.dropdownItemDestructive, FOCUS_STYLES.enhanced)}
                            role="menuitem"
                          >
                            <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                            Delete user
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </>
    </OfflineStateHandler>
  )
})

UsersTable.displayName = "UsersTable"