import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { UsersTable } from '../users-table'
import { User } from '@/lib/types'

// Mock the shared components
vi.mock('@/components/ui/offline-state-handler', () => ({
  OfflineStateHandler: ({ children }: { children: React.ReactNode }) => <div data-testid="offline-handler">{children}</div>
}))

vi.mock('@/components/ui/filter-error-state', () => ({
  FilterErrorState: ({ error, onRetry }: { error: any, onRetry: () => void }) => (
    <div data-testid="filter-error-state">
      <span>{typeof error === 'string' ? error : error.message}</span>
      <button onClick={onRetry}>Retry</button>
    </div>
  )
}))

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}))

const mockUsers: User[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: 2,
    name: 'Jane User',
    email: '<EMAIL>',
    role: 'user',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02')
  },
  {
    id: 3,
    name: 'Bob Manager',
    email: '<EMAIL>',
    role: 'manager',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03')
  }
]

describe('UsersTable', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnChangePassword = vi.fn()
  const mockOnRefresh = vi.fn()
  const mockOnRetry = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders users table with data', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('offline-handler')).toBeInTheDocument()
    expect(screen.getByText('John Admin')).toBeInTheDocument()
    expect(screen.getByText('Jane User')).toBeInTheDocument()
    expect(screen.getByText('Bob Manager')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('shows loading skeleton when loading', () => {
    render(
      <UsersTable
        users={[]}
        loading={true}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('users-table-skeleton')).toBeInTheDocument()
  })

  it('shows error state when error is present', () => {
    const error = 'Failed to load users'
    
    render(
      <UsersTable
        users={[]}
        loading={false}
        error={error}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
    expect(screen.getByText(error)).toBeInTheDocument()
  })

  it('calls onRetry when retry button is clicked in error state', () => {
    render(
      <UsersTable
        users={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
      />
    )

    const retryButton = screen.getByText('Retry')
    fireEvent.click(retryButton)

    expect(mockOnRetry).toHaveBeenCalledTimes(1)
  })

  it('shows empty state when no users', () => {
    render(
      <UsersTable
        users={[]}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('No users found')).toBeInTheDocument()
  })

  it('handles sorting by name', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const nameHeader = screen.getByText('Name')
    fireEvent.click(nameHeader)

    // Check that users are sorted alphabetically
    const userNames = screen.getAllByTestId(/user-name-/)
    expect(userNames[0]).toHaveTextContent('Bob Manager')
    expect(userNames[1]).toHaveTextContent('Jane User')
    expect(userNames[2]).toHaveTextContent('John Admin')
  })

  it('handles sorting by email', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const emailHeader = screen.getByText('Email')
    fireEvent.click(emailHeader)

    // Check that users are sorted by email
    const userEmails = screen.getAllByTestId(/user-email-/)
    expect(userEmails[0]).toHaveTextContent('<EMAIL>')
    expect(userEmails[1]).toHaveTextContent('<EMAIL>')
    expect(userEmails[2]).toHaveTextContent('<EMAIL>')
  })

  it('handles sorting by role', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const roleHeader = screen.getByText('Role')
    fireEvent.click(roleHeader)

    // Check that users are sorted by role
    const userRoles = screen.getAllByTestId(/user-role-/)
    expect(userRoles[0]).toHaveTextContent('admin')
    expect(userRoles[1]).toHaveTextContent('manager')
    expect(userRoles[2]).toHaveTextContent('user')
  })

  it('handles sorting by creation date', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const createdHeader = screen.getByText('Created')
    fireEvent.click(createdHeader)

    // Check that users are sorted by creation date
    const userDates = screen.getAllByTestId(/user-created-/)
    expect(userDates[0]).toHaveTextContent('Jan 1, 2024')
    expect(userDates[1]).toHaveTextContent('Jan 2, 2024')
    expect(userDates[2]).toHaveTextContent('Jan 3, 2024')
  })

  it('calls onEdit when edit button is clicked', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const editButtons = screen.getAllByText('Edit')
    fireEvent.click(editButtons[0])

    expect(mockOnEdit).toHaveBeenCalledWith(mockUsers[0])
  })

  it('calls onDelete when delete button is clicked', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const deleteButtons = screen.getAllByText('Delete')
    fireEvent.click(deleteButtons[0])

    expect(mockOnDelete).toHaveBeenCalledWith(mockUsers[0].id)
  })

  it('calls onChangePassword when change password button is clicked', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const changePasswordButtons = screen.getAllByText('Change Password')
    fireEvent.click(changePasswordButtons[0])

    expect(mockOnChangePassword).toHaveBeenCalledWith(mockUsers[0])
  })

  it('displays role badges with correct styling', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const adminBadge = screen.getByText('admin')
    const userBadge = screen.getByText('user')
    const managerBadge = screen.getByText('manager')

    expect(adminBadge).toHaveClass('badge-destructive')
    expect(userBadge).toHaveClass('badge-secondary')
    expect(managerBadge).toHaveClass('badge-default')
  })

  it('displays mobile card layout on small screens', () => {
    // Mock window.innerWidth to simulate mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 640,
    })

    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('users-mobile-view')).toBeInTheDocument()
    expect(screen.getAllByTestId(/user-card-/)).toHaveLength(3)
  })

  it('handles retry count and retrying state', () => {
    render(
      <UsersTable
        users={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
        retryCount={2}
        isRetrying={true}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
  })

  it('applies proper accessibility attributes', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const table = screen.getByRole('table')
    expect(table).toHaveAttribute('aria-label', 'Users table')

    const columnHeaders = screen.getAllByRole('columnheader')
    expect(columnHeaders).toHaveLength(5) // Name, Email, Role, Created, Actions

    const sortButtons = screen.getAllByRole('button', { name: /sort by/i })
    expect(sortButtons.length).toBeGreaterThan(0)
  })

  it('handles keyboard navigation for user actions', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    const firstEditButton = screen.getAllByText('Edit')[0]
    firstEditButton.focus()
    expect(firstEditButton).toHaveFocus()

    // Test keyboard navigation between action buttons
    fireEvent.keyDown(firstEditButton, { key: 'Tab' })
    const firstChangePasswordButton = screen.getAllByText('Change Password')[0]
    expect(firstChangePasswordButton).toHaveFocus()
  })

  it('memoizes user cards for performance', () => {
    const { rerender } = render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    // Re-render with same props
    rerender(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    // User cards should be memoized and not re-render unnecessarily
    expect(screen.getAllByTestId(/user-card-/)).toHaveLength(3)
  })

  it('handles user role badge rendering optimization', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    // Check that role badges are rendered with proper memoization
    const roleBadges = screen.getAllByTestId(/user-role-/)
    expect(roleBadges).toHaveLength(3)
    
    // Each role should have consistent styling
    roleBadges.forEach(badge => {
      expect(badge).toHaveClass('badge')
    })
  })

  it('formats creation dates consistently', () => {
    render(
      <UsersTable
        users={mockUsers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onChangePassword={mockOnChangePassword}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('Jan 1, 2024')).toBeInTheDocument()
    expect(screen.getByText('Jan 2, 2024')).toBeInTheDocument()
    expect(screen.getByText('Jan 3, 2024')).toBeInTheDocument()
  })
})