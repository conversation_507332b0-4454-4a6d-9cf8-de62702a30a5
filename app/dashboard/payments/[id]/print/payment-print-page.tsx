"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Payment } from "@/lib/types";
import { formatCurrency, PAYMENT_METHOD_LABELS, PAYMENT_STATUS_LABELS } from "@/lib/types";
import { Loader2, AlertCircle, Building2, Calendar, User, Phone, Mail, CreditCard, Hash, FileText } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

interface PaymentPrintPageProps {
  paymentId: number;
}

export function PaymentPrintPage({ paymentId }: PaymentPrintPageProps) {
  const router = useRouter();
  const [payment, setPayment] = useState<Payment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    const fetchPayment = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/payments/${paymentId}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch payment: ${response.statusText}`);
        }
        
        const result = await response.json();
        if (result.success) {
          setPayment(result.data);
        } else {
          throw new Error(result.message || 'Failed to fetch payment');
        }
      } catch (err) {
        console.error("Error fetching payment:", err);
        setError(err instanceof Error ? err.message : "Failed to load payment");
      } finally {
        setLoading(false);
      }
    };

    fetchPayment();
  }, [paymentId]);

  // Auto-print when the component mounts and payment is loaded
  useEffect(() => {
    if (payment && !loading && !error) {
      const timer = setTimeout(() => {
        window.print();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [payment, loading, error]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen print:hidden">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading payment receipt...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen print:hidden">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!payment) {
    return (
      <div className="flex items-center justify-center min-h-screen print:hidden">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Payment not found</AlertDescription>
        </Alert>
      </div>
    );
  }

  const paymentDate = payment.paidAt ? new Date(payment.paidAt) : new Date(payment.createdAt);

  return (
    <>
      {/* Print-only styles */}
      <style jsx global>{`
        @media print {
          body {
            margin: 0;
            padding: 0;
            font-size: 12pt;
            line-height: 1.4;
            color: #000;
            background: white;
          }
          
          .print\\:hidden {
            display: none !important;
          }
          
          .print\\:block {
            display: block !important;
          }
          
          .print\\:break-after-page {
            page-break-after: always;
          }
          
          .print\\:break-inside-avoid {
            page-break-inside: avoid;
          }
          
          .print-container {
            width: 100%;
            max-width: none;
            margin: 0;
            padding: 20px;
            box-shadow: none;
            border: none;
          }
          
          .print-header {
            border-bottom: 2px solid #000;
            margin-bottom: 20px;
            padding-bottom: 10px;
          }
          
          .print-section {
            margin-bottom: 15px;
            page-break-inside: avoid;
          }
          
          .print-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
          }
          
          .print-table th,
          .print-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
          }
          
          .print-table th {
            background: #f5f5f5;
            font-weight: bold;
          }
          
          .print-total {
            font-size: 14pt;
            font-weight: bold;
            text-align: right;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 2px solid #000;
          }
          
          .print-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ccc;
            font-size: 10pt;
            color: #666;
          }
        }
        
        @page {
          margin: 0.5in;
          size: A4;
        }
      `}</style>

      {/* Screen view with back button */}
      <div className="print:hidden">
        <div className="max-w-4xl mx-auto p-6">
          <div className="mb-6">
            <button
              onClick={() => router.back()}
              className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
            >
              ← Back to Invoice
            </button>
            <h1 className="text-2xl font-bold">Payment Receipt</h1>
            <p className="text-muted-foreground">
              This page will automatically print when loaded. You can also use Ctrl+P (Cmd+P on Mac) to print again.
            </p>
          </div>
        </div>
      </div>

      {/* Print content */}
      <div className="print-container max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="print-header print-section">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold mb-2">PAYMENT RECEIPT</h1>
              <div className="text-lg">
                <p><span className="font-semibold">Receipt #:</span> {payment.id.toString().padStart(6, '0')}</p>
                <p><span className="font-semibold">Date:</span> {paymentDate.toLocaleDateString()}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="mb-2">
                <Building2 className="inline h-6 w-6 mr-2" />
                <span className="text-xl font-semibold">Your Company Name</span>
              </div>
              <div className="text-sm space-y-1">
                <p>123 Business Street</p>
                <p>City, State 12345</p>
                <p>Phone: (*************</p>
                <p>Email: <EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Information */}
        <div className="print-section">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <CreditCard className="mr-2 h-5 w-5" />
            Payment Details
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h3 className="font-semibold mb-3">Payment Information</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Amount:</span>
                  <span className="text-xl font-bold">{formatCurrency(payment.amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Method:</span>
                  <span>{PAYMENT_METHOD_LABELS[payment.method]}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Status:</span>
                  <Badge variant={payment.status === 'COMPLETED' ? 'default' : payment.status === 'PENDING' ? 'secondary' : payment.status === 'FAILED' ? 'destructive' : 'outline'}>
                    {PAYMENT_STATUS_LABELS[payment.status]}
                  </Badge>
                </div>
                {payment.reference && (
                  <div className="flex justify-between">
                    <span className="font-medium">Reference:</span>
                    <span>{payment.reference}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="font-medium">Payment Date:</span>
                  <span>{paymentDate.toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-3">Related Invoice</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Invoice #:</span>
                  <span>INV-{payment.invoice.id.toString().padStart(6, '0')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Invoice Date:</span>
                  <span>{new Date(payment.invoice.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Invoice Total:</span>
                  <span>{formatCurrency(payment.invoice.total)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Total Paid:</span>
                  <span>{formatCurrency(payment.invoice.paid)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Outstanding Balance:</span>
                  <span className={`font-semibold ${Number(payment.invoice.total) - Number(payment.invoice.paid) > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {formatCurrency(Number(payment.invoice.total) - Number(payment.invoice.paid))}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Customer:</span>
                  <span>{payment.invoice.booking?.customer?.name || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Notes Section */}
        {payment.notes && (
          <div className="print-section">
            <h3 className="font-semibold mb-3 flex items-center">
              <FileText className="mr-2 h-4 w-4" />
              Notes
            </h3>
            <div className="p-4 bg-gray-50 rounded border">
              <p className="whitespace-pre-wrap">{payment.notes}</p>
            </div>
          </div>
        )}

        {/* Payment Summary */}
        <div className="print-total print-section">
          <div className="text-right">
            <div className="text-2xl font-bold">
              Total Paid: {formatCurrency(payment.amount)}
            </div>
            <div className="text-sm text-gray-600 mt-2">
              Method: {PAYMENT_METHOD_LABELS[payment.method]}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="print-footer print-section">
          <div className="text-center">
            <p className="mb-2">Thank you for your payment!</p>
            <p className="text-sm">
              This receipt was generated on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
            </p>
            <p className="text-sm mt-2">
              For questions about this payment, please contact <NAME_EMAIL> or (*************
            </p>
          </div>
        </div>
      </div>
    </>
  );
}