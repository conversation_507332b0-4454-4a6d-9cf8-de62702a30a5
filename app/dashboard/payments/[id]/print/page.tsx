import { Suspense } from "react";
import { PaymentPrintPage } from "./payment-print-page";
import { Loader2 } from "lucide-react";

interface PaymentPrintRouteProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PaymentPrintRoute({ params }: PaymentPrintRouteProps) {
  const { id } = await params;
  
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      }
    >
      <PaymentPrintPage paymentId={parseInt(id)} />
    </Suspense>
  );
}

export const metadata = {
  title: "Print Payment Receipt",
  robots: "noindex, nofollow",
};