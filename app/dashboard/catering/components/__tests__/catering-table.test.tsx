import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { CateringTable } from '../catering-table'
import { CateringOffer } from '@/lib/types'

// Mock the shared components
vi.mock('@/components/ui/offline-state-handler', () => ({
  OfflineStateHandler: ({ children }: { children: React.ReactNode }) => <div data-testid="offline-handler">{children}</div>
}))

vi.mock('@/components/ui/filter-error-state', () => ({
  FilterErrorState: ({ error, onRetry }: { error: any, onRetry: () => void }) => (
    <div data-testid="filter-error-state">
      <span>{typeof error === 'string' ? error : error.message}</span>
      <button onClick={onRetry}>Retry</button>
    </div>
  )
}))

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}))

// Mock the catering error monitoring
vi.mock('@/lib/utils/catering-error-monitoring', () => ({
  logCateringError: vi.fn(),
  trackCateringPerformance: vi.fn()
}))

const mockCateringOffers: CateringOffer[] = [
  {
    id: 1,
    name: 'Premium Lunch Package',
    description: 'Gourmet lunch for corporate events',
    pricePerPerson: 25.00,
    minimumGuests: 10,
    maximumGuests: 100,
    revenueSharePercentage: 15.0,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: 2,
    name: 'Coffee Break Special',
    description: 'Coffee and pastries for meetings',
    pricePerPerson: 8.50,
    minimumGuests: 5,
    maximumGuests: 50,
    revenueSharePercentage: 20.0,
    isActive: true,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02')
  },
  {
    id: 3,
    name: 'Dinner Banquet',
    description: 'Elegant dinner for special occasions',
    pricePerPerson: 45.00,
    minimumGuests: 20,
    maximumGuests: 200,
    revenueSharePercentage: 12.5,
    isActive: false,
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03')
  }
]

describe('CateringTable', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnToggleActive = vi.fn()
  const mockOnRefresh = vi.fn()
  const mockOnRetry = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders catering table with data', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('offline-handler')).toBeInTheDocument()
    expect(screen.getByText('Premium Lunch Package')).toBeInTheDocument()
    expect(screen.getByText('Coffee Break Special')).toBeInTheDocument()
    expect(screen.getByText('Dinner Banquet')).toBeInTheDocument()
  })

  it('shows loading skeleton when loading', () => {
    render(
      <CateringTable
        cateringOffers={[]}
        loading={true}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('catering-table-skeleton')).toBeInTheDocument()
  })

  it('shows error state when error is present', () => {
    const error = 'Failed to load catering offers'
    
    render(
      <CateringTable
        cateringOffers={[]}
        loading={false}
        error={error}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
    expect(screen.getByText(error)).toBeInTheDocument()
  })

  it('calls onRetry when retry button is clicked in error state', () => {
    render(
      <CateringTable
        cateringOffers={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
      />
    )

    const retryButton = screen.getByText('Retry')
    fireEvent.click(retryButton)

    expect(mockOnRetry).toHaveBeenCalledTimes(1)
  })

  it('shows empty state when no catering offers', () => {
    render(
      <CateringTable
        cateringOffers={[]}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('No catering offers found')).toBeInTheDocument()
  })

  it('handles sorting by name', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    const nameHeader = screen.getByText('Name')
    fireEvent.click(nameHeader)

    // Check that offers are sorted alphabetically
    const offerNames = screen.getAllByTestId(/catering-name-/)
    expect(offerNames[0]).toHaveTextContent('Coffee Break Special')
    expect(offerNames[1]).toHaveTextContent('Dinner Banquet')
    expect(offerNames[2]).toHaveTextContent('Premium Lunch Package')
  })

  it('handles sorting by price per person', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    const priceHeader = screen.getByText('Price/Person')
    fireEvent.click(priceHeader)

    // Check that offers are sorted by price
    const offerPrices = screen.getAllByTestId(/catering-price-/)
    expect(offerPrices[0]).toHaveTextContent('$8.50')
    expect(offerPrices[1]).toHaveTextContent('$25.00')
    expect(offerPrices[2]).toHaveTextContent('$45.00')
  })

  it('handles sorting by revenue share percentage', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    const revenueHeader = screen.getByText('Revenue Share')
    fireEvent.click(revenueHeader)

    // Check that offers are sorted by revenue share
    const revenueShares = screen.getAllByTestId(/catering-revenue-/)
    expect(revenueShares[0]).toHaveTextContent('12.5%')
    expect(revenueShares[1]).toHaveTextContent('15.0%')
    expect(revenueShares[2]).toHaveTextContent('20.0%')
  })

  it('calls onEdit when edit button is clicked', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    const editButtons = screen.getAllByText('Edit')
    fireEvent.click(editButtons[0])

    expect(mockOnEdit).toHaveBeenCalledWith(mockCateringOffers[0])
  })

  it('calls onDelete when delete button is clicked', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    const deleteButtons = screen.getAllByText('Delete')
    fireEvent.click(deleteButtons[0])

    expect(mockOnDelete).toHaveBeenCalledWith(mockCateringOffers[0].id)
  })

  it('calls onToggleActive when toggle button is clicked', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    const toggleButtons = screen.getAllByText(/Activate|Deactivate/)
    fireEvent.click(toggleButtons[0])

    expect(mockOnToggleActive).toHaveBeenCalledWith(mockCateringOffers[0].id, !mockCateringOffers[0].isActive)
  })

  it('displays status badges correctly', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    const activeBadges = screen.getAllByText('Active')
    const inactiveBadge = screen.getByText('Inactive')

    expect(activeBadges).toHaveLength(2)
    expect(inactiveBadge).toBeInTheDocument()

    expect(activeBadges[0]).toHaveClass('badge-success')
    expect(inactiveBadge).toHaveClass('badge-secondary')
  })

  it('formats currency and percentages correctly', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('$25.00')).toBeInTheDocument()
    expect(screen.getByText('$8.50')).toBeInTheDocument()
    expect(screen.getByText('$45.00')).toBeInTheDocument()
    expect(screen.getByText('15.0%')).toBeInTheDocument()
    expect(screen.getByText('20.0%')).toBeInTheDocument()
    expect(screen.getByText('12.5%')).toBeInTheDocument()
  })

  it('displays guest capacity ranges correctly', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('10-100 guests')).toBeInTheDocument()
    expect(screen.getByText('5-50 guests')).toBeInTheDocument()
    expect(screen.getByText('20-200 guests')).toBeInTheDocument()
  })

  it('displays mobile card layout on small screens', () => {
    // Mock window.innerWidth to simulate mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 640,
    })

    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('catering-mobile-view')).toBeInTheDocument()
    expect(screen.getAllByTestId(/catering-card-/)).toHaveLength(3)
  })

  it('handles retry count and retrying state', () => {
    render(
      <CateringTable
        cateringOffers={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
        retryCount={2}
        isRetrying={true}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
  })

  it('applies proper accessibility attributes', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    const table = screen.getByRole('table')
    expect(table).toHaveAttribute('aria-label', 'Catering offers table')

    const columnHeaders = screen.getAllByRole('columnheader')
    expect(columnHeaders).toHaveLength(7) // Name, Description, Price/Person, Capacity, Revenue Share, Status, Actions

    const sortButtons = screen.getAllByRole('button', { name: /sort by/i })
    expect(sortButtons.length).toBeGreaterThan(0)
  })

  it('memoizes catering cards for performance', () => {
    const { rerender } = render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    // Re-render with same props
    rerender(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    // Catering cards should be memoized and not re-render unnecessarily
    expect(screen.getAllByTestId(/catering-card-/)).toHaveLength(3)
  })

  it('optimizes financial calculations and currency formatting', () => {
    render(
      <CateringTable
        cateringOffers={mockCateringOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    // Check that financial calculations are performed efficiently
    const priceElements = screen.getAllByTestId(/catering-price-/)
    const revenueElements = screen.getAllByTestId(/catering-revenue-/)

    expect(priceElements).toHaveLength(3)
    expect(revenueElements).toHaveLength(3)

    // Verify currency formatting is consistent
    priceElements.forEach(element => {
      expect(element.textContent).toMatch(/^\$\d+\.\d{2}$/)
    })

    // Verify percentage formatting is consistent
    revenueElements.forEach(element => {
      expect(element.textContent).toMatch(/^\d+(\.\d+)?%$/)
    })
  })

  it('handles complex sorting with financial data', () => {
    const complexOffers = [
      ...mockCateringOffers,
      {
        id: 4,
        name: 'Budget Breakfast',
        description: 'Simple breakfast option',
        pricePerPerson: 12.75,
        minimumGuests: 15,
        maximumGuests: 75,
        revenueSharePercentage: 18.5,
        isActive: true,
        createdAt: new Date('2024-01-04'),
        updatedAt: new Date('2024-01-04')
      }
    ]

    render(
      <CateringTable
        cateringOffers={complexOffers}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    // Test sorting by price with decimal values
    const priceHeader = screen.getByText('Price/Person')
    fireEvent.click(priceHeader)

    const offerPrices = screen.getAllByTestId(/catering-price-/)
    expect(offerPrices[0]).toHaveTextContent('$8.50')
    expect(offerPrices[1]).toHaveTextContent('$12.75')
    expect(offerPrices[2]).toHaveTextContent('$25.00')
    expect(offerPrices[3]).toHaveTextContent('$45.00')
  })

  it('integrates with error monitoring system', () => {
    const { logCateringError } = require('@/lib/utils/catering-error-monitoring')
    
    render(
      <CateringTable
        cateringOffers={[]}
        loading={false}
        error="Database connection failed"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
      />
    )

    // Error monitoring should be integrated with the error state
    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
  })

  it('handles performance monitoring for large datasets', () => {
    const largeCateringSet = Array.from({ length: 50 }, (_, index) => ({
      id: index + 1,
      name: `Catering Package ${index + 1}`,
      description: `Description for package ${index + 1}`,
      pricePerPerson: 10 + (index * 2),
      minimumGuests: 5 + (index * 2),
      maximumGuests: 50 + (index * 10),
      revenueSharePercentage: 10 + (index % 15),
      isActive: index % 2 === 0,
      createdAt: new Date(`2024-01-${(index % 30) + 1}`),
      updatedAt: new Date(`2024-01-${(index % 30) + 1}`)
    }))

    const startTime = performance.now()
    
    render(
      <CateringTable
        cateringOffers={largeCateringSet}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onRefresh={mockOnRefresh}
      />
    )

    const endTime = performance.now()
    const renderTime = endTime - startTime

    // Ensure rendering completes within reasonable time
    expect(renderTime).toBeLessThan(200)
    expect(screen.getByText('Catering Package 1')).toBeInTheDocument()
  })
})