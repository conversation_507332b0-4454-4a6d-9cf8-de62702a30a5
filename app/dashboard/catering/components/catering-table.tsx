"use client"

import { useState, memo, useMemo, useCallback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  UtensilsCrossed,
  AlertCircle,
} from "lucide-react"
import { Catering, CateringWithBookingCount } from "@/lib/types"
import { format } from "date-fns"
import { formatCurrency, calculatePercentageBreakdown } from "@/lib/utils/catering"
import { CateringTableSkeleton } from "./catering-table-skeleton"
import { useCateringErrorMonitor } from "@/lib/utils/catering-error-monitoring"
import { OfflineStateHandler } from "@/components/ui/offline-state-handler"
import { FilterErrorState } from "@/components/ui/filter-error-state"

interface CateringTableProps {
  catering: (Catering | CateringWithBookingCount)[]
  loading: boolean
  error?: Error | string | null
  retryCount?: number
  isRetrying?: boolean
  onEdit: (catering: Catering) => void
  onDelete: (cateringId: number) => void
  onRefresh: () => void
  onRetry?: () => void
}

type SortField = 'offerName' | 'pricePerPerson' | 'firstPartyShare' | 'vendorShare' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

// Helper function to check if catering has booking count
const hasBookingCount = (catering: Catering | CateringWithBookingCount): catering is CateringWithBookingCount => {
  return '_count' in catering && typeof catering._count === 'object' && catering._count !== null
}

// Optimized table row component for desktop view
const CateringTableRow = memo(({
  catering,
  onEdit,
  onDelete
}: {
  catering: Catering | CateringWithBookingCount
  onEdit: (catering: Catering) => void
  onDelete: (cateringId: number) => void
}) => {
  // Memoize expensive calculations
  const bookingCount = useMemo(() => 
    hasBookingCount(catering) ? catering._count.bookings : 0, 
    [catering]
  )
  
  const hasBookings = useMemo(() => bookingCount > 0, [bookingCount])
  
  const { firstPartyPercentage, vendorPercentage } = useMemo(() => 
    calculatePercentageBreakdown(catering.firstPartyShare, catering.vendorShare),
    [catering.firstPartyShare, catering.vendorShare]
  )
  
  const formattedPrice = useMemo(() => 
    formatCurrency(catering.pricePerPerson),
    [catering.pricePerPerson]
  )
  
  const formattedFirstPartyShare = useMemo(() => 
    formatCurrency(catering.firstPartyShare),
    [catering.firstPartyShare]
  )
  
  const formattedVendorShare = useMemo(() => 
    formatCurrency(catering.vendorShare),
    [catering.vendorShare]
  )
  
  const formattedDate = useMemo(() => {
    if (!catering.createdAt) return 'N/A'
    try {
      return format(new Date(catering.createdAt), 'MMM dd, yyyy')
    } catch (error) {
      return 'Invalid date'
    }
  }, [catering.createdAt])
  
  // Memoize event handlers
  const handleEdit = useCallback(() => onEdit(catering), [onEdit, catering])
  const handleDelete = useCallback(() => onDelete(catering.id), [onDelete, catering.id])

  return (
    <TableRow>
      <TableCell className="font-medium">
        <div className="flex items-center space-x-2">
          <span>{catering.offerName}</span>
          {hasBookings && (
            <Badge variant="secondary" className="text-xs">
              {bookingCount} booking{bookingCount !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </TableCell>
      <TableCell className="font-bold text-primary">
        {formattedPrice}
      </TableCell>
      <TableCell>
        <div className="space-y-1">
          <div className="font-semibold text-green-700">{formattedFirstPartyShare}</div>
          <div className="text-xs text-muted-foreground">({firstPartyPercentage}%)</div>
        </div>
      </TableCell>
      <TableCell>
        <div className="space-y-1">
          <div className="font-semibold text-blue-700">{formattedVendorShare}</div>
          <div className="text-xs text-muted-foreground">({vendorPercentage}%)</div>
        </div>
      </TableCell>
      <TableCell className="text-muted-foreground hidden lg:table-cell">
        {formattedDate}
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              className="h-10 w-10 p-0 focus-enhanced"
              aria-label={`Actions for ${catering.offerName}`}
            >
              <span className="sr-only">Open actions menu for {catering.offerName}</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48" role="menu">
            <DropdownMenuItem 
              onClick={handleEdit}
              className="cursor-pointer focus-enhanced py-3"
              role="menuitem"
            >
              <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
              Edit {catering.offerName}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleDelete}
              className="text-destructive cursor-pointer focus-enhanced py-3"
              role="menuitem"
              disabled={hasBookings}
            >
              <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
              {hasBookings ? `Cannot delete (${bookingCount} bookings)` : `Delete ${catering.offerName}`}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  )
})

// Mobile card component for individual catering offers - memoized for performance
const CateringCard = memo(({ 
  catering, 
  onEdit, 
  onDelete 
}: { 
  catering: Catering | CateringWithBookingCount
  onEdit: (catering: Catering) => void
  onDelete: (cateringId: number) => void
}) => {
  // Memoize expensive calculations
  const bookingCount = useMemo(() => 
    hasBookingCount(catering) ? catering._count.bookings : 0, 
    [catering]
  )
  
  const hasBookings = useMemo(() => bookingCount > 0, [bookingCount])
  
  const { firstPartyPercentage, vendorPercentage } = useMemo(() => 
    calculatePercentageBreakdown(catering.firstPartyShare, catering.vendorShare),
    [catering.firstPartyShare, catering.vendorShare]
  )
  
  const formattedPrice = useMemo(() => 
    formatCurrency(catering.pricePerPerson),
    [catering.pricePerPerson]
  )
  
  const formattedFirstPartyShare = useMemo(() => 
    formatCurrency(catering.firstPartyShare),
    [catering.firstPartyShare]
  )
  
  const formattedVendorShare = useMemo(() => 
    formatCurrency(catering.vendorShare),
    [catering.vendorShare]
  )
  
  const formattedDate = useMemo(() => {
    if (!catering.createdAt) return 'N/A'
    try {
      return format(new Date(catering.createdAt), 'MMM dd, yyyy')
    } catch (error) {
      return 'Invalid date'
    }
  }, [catering.createdAt])
  
  // Memoize event handlers to prevent unnecessary re-renders
  const handleEdit = useCallback(() => onEdit(catering), [onEdit, catering])
  const handleDelete = useCallback(() => onDelete(catering.id), [onDelete, catering.id])
  
  return (
    <Card className="mb-4">
      <CardContent className="mobile-spacing">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-4">
              <div 
                className="flex items-center justify-center w-12 h-12 rounded-lg bg-primary/10 border border-primary/20"
                aria-hidden="true"
              >
                <UtensilsCrossed className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-lg truncate" id={`catering-${catering.id}`}>
                  {catering.offerName}
                </h3>
                {hasBookings && (
                  <div className="flex items-center space-x-1 mt-1">
                    <AlertCircle className="h-3 w-3 text-amber-500" />
                    <Badge variant="secondary" className="text-xs">
                      {bookingCount} booking{bookingCount !== 1 ? 's' : ''}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-3 text-sm">
              {/* Price per person - highlighted as primary metric */}
              <div className="flex items-center justify-between p-2 bg-muted/30 rounded-md">
                <span className="font-medium text-muted-foreground">Price per person:</span>
                <span className="font-bold text-lg text-primary">{formattedPrice}</span>
              </div>
              
              {/* Revenue sharing breakdown */}
              <div className="space-y-2">
                <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                  Revenue Sharing
                </div>
                <div className="space-y-2 pl-2 border-l-2 border-muted">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-muted-foreground">First party share:</span>
                    <div className="text-right">
                      <div className="font-semibold text-green-700">{formattedFirstPartyShare}</div>
                      <div className="text-xs text-muted-foreground">({firstPartyPercentage}%)</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-muted-foreground">Vendor share:</span>
                    <div className="text-right">
                      <div className="font-semibold text-blue-700">{formattedVendorShare}</div>
                      <div className="text-xs text-muted-foreground">({vendorPercentage}%)</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between pt-1 border-t">
                <span className="font-medium text-muted-foreground">Created:</span>
                <span>{formattedDate}</span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="h-11 w-11 p-0 touch-target focus-enhanced"
                aria-label={`Actions for ${catering.offerName}`}
              >
                <span className="sr-only">Open actions menu for {catering.offerName}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={handleEdit}
                className="cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                Edit {catering.offerName}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-destructive cursor-pointer touch-target focus-enhanced"
                role="menuitem"
                disabled={hasBookings}
              >
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                {hasBookings ? `Cannot delete (${bookingCount} bookings)` : `Delete ${catering.offerName}`}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
})

export const CateringTable = memo(({
  catering,
  loading,
  error,
  retryCount = 0,
  isRetrying = false,
  onEdit,
  onDelete,
  onRefresh,
  onRetry,
}: CateringTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdAt',
    direction: 'desc',
  })
  const { captureError } = useCateringErrorMonitor()

  const handleSort = useCallback((field: SortField) => {
    try {
      setSortConfig(prev => ({
        field,
        direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
      }))
    } catch (error) {
      captureError(
        error instanceof Error ? error : new Error('Sort operation failed'),
        {
          operation: 'fetch'
        },
        { sortField: field, action: 'sort', retryCount }
      )
      console.error('Error sorting catering table:', error)
    }
  }, [captureError, catering.length, retryCount])

  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  // Optimized sorting with memoized comparator functions
  const sortComparator = useMemo(() => {
    const { field, direction } = sortConfig
    const multiplier = direction === 'asc' ? 1 : -1
    
    return (a: Catering | CateringWithBookingCount, b: Catering | CateringWithBookingCount) => {
      let aValue: any = a[field]
      let bValue: any = b[field]

      // Handle date sorting with optimized parsing
      if (field === 'createdAt') {
        try {
          const aTime = aValue ? new Date(aValue).getTime() : 0
          const bTime = bValue ? new Date(bValue).getTime() : 0
          
          if (isNaN(aTime) || isNaN(bTime)) {
            return 0
          }
          
          return (aTime - bTime) * multiplier
        } catch (dateError) {
          console.error('Error parsing dates for sorting:', dateError)
          return 0
        }
      }

      // Handle numeric sorting (financial fields)
      if (field === 'pricePerPerson' || field === 'firstPartyShare' || field === 'vendorShare') {
        const aNum = Number(aValue) || 0
        const bNum = Number(bValue) || 0
        return (aNum - bNum) * multiplier
      }

      // Handle string sorting with case-insensitive comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return aValue.toLowerCase().localeCompare(bValue.toLowerCase()) * multiplier
      }

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0
      if (aValue == null) return -1 * multiplier
      if (bValue == null) return 1 * multiplier

      // Generic comparison
      if (aValue < bValue) return -1 * multiplier
      if (aValue > bValue) return 1 * multiplier
      return 0
    }
  }, [sortConfig])

  const sortedCatering = useMemo(() => {
    try {
      // Use optimized sorting with pre-computed comparator
      return [...catering].sort(sortComparator)
    } catch (error) {
      captureError(
        error instanceof Error ? error : new Error('Sorting failed'),
        {
          operation: 'fetch'
        },
        { sortConfig, cateringCount: catering.length, action: 'sort_processing', retryCount }
      )
      console.error('Error sorting catering data:', error)
      // Return unsorted data as fallback
      return catering
    }
  }, [catering, sortComparator, captureError, sortConfig, retryCount])

  // Handle error state first
  if (error && !loading) {
    return (
      <OfflineStateHandler 
        onConnectionRestored={onRefresh}
        storageKey="catering_offline_queue"
      >
        <FilterErrorState
          error={error}
          filterType="catering"
          onRetry={onRetry || onRefresh}
          loading={isRetrying}
          context="catering offers"
          showDetails={true}
        />
      </OfflineStateHandler>
    )
  }

  if (loading) {
    return (
      <OfflineStateHandler 
        onConnectionRestored={onRefresh}
        storageKey="catering_offline_queue"
      >
        <CateringTableSkeleton />
      </OfflineStateHandler>
    )
  }

  if (catering.length === 0) {
    return (
      <OfflineStateHandler 
        onConnectionRestored={onRefresh}
        storageKey="catering_offline_queue"
      >
        <>
          {/* Mobile empty state */}
          <div className="block md:hidden">
            <Card>
              <CardContent className="p-8 text-center">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <UtensilsCrossed className="h-12 w-12 text-muted-foreground" />
                  <div className="space-y-2">
                    <p className="text-lg font-medium">No catering offers found</p>
                    <p className="text-sm text-muted-foreground">
                      Try adjusting your search or add a new catering offer
                    </p>
                  </div>
                  <Button 
                    variant="outline" 
                    onClick={onRefresh} 
                    className="touch-target focus-enhanced px-6"
                    aria-label="Refresh catering offers list"
                  >
                    Refresh
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Desktop empty state */}
          <div className="hidden md:block rounded-md border">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[150px]">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort('offerName')}
                        className="h-auto p-0 font-semibold"
                      >
                        Offer Name
                        {getSortIcon('offerName')}
                      </Button>
                    </TableHead>
                    <TableHead className="min-w-[120px]">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort('pricePerPerson')}
                        className="h-auto p-0 font-semibold"
                      >
                        Price per Person
                        {getSortIcon('pricePerPerson')}
                      </Button>
                    </TableHead>
                    <TableHead className="min-w-[140px]">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort('firstPartyShare')}
                        className="h-auto p-0 font-semibold"
                      >
                        First Party Share
                        {getSortIcon('firstPartyShare')}
                      </Button>
                    </TableHead>
                    <TableHead className="min-w-[120px]">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort('vendorShare')}
                        className="h-auto p-0 font-semibold"
                      >
                        Vendor Share
                        {getSortIcon('vendorShare')}
                      </Button>
                    </TableHead>
                    <TableHead className="min-w-[120px] hidden lg:table-cell">
                      <Button
                        variant="ghost"
                        onClick={() => handleSort('createdAt')}
                        className="h-auto p-0 font-semibold"
                      >
                        Created
                        {getSortIcon('createdAt')}
                      </Button>
                    </TableHead>
                    <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center space-y-2">
                        <p className="text-muted-foreground">No catering offers found</p>
                        <Button 
                          variant="outline" 
                          onClick={onRefresh} 
                          className="touch-target focus-enhanced px-6"
                          aria-label="Refresh catering offers list"
                        >
                          Refresh
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </>
      </OfflineStateHandler>
    )
  }

  return (
    <OfflineStateHandler 
      onConnectionRestored={onRefresh}
      storageKey="catering_offline_queue"
    >
      <>
        {/* Mobile card view */}
        <div className="block md:hidden">
        {/* Mobile sort controls */}
        <div className="flex items-center justify-between mb-4 p-4 bg-muted/50 rounded-lg mobile-spacing">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium" id="sort-label">Sort by:</span>
            <Badge variant="outline" className="text-xs">
              {catering.length} offer{catering.length !== 1 ? 's' : ''}
            </Badge>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="touch-target focus-enhanced min-w-[140px]"
                aria-labelledby="sort-label"
                aria-expanded="false"
              >
                {sortConfig.field === 'offerName' && 'Offer Name'}
                {sortConfig.field === 'pricePerPerson' && 'Price per Person'}
                {sortConfig.field === 'firstPartyShare' && 'First Party Share'}
                {sortConfig.field === 'vendorShare' && 'Vendor Share'}
                {sortConfig.field === 'createdAt' && 'Created'}
                {sortConfig.direction === 'asc' ? (
                  <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                ) : (
                  <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48" role="menu">
              <DropdownMenuItem 
                onClick={() => handleSort('offerName')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Offer Name {getSortIcon('offerName')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('pricePerPerson')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Price per Person {getSortIcon('pricePerPerson')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('firstPartyShare')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                First Party Share {getSortIcon('firstPartyShare')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('vendorShare')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Vendor Share {getSortIcon('vendorShare')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('createdAt')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Created {getSortIcon('createdAt')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-4">
          {sortedCatering.map((cateringItem) => (
            <CateringCard
              key={cateringItem.id}
              catering={cateringItem}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      </div>

      {/* Desktop table view */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[150px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('offerName')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by offer name ${sortConfig.field === 'offerName' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Offer Name
                    {getSortIcon('offerName')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('pricePerPerson')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by price per person ${sortConfig.field === 'pricePerPerson' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Price per Person
                    {getSortIcon('pricePerPerson')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[140px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('firstPartyShare')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by first party share ${sortConfig.field === 'firstPartyShare' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    First Party Share
                    {getSortIcon('firstPartyShare')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('vendorShare')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by vendor share ${sortConfig.field === 'vendorShare' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Vendor Share
                    {getSortIcon('vendorShare')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('createdAt')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by creation date ${sortConfig.field === 'createdAt' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Created
                    {getSortIcon('createdAt')}
                  </Button>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedCatering.map((cateringItem) => {
                return <CateringTableRow key={cateringItem.id} catering={cateringItem} onEdit={onEdit} onDelete={onDelete} />
              })}
            </TableBody>
          </Table>
        </div>
      </div>
      </>
    </OfflineStateHandler>
  )
})

CateringTableRow.displayName = "CateringTableRow"
CateringCard.displayName = "CateringCard"
CateringTable.displayName = "CateringTable"