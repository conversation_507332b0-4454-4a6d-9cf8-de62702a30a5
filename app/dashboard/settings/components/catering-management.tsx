"use client";

import { useState, use<PERSON><PERSON>back, use<PERSON>em<PERSON>, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Plus, RefreshCw, AlertCircle, ChefHat, Wifi, WifiOff } from "lucide-react";

// Import components from the original catering page
import { CateringTable } from "../../catering/components/catering-table";
import { CateringFormDialog } from "../../catering/components/catering-form-dialog";
import { DeleteConfirmationDialog } from "../../catering/components/delete-confirmation-dialog";
import { SearchInput } from "@/components/dashboard/search-input";
import { PaginationControls } from "@/components/dashboard/pagination-controls";
import { CateringManagementErrorBoundary } from "../../catering/components/catering-management-error-boundary";
import { CateringLoadingStates, InlineLoadingIndicator } from "../../catering/components/catering-loading-states";

// Import custom hook
import { useCatering } from "@/hooks/use-catering";

// Import types
import { Catering, CateringFormData } from "@/lib/types";

// Import error monitoring
import { useCateringErrorMonitor } from "@/lib/utils/catering-error-monitoring";

interface DialogState {
  cateringForm: {
    open: boolean;
    catering: Catering | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    catering: Catering | null;
    loading: boolean;
    bookingCount?: number;
  };
}

export default function CateringManagement() {
  // Network status tracking - initialize as true to avoid hydration mismatch
  const [isOnline, setIsOnline] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);
  const { captureError, getStats } = useCateringErrorMonitor();

  useEffect(() => {
    // Set the actual online status after component mounts
    setHasMounted(true);
    setIsOnline(navigator.onLine);

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Use custom hook for catering data management
  const {
    catering,
    loading,
    error,
    totalCatering,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    createCatering,
    updateCatering,
    deleteCatering,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  } = useCatering();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    cateringForm: {
      open: false,
      catering: null,
      loading: false,
    },
    deleteConfirmation: {
      open: false,
      catering: null,
      loading: false,
    },
  });

  // Catering form handlers
  const handleCreateCatering = useCallback(() => {
    setDialogs(prev => ({
      ...prev,
      cateringForm: { open: true, catering: null, loading: false },
    }));
  }, []);

  const handleEditCatering = useCallback((catering: Catering) => {
    setDialogs(prev => ({
      ...prev,
      cateringForm: { open: true, catering, loading: false },
    }));
  }, []);

  const handleCateringFormSubmit = useCallback(async (data: CateringFormData) => {
    const isEdit = !!dialogs.cateringForm.catering;
    
    setDialogs(prev => ({
      ...prev,
      cateringForm: { ...prev.cateringForm, loading: true },
    }));

    try {
      if (isEdit) {
        await updateCatering(dialogs.cateringForm.catering!.id, data);
      } else {
        await createCatering(data);
      }

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        cateringForm: { open: false, catering: null, loading: false },
      }));
    } catch (error) {
      // Enhanced error capture with context
      captureError(
        error instanceof Error ? error : new Error('Form submission failed'),
        {
          operation: isEdit ? 'update' : 'create',
          cateringId: isEdit ? dialogs.cateringForm.catering?.id : undefined,
          cateringName: data.offerName,
          formData: data
        },
        {
          dialogState: 'form_submission',
          isEdit,
          hasNetworkConnection: isOnline
        }
      );
      
      console.error("Error submitting catering form:", error);
      
      // Keep dialog open on error so user can retry
      setDialogs(prev => ({
        ...prev,
        cateringForm: { ...prev.cateringForm, loading: false },
      }));
      
      // Re-throw to let form handle the error display
      throw error;
    }
  }, [dialogs.cateringForm.catering, createCatering, updateCatering, captureError, isOnline]);

  // Delete catering handlers
  const handleDeleteCatering = useCallback((cateringId: number) => {
    const cateringOffer = catering.find(c => c.id === cateringId);
    if (cateringOffer) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: true, catering: cateringOffer, loading: false },
      }));
    }
  }, [catering]);

  const handleDeleteConfirm = useCallback(async () => {
    const { catering } = dialogs.deleteConfirmation;
    if (!catering) return;

    setDialogs(prev => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteCatering(catering.id);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, catering: null, loading: false },
      }));
    } catch (error) {
      // Enhanced error capture with context
      captureError(
        error instanceof Error ? error : new Error('Delete operation failed'),
        {
          operation: 'delete',
          cateringId: catering.id,
          cateringName: catering.offerName
        },
        {
          dialogState: 'delete_confirmation',
          hasNetworkConnection: isOnline
        }
      );
      
      console.error("Error deleting catering:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.catering, deleteCatering, captureError, isOnline]);

  // Dialog close handlers
  const handleCateringFormClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        cateringForm: { open: false, catering: null, loading: false },
      }));
    }
  }, []);

  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, catering: null, loading: false },
      }));
    }
  }, []);

  // Memoized values for better performance
  const totalCateringText = useMemo(() => 
    `${totalCatering} total catering offers`, 
    [totalCatering]
  );

  const hasFilters = useMemo(() => 
    searchQuery.length > 0, 
    [searchQuery]
  );

  const clearFilters = useCallback(() => {
    setSearchQuery("");
  }, [setSearchQuery]);

  if (loading && catering.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-16 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <CateringManagementErrorBoundary>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <ChefHat className="h-6 w-6 text-orange-600" />
              <h2 className="text-2xl font-bold text-gray-900">Catering</h2>
            </div>
            <p className="text-gray-600">
              Manage catering services and food offerings for events
            </p>
          </div>
          <Button 
            onClick={handleCreateCatering} 
            className="bg-orange-600 hover:bg-orange-700"
            disabled={loading}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Catering
          </Button>
        </div>

        {/* Network Status Indicator */}
        {hasMounted && !isOnline && (
          <Alert variant="destructive">
            <WifiOff className="h-4 w-4" />
            <AlertDescription>
              You are currently offline. Some features may not be available.
            </AlertDescription>
          </Alert>
        )}

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex justify-between items-center">
              <span>{error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={clearError}
                className="ml-2"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{totalCateringText}</span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refresh}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-4">
              <SearchInput
                onSearch={setSearchQuery}
                placeholder="Search catering..."
                className="max-w-sm"
              />
              
              {hasFilters && (
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  disabled={loading}
                >
                  Clear Filters
                </Button>
              )}
            </div>

            {/* Catering Table */}
            <CateringTable
              catering={catering}
              loading={loading}
              onEdit={handleEditCatering}
              onDelete={handleDeleteCatering}
              onRefresh={refresh}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-6 flex justify-center">
                <PaginationControls
                  currentPage={currentPage}
                  totalPages={totalPages}
                  pageSize={pageSize}
                  totalItems={totalCatering}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setPageSize}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Catering Form Dialog */}
        <CateringFormDialog
          open={dialogs.cateringForm.open}
          onOpenChange={handleCateringFormClose}
          catering={dialogs.cateringForm.catering}
          onSubmit={handleCateringFormSubmit}
          loading={dialogs.cateringForm.loading}
        />

        {/* Delete Confirmation Dialog */}
        <DeleteConfirmationDialog
          open={dialogs.deleteConfirmation.open}
          onOpenChange={handleDeleteConfirmationClose}
          catering={dialogs.deleteConfirmation.catering}
          onConfirm={handleDeleteConfirm}
          loading={dialogs.deleteConfirmation.loading}
        />
      </div>
    </CateringManagementErrorBoundary>
  );
}