"use client";

import { useState, use<PERSON><PERSON>back, use<PERSON>em<PERSON>, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Plus, RefreshCw, AlertCircle, Wifi, WifiOff } from "lucide-react";

// Import components from the original amenities page
import { AmenitiesTable } from "../../amenities/components/amenities-table";
import { AmenityFormDialog } from "../../amenities/components/amenity-form-dialog";
import { DeleteConfirmationDialog } from "../../amenities/components/delete-confirmation-dialog";
import { SearchInput } from "@/components/dashboard/search-input";
import { PaginationControls } from "@/components/dashboard/pagination-controls";
import { AmenityManagementErrorBoundary } from "../../amenities/components/amenity-management-error-boundary";

// Import custom hook
import { useAmenities } from "@/hooks/use-amenities";

// Import types
import { Amenity, AmenityFormData } from "@/lib/types";

interface DialogState {
  amenityForm: {
    open: boolean;
    amenity: Amenity | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    amenity: Amenity | null;
    loading: boolean;
  };
}

export default function AmenitiesManagement() {
  // Network status tracking - initialize as true to avoid hydration mismatch
  const [isOnline, setIsOnline] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    // Set the actual online status after component mounts
    setHasMounted(true);
    setIsOnline(navigator.onLine);

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Use custom hook for amenity data management
  const {
    amenities,
    loading,
    error,
    totalAmenities,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    createAmenity,
    updateAmenity,
    deleteAmenity,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  } = useAmenities();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    amenityForm: {
      open: false,
      amenity: null,
      loading: false,
    },
    deleteConfirmation: {
      open: false,
      amenity: null,
      loading: false,
    },
  });

  // Amenity form handlers
  const handleCreateAmenity = useCallback(() => {
    setDialogs(prev => ({
      ...prev,
      amenityForm: { open: true, amenity: null, loading: false },
    }));
  }, []);

  const handleEditAmenity = useCallback((amenity: Amenity) => {
    setDialogs(prev => ({
      ...prev,
      amenityForm: { open: true, amenity, loading: false },
    }));
  }, []);

  const handleAmenityFormSubmit = useCallback(async (data: AmenityFormData) => {
    const isEdit = !!dialogs.amenityForm.amenity;
    
    setDialogs(prev => ({
      ...prev,
      amenityForm: { ...prev.amenityForm, loading: true },
    }));

    try {
      if (isEdit) {
        await updateAmenity(dialogs.amenityForm.amenity!.id, data);
      } else {
        await createAmenity(data);
      }

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        amenityForm: { open: false, amenity: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error submitting amenity form:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        amenityForm: { ...prev.amenityForm, loading: false },
      }));
    }
  }, [dialogs.amenityForm.amenity, createAmenity, updateAmenity]);

  // Delete amenity handlers
  const handleDeleteAmenity = useCallback((amenityId: number) => {
    const amenity = amenities.find(a => a.id === amenityId);
    if (amenity) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: true, amenity, loading: false },
      }));
    }
  }, [amenities]);

  const handleDeleteConfirm = useCallback(async () => {
    const { amenity } = dialogs.deleteConfirmation;
    if (!amenity) return;

    setDialogs(prev => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteAmenity(amenity.id);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, amenity: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error deleting amenity:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.amenity, deleteAmenity]);

  // Dialog close handlers
  const handleAmenityFormClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        amenityForm: { open: false, amenity: null, loading: false },
      }));
    }
  }, []);

  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, amenity: null, loading: false },
      }));
    }
  }, []);

  // Memoized values for better performance
  const totalAmenitiesText = useMemo(() => 
    `${totalAmenities} total amenities`, 
    [totalAmenities]
  );

  const hasFilters = useMemo(() => 
    searchQuery.length > 0, 
    [searchQuery]
  );

  const clearFilters = useCallback(() => {
    setSearchQuery("");
  }, [setSearchQuery]);

  if (loading && amenities.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-16 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <AmenityManagementErrorBoundary>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Wifi className="h-6 w-6 text-purple-600" />
              <h2 className="text-2xl font-bold text-gray-900">Amenities</h2>
            </div>
            <p className="text-gray-600">
              Manage available amenities and features for resources
            </p>
          </div>
          <Button 
            onClick={handleCreateAmenity} 
            className="bg-purple-600 hover:bg-purple-700"
            disabled={loading}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Amenity
          </Button>
        </div>

        {/* Network Status Indicator */}
        {hasMounted && !isOnline && (
          <Alert variant="destructive">
            <WifiOff className="h-4 w-4" />
            <AlertDescription>
              You are currently offline. Some features may not be available.
            </AlertDescription>
          </Alert>
        )}

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex justify-between items-center">
              <span>{error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={clearError}
                className="ml-2"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{totalAmenitiesText}</span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refresh}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-4">
              <SearchInput
                onSearch={setSearchQuery}
                placeholder="Search amenities..."
                className="max-w-sm"
              />
              
              {hasFilters && (
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  disabled={loading}
                >
                  Clear Filters
                </Button>
              )}
            </div>

            {/* Amenities Table */}
            <AmenitiesTable
              amenities={amenities}
              loading={loading}
              onEdit={handleEditAmenity}
              onDelete={handleDeleteAmenity}
              onRefresh={refresh}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-6 flex justify-center">
                <PaginationControls
                  currentPage={currentPage}
                  totalPages={totalPages}
                  pageSize={pageSize}
                  totalItems={totalAmenities}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setPageSize}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Amenity Form Dialog */}
        <AmenityFormDialog
          open={dialogs.amenityForm.open}
          onOpenChange={handleAmenityFormClose}
          amenity={dialogs.amenityForm.amenity}
          onSubmit={handleAmenityFormSubmit}
          loading={dialogs.amenityForm.loading}
        />

        {/* Delete Confirmation Dialog */}
        <DeleteConfirmationDialog
          open={dialogs.deleteConfirmation.open}
          onOpenChange={handleDeleteConfirmationClose}
          amenity={dialogs.deleteConfirmation.amenity}
          onConfirm={handleDeleteConfirm}
          loading={dialogs.deleteConfirmation.loading}
        />
      </div>
    </AmenityManagementErrorBoundary>
  );
}