"use client";

import { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Plus, RefreshCw, AlertCircle, Users } from "lucide-react";

// Import components from the original users page
import { UsersTable } from "../../users/components/users-table";
import { UserFormDialog } from "../../users/components/user-form-dialog";
import { PasswordChangeDialog } from "../../users/components/password-change-dialog";
import { DeleteConfirmationDialog } from "../../users/components/delete-confirmation-dialog";
import { SearchInput } from "../../users/components/search-input";
import { PaginationControls } from "../../users/components/pagination-controls";
import { UserManagementErrorBoundary } from "../../users/components/user-management-error-boundary";

// Import custom hook
import { useUsers } from "@/hooks/use-users";

// Import types
import { User, UserFormData, PasswordChangeData } from "@/lib/types";

interface DialogState {
  userForm: {
    open: boolean;
    user: User | null;
    loading: boolean;
  };
  passwordChange: {
    open: boolean;
    userId: number | null;
    username: string;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    user: User | null;
    loading: boolean;
  };
}

export default function UsersManagement() {
  // Use custom hook for user data management
  const {
    users,
    loading,
    error,
    totalUsers,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    createUser,
    updateUser,
    deleteUser,
    changePassword,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  } = useUsers();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    userForm: {
      open: false,
      user: null,
      loading: false,
    },
    passwordChange: {
      open: false,
      userId: null,
      username: "",
      loading: false,
    },
    deleteConfirmation: {
      open: false,
      user: null,
      loading: false,
    },
  });

  // User form handlers
  const handleCreateUser = useCallback(() => {
    setDialogs(prev => ({
      ...prev,
      userForm: { open: true, user: null, loading: false },
    }));
  }, []);

  const handleEditUser = useCallback((user: User) => {
    setDialogs(prev => ({
      ...prev,
      userForm: { open: true, user, loading: false },
    }));
  }, []);

  const handleUserFormSubmit = useCallback(async (data: UserFormData) => {
    const isEdit = !!dialogs.userForm.user;
    
    setDialogs(prev => ({
      ...prev,
      userForm: { ...prev.userForm, loading: true },
    }));

    try {
      if (isEdit) {
        await updateUser(dialogs.userForm.user!.id, data);
      } else {
        await createUser(data);
      }

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        userForm: { open: false, user: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error submitting user form:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        userForm: { ...prev.userForm, loading: false },
      }));
    }
  }, [dialogs.userForm.user, createUser, updateUser]);

  // Password change handlers
  const handleChangePassword = useCallback((userId: number) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setDialogs(prev => ({
        ...prev,
        passwordChange: {
          open: true,
          userId,
          username: user.username,
          loading: false,
        },
      }));
    }
  }, [users]);

  const handlePasswordChangeSubmit = useCallback(async (data: PasswordChangeData) => {
    const { userId } = dialogs.passwordChange;
    if (!userId) return;

    setDialogs(prev => ({
      ...prev,
      passwordChange: { ...prev.passwordChange, loading: true },
    }));

    try {
      await changePassword(userId, data);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        passwordChange: {
          open: false,
          userId: null,
          username: "",
          loading: false,
        },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error changing password:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        passwordChange: { ...prev.passwordChange, loading: false },
      }));
    }
  }, [dialogs.passwordChange.userId, changePassword]);

  // Delete user handlers
  const handleDeleteUser = useCallback((userId: number) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: true, user, loading: false },
      }));
    }
  }, [users]);

  const handleDeleteConfirm = useCallback(async () => {
    const { user } = dialogs.deleteConfirmation;
    if (!user) return;

    setDialogs(prev => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteUser(user.id);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, user: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error deleting user:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.user, deleteUser]);

  // Dialog close handlers
  const handleUserFormClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        userForm: { open: false, user: null, loading: false },
      }));
    }
  }, []);

  const handlePasswordChangeClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        passwordChange: {
          open: false,
          userId: null,
          username: "",
          loading: false,
        },
      }));
    }
  }, []);

  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, user: null, loading: false },
      }));
    }
  }, []);

  // Memoized values for better performance
  const totalUsersText = useMemo(() => 
    `${totalUsers} total users`, 
    [totalUsers]
  );

  const hasFilters = useMemo(() => 
    searchQuery.length > 0, 
    [searchQuery]
  );

  const clearFilters = useCallback(() => {
    setSearchQuery("");
  }, [setSearchQuery]);

  if (loading && users.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-16 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <UserManagementErrorBoundary>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Users className="h-6 w-6 text-blue-600" />
              <h2 className="text-2xl font-bold text-gray-900">Users</h2>
            </div>
            <p className="text-gray-600">
              Manage user accounts and permissions for the system
            </p>
          </div>
          <Button 
            onClick={handleCreateUser} 
            className="bg-blue-600 hover:bg-blue-700"
            disabled={loading}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex justify-between items-center">
              <span>{error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={clearError}
                className="ml-2"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{totalUsersText}</span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refresh}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-4">
              <SearchInput
                onSearch={setSearchQuery}
                placeholder="Search users..."
                className="max-w-sm"
              />
              
              {hasFilters && (
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  disabled={loading}
                >
                  Clear Filters
                </Button>
              )}
            </div>

            {/* Users Table */}
            <UsersTable
              users={users}
              loading={loading}
              onEdit={handleEditUser}
              onDelete={handleDeleteUser}
              onChangePassword={handleChangePassword}
              onRefresh={refresh}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-6 flex justify-center">
                <PaginationControls
                  currentPage={currentPage}
                  totalPages={totalPages}
                  pageSize={pageSize}
                  totalItems={totalUsers}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setPageSize}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* User Form Dialog */}
        <UserFormDialog
          open={dialogs.userForm.open}
          onOpenChange={handleUserFormClose}
          user={dialogs.userForm.user}
          onSubmit={handleUserFormSubmit}
          loading={dialogs.userForm.loading}
        />

        {/* Password Change Dialog */}
        <PasswordChangeDialog
          open={dialogs.passwordChange.open}
          onOpenChange={handlePasswordChangeClose}
          userId={dialogs.passwordChange.userId || 0}
          username={dialogs.passwordChange.username}
          onSubmit={handlePasswordChangeSubmit}
          loading={dialogs.passwordChange.loading}
        />

        {/* Delete Confirmation Dialog */}
        <DeleteConfirmationDialog
          open={dialogs.deleteConfirmation.open}
          onOpenChange={handleDeleteConfirmationClose}
          user={dialogs.deleteConfirmation.user}
          onConfirm={handleDeleteConfirm}
          loading={dialogs.deleteConfirmation.loading}
        />
      </div>
    </UserManagementErrorBoundary>
  );
}