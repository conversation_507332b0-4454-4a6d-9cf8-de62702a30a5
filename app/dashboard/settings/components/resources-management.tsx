"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertCircle, Plus, RefreshCw, Building2 } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";

// Import components from the original resources page
import { ClearFiltersButton } from "@/components/dashboard/clear-filters-button";
import { PaginationControls } from "@/components/dashboard/pagination-controls";
import { ResourceTypeFilter } from "@/components/dashboard/resource-type-filter";
import { SearchInput } from "@/components/dashboard/search-input";
import { DeleteConfirmationDialog } from "../../resources/components/delete-confirmation-dialog";
import { ResourceFormDialog } from "../../resources/components/resource-form-dialog";
import { ResourceManagementErrorBoundary } from "../../resources/components/resource-management-error-boundary";
import { ResourcesTable } from "../../resources/components/resources-table";

// Import custom hook
import { useResources } from "@/hooks/use-resources";

// Import types
import {
  Amenity,
  Resource,
  ResourceFormData,
  ResourceType
} from "@/lib/types";

interface DialogState {
  resourceForm: {
    open: boolean;
    resource: Resource | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    resource: Resource | null;
    loading: boolean;
  };
}

export default function ResourcesManagement() {
  const [availableAmenities, setAvailableAmenities] = useState<Amenity[]>([]);
  const [amenitiesLoading, setAmenitiesLoading] = useState(true);

  // Fetch available amenities for the form
  useEffect(() => {
    const fetchAmenities = async () => {
      try {
        setAmenitiesLoading(true);
        const response = await fetch("/api/amenities?limit=100"); // Get all amenities
        const result = await response.json();

        if (result.success && result.data) {
          setAvailableAmenities(result.data.data);
        }
      } catch (error) {
        console.error("Error fetching amenities:", error);
      } finally {
        setAmenitiesLoading(false);
      }
    };

    fetchAmenities();
  }, []);

  // Use custom hook for resource data management
  const {
    resources,
    loading,
    error,
    totalResources,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    selectedType,
    createResource,
    updateResource,
    deleteResource,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    setSelectedType,
    refresh,
    clearError,
  } = useResources();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    resourceForm: {
      open: false,
      resource: null,
      loading: false,
    },
    deleteConfirmation: {
      open: false,
      resource: null,
      loading: false,
    },
  });

  // Resource form handlers
  const handleCreateResource = useCallback(() => {
    setDialogs((prev) => ({
      ...prev,
      resourceForm: { open: true, resource: null, loading: false },
    }));
  }, []);

  const handleEditResource = useCallback((resource: Resource) => {
    setDialogs((prev) => ({
      ...prev,
      resourceForm: { open: true, resource, loading: false },
    }));
  }, []);

  const handleResourceFormSubmit = useCallback(
    async (data: ResourceFormData) => {
      const isEdit = !!dialogs.resourceForm.resource;

      setDialogs((prev) => ({
        ...prev,
        resourceForm: { ...prev.resourceForm, loading: true },
      }));

      try {
        if (isEdit) {
          await updateResource(dialogs.resourceForm.resource!.id, data);
        } else {
          await createResource(data);
        }

        // Close dialog on success
        setDialogs((prev) => ({
          ...prev,
          resourceForm: { open: false, resource: null, loading: false },
        }));
      } catch (error) {
        // Error handling is done in the hook
        console.error("Error submitting resource form:", error);
      } finally {
        setDialogs((prev) => ({
          ...prev,
          resourceForm: { ...prev.resourceForm, loading: false },
        }));
      }
    },
    [dialogs.resourceForm.resource, createResource, updateResource]
  );

  // Delete resource handlers
  const handleDeleteResource = useCallback(
    (resourceId: number) => {
      const resource = resources.find((r) => r.id === resourceId);
      if (resource) {
        setDialogs((prev) => ({
          ...prev,
          deleteConfirmation: { open: true, resource, loading: false },
        }));
      }
    },
    [resources]
  );

  const handleDeleteConfirm = useCallback(async () => {
    const { resource } = dialogs.deleteConfirmation;
    if (!resource) return;

    setDialogs((prev) => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteResource(resource.id);

      // Close dialog on success
      setDialogs((prev) => ({
        ...prev,
        deleteConfirmation: { open: false, resource: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error deleting resource:", error);
    } finally {
      setDialogs((prev) => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.resource, deleteResource]);

  // Dialog close handlers
  const handleResourceFormClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs((prev) => ({
        ...prev,
        resourceForm: { open: false, resource: null, loading: false },
      }));
    }
  }, []);

  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs((prev) => ({
        ...prev,
        deleteConfirmation: { open: false, resource: null, loading: false },
      }));
    }
  }, []);

  // Memoized values for better performance
  const totalResourcesText = useMemo(
    () => `${totalResources} total resources`,
    [totalResources]
  );

  const hasFilters = useMemo(
    () => searchQuery.length > 0 || selectedType !== "ALL",
    [searchQuery, selectedType]
  );

  const clearFilters = useCallback(() => {
    setSearchQuery("");
    setSelectedType("ALL");
  }, [setSearchQuery, setSelectedType]);

  if (loading && resources.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <div className="h-8 w-48 mb-2 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 w-96 bg-gray-200 rounded animate-pulse" />
          </div>
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="h-16 w-full bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <ResourceManagementErrorBoundary>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Building2 className="h-6 w-6 text-green-600" />
              <h2 className="text-2xl font-bold text-gray-900">Resources</h2>
            </div>
            <p className="text-gray-600">
              Manage bookable resources like rooms, equipment, and facilities
            </p>
          </div>
          <Button
            onClick={handleCreateResource}
            className="bg-green-600 hover:bg-green-700"
            disabled={loading}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Resource
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex justify-between items-center">
              <span>{error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={clearError}
                className="ml-2"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{totalResourcesText}</span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refresh}
                  disabled={loading}
                >
                  <RefreshCw
                    className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
                  />
                  Refresh
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-4">
              <SearchInput
                onSearch={setSearchQuery}
                placeholder="Search resources..."
                className="max-w-sm"
              />

              <ResourceTypeFilter
                selectedType={selectedType}
                onTypeChange={setSelectedType}
                disabled={loading}
              />

              {hasFilters && (
                <ClearFiltersButton
                  filters={{ searchQuery, selectedType }}
                  onClearFilters={clearFilters}
                  disabled={loading}
                />
              )}
            </div>

            {/* Resources Table */}
            <ResourcesTable
              resources={resources}
              loading={loading}
              onEdit={handleEditResource}
              onDelete={handleDeleteResource}
              onRefresh={refresh}
              onTypeFilter={setSelectedType}
              selectedType={selectedType}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-6 flex justify-center">
                <PaginationControls
                  currentPage={currentPage}
                  totalPages={totalPages}
                  pageSize={pageSize}
                  totalItems={totalResources}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setPageSize}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Resource Form Dialog */}
        <ResourceFormDialog
          open={dialogs.resourceForm.open}
          onOpenChange={handleResourceFormClose}
          resource={dialogs.resourceForm.resource}
          availableAmenities={availableAmenities}
          onSubmit={handleResourceFormSubmit}
          loading={dialogs.resourceForm.loading}
        />

        {/* Delete Confirmation Dialog */}
        <DeleteConfirmationDialog
          open={dialogs.deleteConfirmation.open}
          onOpenChange={handleDeleteConfirmationClose}
          resource={dialogs.deleteConfirmation.resource}
          onConfirm={handleDeleteConfirm}
          loading={dialogs.deleteConfirmation.loading}
        />
      </div>
    </ResourceManagementErrorBoundary>
  );
}