"use client";

import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Upload, Image as ImageIcon, CheckCircle2, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { organizationSettingsSchema, OrganizationSettingsInput } from "@/lib/validations/settings";

type SettingsResponse = {
  success: boolean;
  data?: any;
  error?: string;
};

export default function OrganizationSettings() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [serverError, setServerError] = useState<string>("");

  const form = useForm<OrganizationSettingsInput>({
    resolver: zodResolver(organizationSettingsSchema),
    mode: "onChange",
    defaultValues: {
      organizationName: "",
      address: "",
      email: "",
      phoneNumber: "",
      website: "",
      linkedin: "",
      facebook: "",
      instagram: "",
      paymentTerms: "",
      paymentMethods: "",
      support: "",
      invoiceFooter: "",
      invoiceSubfooter: "",
      invoiceTitle: "",
      invoiceSubtitle: "",
      logoUrl: "",
      mainColor: "",
      secondaryColor: "",
    },
  });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const res = await fetch("/api/settings", { cache: "no-store" });
        const json: SettingsResponse = await res.json();
        if (json.success && json.data) {
          const data = json.data as OrganizationSettingsInput;
          form.reset({
            organizationName: data.organizationName || "",
            address: data.address || "",
            email: data.email || "",
            phoneNumber: data.phoneNumber || "",
            website: data.website || "",
            linkedin: data.linkedin || "",
            facebook: data.facebook || "",
            instagram: data.instagram || "",
            paymentTerms: data.paymentTerms || "",
            paymentMethods: data.paymentMethods || "",
            support: data.support || "",
            invoiceFooter: data.invoiceFooter || "",
            invoiceSubfooter: data.invoiceSubfooter || "",
            invoiceTitle: data.invoiceTitle || "",
            invoiceSubtitle: data.invoiceSubtitle || "",
            logoUrl: data.logoUrl || "",
            mainColor: data.mainColor || "",
            secondaryColor: data.secondaryColor || "",
          }, { keepDirty: false, keepTouched: false });
        }
      } catch (e) {
        console.error(e);
        setServerError("Failed to load settings");
      } finally {
        setLoading(false);
      }
    };
    fetchSettings();
  }, [form]);

  const canSubmit = !submitting && !loading;

  const handleUpload = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    const res = await fetch("/api/settings/logo", { method: "POST", body: formData });
    const json: SettingsResponse = await res.json();
    if (!json.success || !json.data?.url) {
      throw new Error(json.error || "Upload failed");
    }
    return json.data.url as string;
  };

  const onSubmit = async (data: OrganizationSettingsInput) => {
    setServerError("");
    setSubmitting(true);
    try {
      const res = await fetch("/api/settings", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      const json: SettingsResponse = await res.json();
      if (!json.success) {
        throw new Error(json.error || "Failed to save settings");
      }
      toast({ title: "Settings Saved", description: "Organization settings updated successfully." });
      form.reset(data, { keepDirty: false, keepTouched: false });
    } catch (e: any) {
      setServerError(e?.message || "Failed to save settings");
      toast({ variant: "destructive", title: "Save Failed", description: String(e?.message || e) });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-6 w-40 bg-gray-200 rounded animate-pulse" />
        <div className="h-4 w-64 bg-gray-200 rounded animate-pulse" />
        <div className="h-64 w-full bg-gray-100 rounded animate-pulse" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Organization</h2>
        <p className="text-gray-600">Manage organization profile, branding, and invoice text.</p>
      </div>

      {serverError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{serverError}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Profile</CardTitle>
          <CardDescription>Basic details and social links.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="organizationName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Organization Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Your organization" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <Input placeholder="****** 123 4567" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="linkedin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>LinkedIn</FormLabel>
                      <FormControl>
                        <Input placeholder="https://linkedin.com/company/..." {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="facebook"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Facebook</FormLabel>
                      <FormControl>
                        <Input placeholder="https://facebook.com/..." {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="instagram"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Instagram</FormLabel>
                      <FormControl>
                        <Input placeholder="https://instagram.com/..." {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem className="md:col-span-3">
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input placeholder="Street, City, Country" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="logoUrl"
                  render={({ field }) => (
                    <FormItem className="md:col-span-3">
                      <FormLabel>Logo</FormLabel>
                      <div className="flex items-center gap-4">
                        <div className="relative h-12 w-12 border rounded overflow-hidden">
                          {field.value ? (
                            <img
                              src={field.value}
                              alt="Logo"
                              className="h-full w-full object-contain"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none';
                                const parent = e.currentTarget.parentElement;
                                if (parent) {
                                  const placeholder = parent.querySelector('.logo-placeholder') as HTMLElement;
                                  if (placeholder) placeholder.style.display = 'flex';
                                }
                              }}
                            />
                          ) : null}
                          <div className={`logo-placeholder h-full w-full flex items-center justify-center text-gray-400 ${field.value ? 'hidden' : 'flex'}`}>
                            <ImageIcon className="h-6 w-6" />
                          </div>
                        </div>
                        <input
                          type="file"
                          accept="image/png,image/jpeg,image/webp,image/svg+xml"
                          onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (!file) return;

                            try {
                              setSubmitting(true); // Prevent form submission during upload
                              const url = await handleUpload(file);

                              // Update form with new logo URL
                              form.setValue("logoUrl", url, {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true
                              });

                              // Clear any existing validation errors for logoUrl
                              form.clearErrors("logoUrl");

                              // Trigger form re-validation
                              await form.trigger("logoUrl");

                              toast({ title: "Logo Uploaded", description: "Logo uploaded successfully." });
                            } catch (err: any) {
                              console.error("Logo upload error:", err);
                              toast({
                                variant: "destructive",
                                title: "Upload Failed",
                                description: err?.message || "Failed to upload logo"
                              });
                            } finally {
                              setSubmitting(false); // Re-enable form submission
                              // Reset input value if element still exists
                              if (e.currentTarget) {
                                e.currentTarget.value = "";
                              }
                            }
                          }}
                          className="hidden"
                          id="org-logo-input"
                        />
                        <Button type="button" variant="outline" onClick={() => document.getElementById("org-logo-input")?.click()}>
                          <Upload className="h-4 w-4 mr-2" /> Upload Logo
                        </Button>
                      </div>
                      <FormDescription>PNG, JPG, WEBP, SVG up to 5MB.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="mainColor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Main Brand Color</FormLabel>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <input
                            type="color"
                            {...field}
                            value={field.value || ""}
                            className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                          />
                          <Input
                            {...field}
                            value={field.value || ""}
                            placeholder="#000000"
                            className="w-24"
                          />
                        </div>
                        {field.value && (
                          <div
                            className="w-8 h-8 rounded border"
                            style={{ backgroundColor: field.value }}
                          />
                        )}
                      </div>
                      <FormDescription>Primary color for your invoice branding</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="secondaryColor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Secondary Brand Color</FormLabel>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <input
                            type="color"
                            {...field}
                            value={field.value || ""}
                            className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                          />
                          <Input
                            {...field}
                            value={field.value || ""}
                            placeholder="#ffffff"
                            className="w-24"
                          />
                        </div>
                        {field.value && (
                          <div
                            className="w-8 h-8 rounded border"
                            style={{ backgroundColor: field.value }}
                          />
                        )}
                      </div>
                      <FormDescription>Secondary color for your invoice branding</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Invoices</CardTitle>
                  <CardDescription>Texts shown on generated invoices.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="invoiceTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Invoice" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="invoiceSubtitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice Subtitle</FormLabel>
                        <FormControl>
                          <Input placeholder="Thank you for your business" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Rich text-capable fields (accept formatting as text) */}
                  <FormField
                    control={form.control}
                    name="paymentTerms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Terms</FormLabel>
                        <FormControl>
                          <Textarea rows={5} placeholder="Enter payment terms (markdown or plain text)" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormDescription>Supports plain text or pasted formatted text.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="paymentMethods"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Methods</FormLabel>
                        <FormControl>
                          <Textarea rows={5} placeholder="Describe accepted payment methods" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormDescription>Supports plain text or pasted formatted text.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="support"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Support</FormLabel>
                        <FormControl>
                          <Textarea rows={5} placeholder="Support contacts, SLA, hours" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormDescription>Supports plain text or pasted formatted text.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="invoiceFooter"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice Footer</FormLabel>
                        <FormControl>
                          <Textarea rows={3} placeholder="Footer text" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="invoiceSubfooter"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice Subfooter</FormLabel>
                        <FormControl>
                          <Textarea rows={3} placeholder="Subfooter text" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <div className="flex justify-end gap-2">
                <Button type="submit" disabled={!canSubmit}>
                  {submitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}Save Changes
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}


