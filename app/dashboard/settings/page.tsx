"use client";

import { useState, Suspense } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Users, Building2, Wifi, Utensils, Cog } from "lucide-react";
import dynamic from "next/dynamic";

// Dynamic imports to code-split each tab content
const UsersManagement = dynamic(() => import("./components/users-management"), {
  loading: () => <TabContentSkeleton />,
  ssr: false,
});

const ResourcesManagement = dynamic(() => import("./components/resources-management"), {
  loading: () => <TabContentSkeleton />,
  ssr: false,
});

const AmenitiesManagement = dynamic(() => import("./components/amenities-management"), {
  loading: () => <TabContentSkeleton />,
  ssr: false,
});

const CateringManagement = dynamic(() => import("./components/catering-management"), {
  loading: () => <TabContentSkeleton />,
  ssr: false,
});

const OrganizationSettings = dynamic(() => import("./components/organization-settings"), {
  loading: () => <TabContentSkeleton />,
  ssr: false,
});

// Loading skeleton component for tab content
function TabContentSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="grid gap-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="border rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-48" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("organization");

  return (
    <div className="h-full bg-gray-50 p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-2">
            Manage users, resources, amenities, and catering services
          </p>
        </div>
      </div>
      
      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="organization" className="flex items-center gap-2">
                <Cog className="h-4 w-4" />
                Organization
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Users
              </TabsTrigger>
              <TabsTrigger value="resources" className="flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Resources
              </TabsTrigger>
              <TabsTrigger value="amenities" className="flex items-center gap-2">
                <Wifi className="h-4 w-4" />
                Amenities
              </TabsTrigger>
              <TabsTrigger value="catering" className="flex items-center gap-2">
                <Utensils className="h-4 w-4" />
                Catering
              </TabsTrigger>
              
            </TabsList>
            
            <TabsContent value="users">
              <Suspense fallback={<TabContentSkeleton />}>
                <UsersManagement />
              </Suspense>
            </TabsContent>
            
            <TabsContent value="resources">
              <Suspense fallback={<TabContentSkeleton />}>
                <ResourcesManagement />
              </Suspense>
            </TabsContent>
            
            <TabsContent value="amenities">
              <Suspense fallback={<TabContentSkeleton />}>
                <AmenitiesManagement />
              </Suspense>
            </TabsContent>
            
            <TabsContent value="catering">
              <Suspense fallback={<TabContentSkeleton />}>
                <CateringManagement />
              </Suspense>
            </TabsContent>

            <TabsContent value="organization">
              <Suspense fallback={<TabContentSkeleton />}>
                <OrganizationSettings />
              </Suspense>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}