"use client";

import { useState, use<PERSON><PERSON>back, use<PERSON>emo, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Plus, RefreshCw, AlertCircle, Package, Wifi, WifiOff } from "lucide-react";

// Import components
import { AmenitiesTable } from "./components/amenities-table";
import { AmenityFormDialog } from "./components/amenity-form-dialog";
import { DeleteConfirmationDialog } from "./components/delete-confirmation-dialog";
import { SearchInput } from "@/components/dashboard/search-input";
import { PaginationControls } from "@/components/dashboard/pagination-controls";
import { AmenityManagementErrorBoundary } from "./components/amenity-management-error-boundary";

// Import custom hook
import { useAmenities } from "@/hooks/use-amenities";

// Import types
import { Amenity, AmenityFormData } from "@/lib/types";

interface DialogState {
  amenityForm: {
    open: boolean;
    amenity: Amenity | null;
    loading: boolean;
  };
  deleteConfirmation: {
    open: boolean;
    amenity: Amenity | null;
    loading: boolean;
  };
}

export default function AmenitiesPage() {
  // Network status tracking - initialize as true to avoid hydration mismatch
  const [isOnline, setIsOnline] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    // Set the actual online status after component mounts
    setHasMounted(true);
    setIsOnline(navigator.onLine);

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Use custom hook for amenity data management
  const {
    amenities,
    loading,
    error,
    totalAmenities,
    totalPages,
    currentPage,
    pageSize,
    searchQuery,
    createAmenity,
    updateAmenity,
    deleteAmenity,
    setSearchQuery,
    setCurrentPage,
    setPageSize,
    refresh,
    clearError,
  } = useAmenities();

  // Dialog states
  const [dialogs, setDialogs] = useState<DialogState>({
    amenityForm: {
      open: false,
      amenity: null,
      loading: false,
    },
    deleteConfirmation: {
      open: false,
      amenity: null,
      loading: false,
    },
  });

  // Amenity form handlers
  const handleCreateAmenity = useCallback(() => {
    setDialogs(prev => ({
      ...prev,
      amenityForm: { open: true, amenity: null, loading: false },
    }));
  }, []);

  const handleEditAmenity = useCallback((amenity: Amenity) => {
    setDialogs(prev => ({
      ...prev,
      amenityForm: { open: true, amenity, loading: false },
    }));
  }, []);

  const handleAmenityFormSubmit = useCallback(async (data: AmenityFormData) => {
    const isEdit = !!dialogs.amenityForm.amenity;
    
    setDialogs(prev => ({
      ...prev,
      amenityForm: { ...prev.amenityForm, loading: true },
    }));

    try {
      if (isEdit) {
        await updateAmenity(dialogs.amenityForm.amenity!.id, data);
      } else {
        await createAmenity(data);
      }

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        amenityForm: { open: false, amenity: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error submitting amenity form:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        amenityForm: { ...prev.amenityForm, loading: false },
      }));
    }
  }, [dialogs.amenityForm.amenity, createAmenity, updateAmenity]);

  // Delete amenity handlers
  const handleDeleteAmenity = useCallback((amenityId: number) => {
    const amenity = amenities.find(a => a.id === amenityId);
    if (amenity) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: true, amenity, loading: false },
      }));
    }
  }, [amenities]);

  const handleDeleteConfirm = useCallback(async () => {
    const { amenity } = dialogs.deleteConfirmation;
    if (!amenity) return;

    setDialogs(prev => ({
      ...prev,
      deleteConfirmation: { ...prev.deleteConfirmation, loading: true },
    }));

    try {
      await deleteAmenity(amenity.id);

      // Close dialog on success
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, amenity: null, loading: false },
      }));
    } catch (error) {
      // Error handling is done in the hook
      console.error("Error deleting amenity:", error);
    } finally {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { ...prev.deleteConfirmation, loading: false },
      }));
    }
  }, [dialogs.deleteConfirmation.amenity, deleteAmenity]);

  // Dialog close handlers
  const handleAmenityFormClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        amenityForm: { open: false, amenity: null, loading: false },
      }));
    }
  }, []);

  const handleDeleteConfirmationClose = useCallback((open: boolean) => {
    if (!open) {
      setDialogs(prev => ({
        ...prev,
        deleteConfirmation: { open: false, amenity: null, loading: false },
      }));
    }
  }, []);

  // Memoized values for better performance
  const totalAmenitiesText = useMemo(() => 
    `${totalAmenities} total amenities`, 
    [totalAmenities]
  );

  const shouldShowPagination = useMemo(() => 
    !loading && amenities.length > 0, 
    [loading, amenities.length]
  );

  return (
    <AmenityManagementErrorBoundary onRetry={refresh}>
      <div className="h-full bg-gray-50 mobile-spacing">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900" id="page-title">
              Amenities
            </h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base" id="page-description">
              Manage facility amenities and features
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
            {/* Network Status Indicator - only show after mount to avoid hydration issues */}
            {hasMounted && (
              <Badge 
                variant={isOnline ? "secondary" : "destructive"} 
                className="flex items-center gap-1 sm:hidden"
              >
                {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
                {isOnline ? "Online" : "Offline"}
              </Badge>
            )}
            
            <Button
              variant="outline"
              onClick={refresh}
              disabled={loading || (hasMounted && !isOnline)}
              className="touch-target focus-enhanced"
              aria-label="Refresh amenities list"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} aria-hidden="true" />
              Refresh
            </Button>
            <Button 
              onClick={handleCreateAmenity} 
              disabled={hasMounted && !isOnline}
              className="touch-target focus-enhanced"
              aria-label="Add new amenity"
            >
              <Plus className="mr-2 h-4 w-4" aria-hidden="true" />
              Add Amenity
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div>
                <CardTitle className="flex items-center" id="card-title">
                  <Package className="mr-2 h-5 w-5" aria-hidden="true" />
                  Amenity Management
                </CardTitle>
                {loading ? (
                  <div className="text-sm text-muted-foreground">
                    <Skeleton className="h-4 w-48" />
                  </div>
                ) : (
                  <CardDescription>
                    {totalAmenitiesText}
                  </CardDescription>
                )}
              </div>
              <div className="flex items-center space-x-3">
                {/* Desktop Network Status - only show after mount */}
                {hasMounted && (
                  <Badge 
                    variant={isOnline ? "secondary" : "destructive"} 
                    className="hidden sm:flex items-center gap-1"
                  >
                    {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
                    {isOnline ? "Online" : "Offline"}
                  </Badge>
                )}
                
                <div className="w-full lg:w-80">
                  <SearchInput
                    onSearch={(!hasMounted || isOnline) ? setSearchQuery : () => {}}
                    placeholder={(!hasMounted || isOnline) ? "Search amenities by name..." : "Search unavailable (offline)"}
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            {error ? (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-3">
                    <div>
                      <p className="font-medium">Unable to load amenities</p>
                      <p className="text-sm">{error}</p>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          clearError();
                          refresh();
                        }}
                        className="touch-target focus-enhanced"
                        aria-label="Try loading amenities again"
                      >
                        <RefreshCw className="mr-2 h-3 w-3" aria-hidden="true" />
                        Try Again
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          clearError();
                          setSearchQuery("");
                          setCurrentPage(1);
                          refresh();
                        }}
                        className="touch-target focus-enhanced"
                        aria-label="Reset search and retry loading amenities"
                      >
                        Reset & Retry
                      </Button>
                      {hasMounted && !isOnline && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.location.reload()}
                          className="touch-target focus-enhanced"
                          aria-label="Refresh entire page"
                        >
                          Refresh Page
                        </Button>
                      )}
                    </div>
                    {hasMounted && !isOnline && (
                      <p className="text-xs text-muted-foreground">
                        You appear to be offline. Please check your internet connection.
                      </p>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <AmenitiesTable
                  amenities={amenities}
                  loading={loading}
                  error={error}
                  onEdit={handleEditAmenity}
                  onDelete={handleDeleteAmenity}
                  onRefresh={refresh}
                  onRetry={refresh}
                />
                
                {shouldShowPagination && (
                  <div className="mt-4 sm:mt-6">
                    <PaginationControls
                      currentPage={currentPage}
                      totalPages={totalPages}
                      pageSize={pageSize}
                      totalItems={totalAmenities}
                      onPageChange={setCurrentPage}
                      onPageSizeChange={setPageSize}
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Dialogs */}
        <AmenityFormDialog
          open={dialogs.amenityForm.open}
          onOpenChange={handleAmenityFormClose}
          amenity={dialogs.amenityForm.amenity}
          onSubmit={handleAmenityFormSubmit}
          loading={dialogs.amenityForm.loading}
        />

        <DeleteConfirmationDialog
          open={dialogs.deleteConfirmation.open}
          onOpenChange={handleDeleteConfirmationClose}
          amenity={dialogs.deleteConfirmation.amenity}
          onConfirm={handleDeleteConfirm}
          loading={dialogs.deleteConfirmation.loading}
        />
      </div>
    </AmenityManagementErrorBoundary>
  );
}