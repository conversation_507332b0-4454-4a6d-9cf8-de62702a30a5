import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { AmenitiesTable } from '../amenities-table'
import { Amenity } from '@/lib/types'

// Mock the shared components
vi.mock('@/components/ui/offline-state-handler', () => ({
  OfflineStateHandler: ({ children }: { children: React.ReactNode }) => <div data-testid="offline-handler">{children}</div>
}))

vi.mock('@/components/ui/filter-error-state', () => ({
  FilterErrorState: ({ error, onRetry }: { error: any, onRetry: () => void }) => (
    <div data-testid="filter-error-state">
      <span>{typeof error === 'string' ? error : error.message}</span>
      <button onClick={onRetry}>Retry</button>
    </div>
  )
}))

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}))

// Mock the icon components
vi.mock('lucide-react', () => ({
  Wifi: () => <div data-testid="wifi-icon">WiFi</div>,
  Projector: () => <div data-testid="projector-icon">Projector</div>,
  Coffee: () => <div data-testid="coffee-icon">Coffee</div>,
  Car: () => <div data-testid="car-icon">Parking</div>,
  Utensils: () => <div data-testid="utensils-icon">Catering</div>,
  Edit: () => <div data-testid="edit-icon">Edit</div>,
  Trash2: () => <div data-testid="trash-icon">Delete</div>,
  ChevronUp: () => <div data-testid="chevron-up">↑</div>,
  ChevronDown: () => <div data-testid="chevron-down">↓</div>
}))

const mockAmenities: Amenity[] = [
  {
    id: 1,
    name: 'WiFi',
    icon: 'wifi',
    description: 'High-speed wireless internet',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: 2,
    name: 'Projector',
    icon: 'projector',
    description: 'HD projector for presentations',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02')
  },
  {
    id: 3,
    name: 'Coffee Machine',
    icon: 'coffee',
    description: 'Professional espresso machine',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03')
  },
  {
    id: 4,
    name: 'Parking',
    icon: 'car',
    description: 'Dedicated parking spaces',
    createdAt: new Date('2024-01-04'),
    updatedAt: new Date('2024-01-04')
  }
]

describe('AmenitiesTable', () => {
  const mockOnEdit = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnRefresh = vi.fn()
  const mockOnRetry = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders amenities table with data', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('offline-handler')).toBeInTheDocument()
    expect(screen.getByText('WiFi')).toBeInTheDocument()
    expect(screen.getByText('Projector')).toBeInTheDocument()
    expect(screen.getByText('Coffee Machine')).toBeInTheDocument()
    expect(screen.getByText('Parking')).toBeInTheDocument()
  })

  it('shows loading skeleton when loading', () => {
    render(
      <AmenitiesTable
        amenities={[]}
        loading={true}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('amenities-table-skeleton')).toBeInTheDocument()
  })

  it('shows error state when error is present', () => {
    const error = 'Failed to load amenities'
    
    render(
      <AmenitiesTable
        amenities={[]}
        loading={false}
        error={error}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
    expect(screen.getByText(error)).toBeInTheDocument()
  })

  it('calls onRetry when retry button is clicked in error state', () => {
    render(
      <AmenitiesTable
        amenities={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
      />
    )

    const retryButton = screen.getByText('Retry')
    fireEvent.click(retryButton)

    expect(mockOnRetry).toHaveBeenCalledTimes(1)
  })

  it('shows empty state when no amenities', () => {
    render(
      <AmenitiesTable
        amenities={[]}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('No amenities found')).toBeInTheDocument()
  })

  it('handles sorting by name', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const nameHeader = screen.getByText('Name')
    fireEvent.click(nameHeader)

    // Check that amenities are sorted alphabetically
    const amenityNames = screen.getAllByTestId(/amenity-name-/)
    expect(amenityNames[0]).toHaveTextContent('Coffee Machine')
    expect(amenityNames[1]).toHaveTextContent('Parking')
    expect(amenityNames[2]).toHaveTextContent('Projector')
    expect(amenityNames[3]).toHaveTextContent('WiFi')
  })

  it('handles sorting by creation date', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const createdHeader = screen.getByText('Created')
    fireEvent.click(createdHeader)

    // Check that amenities are sorted by creation date
    const amenityDates = screen.getAllByTestId(/amenity-created-/)
    expect(amenityDates[0]).toHaveTextContent('Jan 1, 2024')
    expect(amenityDates[1]).toHaveTextContent('Jan 2, 2024')
    expect(amenityDates[2]).toHaveTextContent('Jan 3, 2024')
    expect(amenityDates[3]).toHaveTextContent('Jan 4, 2024')
  })

  it('calls onEdit when edit button is clicked', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const editButtons = screen.getAllByText('Edit')
    fireEvent.click(editButtons[0])

    expect(mockOnEdit).toHaveBeenCalledWith(mockAmenities[0])
  })

  it('calls onDelete when delete button is clicked', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const deleteButtons = screen.getAllByText('Delete')
    fireEvent.click(deleteButtons[0])

    expect(mockOnDelete).toHaveBeenCalledWith(mockAmenities[0].id)
  })

  it('displays icons correctly for each amenity', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('wifi-icon')).toBeInTheDocument()
    expect(screen.getByTestId('projector-icon')).toBeInTheDocument()
    expect(screen.getByTestId('coffee-icon')).toBeInTheDocument()
    expect(screen.getByTestId('car-icon')).toBeInTheDocument()
  })

  it('displays descriptions correctly', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('High-speed wireless internet')).toBeInTheDocument()
    expect(screen.getByText('HD projector for presentations')).toBeInTheDocument()
    expect(screen.getByText('Professional espresso machine')).toBeInTheDocument()
    expect(screen.getByText('Dedicated parking spaces')).toBeInTheDocument()
  })

  it('displays mobile card layout on small screens', () => {
    // Mock window.innerWidth to simulate mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 640,
    })

    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByTestId('amenities-mobile-view')).toBeInTheDocument()
    expect(screen.getAllByTestId(/amenity-card-/)).toHaveLength(4)
  })

  it('handles retry count and retrying state', () => {
    render(
      <AmenitiesTable
        amenities={[]}
        loading={false}
        error="Network error"
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
        onRetry={mockOnRetry}
        retryCount={2}
        isRetrying={true}
      />
    )

    expect(screen.getByTestId('filter-error-state')).toBeInTheDocument()
  })

  it('applies proper accessibility attributes', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const table = screen.getByRole('table')
    expect(table).toHaveAttribute('aria-label', 'Amenities table')

    const columnHeaders = screen.getAllByRole('columnheader')
    expect(columnHeaders).toHaveLength(5) // Icon, Name, Description, Created, Actions

    const sortButtons = screen.getAllByRole('button', { name: /sort by/i })
    expect(sortButtons.length).toBeGreaterThan(0)
  })

  it('memoizes amenity cards for performance', () => {
    const { rerender } = render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    // Re-render with same props
    rerender(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    // Amenity cards should be memoized and not re-render unnecessarily
    expect(screen.getAllByTestId(/amenity-card-/)).toHaveLength(4)
  })

  it('optimizes icon rendering and caching', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    // Check that icons are rendered efficiently
    const iconElements = screen.getAllByTestId(/.*-icon$/)
    expect(iconElements.length).toBe(4) // One icon per amenity

    // Each amenity should have its corresponding icon
    expect(screen.getByTestId('wifi-icon')).toBeInTheDocument()
    expect(screen.getByTestId('projector-icon')).toBeInTheDocument()
    expect(screen.getByTestId('coffee-icon')).toBeInTheDocument()
    expect(screen.getByTestId('car-icon')).toBeInTheDocument()
  })

  it('handles icon loading performance optimization', () => {
    const { rerender } = render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    // Re-render with same amenities
    rerender(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    // Icons should be cached and not re-render
    expect(screen.getAllByTestId(/.*-icon$/)).toHaveLength(4)
  })

  it('formats creation dates consistently', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    expect(screen.getByText('Jan 1, 2024')).toBeInTheDocument()
    expect(screen.getByText('Jan 2, 2024')).toBeInTheDocument()
    expect(screen.getByText('Jan 3, 2024')).toBeInTheDocument()
    expect(screen.getByText('Jan 4, 2024')).toBeInTheDocument()
  })

  it('handles keyboard navigation for amenity actions', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const firstEditButton = screen.getAllByText('Edit')[0]
    firstEditButton.focus()
    expect(firstEditButton).toHaveFocus()

    // Test keyboard navigation between action buttons
    fireEvent.keyDown(firstEditButton, { key: 'Tab' })
    const firstDeleteButton = screen.getAllByText('Delete')[0]
    expect(firstDeleteButton).toHaveFocus()
  })

  it('handles amenity sorting and filtering operations efficiently', () => {
    render(
      <AmenitiesTable
        amenities={mockAmenities}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    // Test multiple sort operations
    const nameHeader = screen.getByText('Name')
    fireEvent.click(nameHeader) // Sort ascending
    fireEvent.click(nameHeader) // Sort descending

    const amenityNames = screen.getAllByTestId(/amenity-name-/)
    expect(amenityNames[0]).toHaveTextContent('WiFi')
    expect(amenityNames[1]).toHaveTextContent('Projector')
    expect(amenityNames[2]).toHaveTextContent('Parking')
    expect(amenityNames[3]).toHaveTextContent('Coffee Machine')
  })

  it('handles large datasets with performance monitoring', () => {
    const largeAmenitySet = Array.from({ length: 100 }, (_, index) => ({
      id: index + 1,
      name: `Amenity ${index + 1}`,
      icon: 'wifi',
      description: `Description for amenity ${index + 1}`,
      createdAt: new Date(`2024-01-${(index % 30) + 1}`),
      updatedAt: new Date(`2024-01-${(index % 30) + 1}`)
    }))

    const startTime = performance.now()
    
    render(
      <AmenitiesTable
        amenities={largeAmenitySet}
        loading={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onRefresh={mockOnRefresh}
      />
    )

    const endTime = performance.now()
    const renderTime = endTime - startTime

    // Ensure rendering completes within reasonable time (less than 100ms)
    expect(renderTime).toBeLessThan(100)
    expect(screen.getByText('Amenity 1')).toBeInTheDocument()
  })
})