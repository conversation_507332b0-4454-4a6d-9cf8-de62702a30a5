"use client"

import { useState, memo, useMemo, useCallback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Package,
} from "lucide-react"
import { Amenity } from "@/lib/types"
import { format } from "date-fns"
import { getAmenityIcon } from "@/lib/utils/amenity-icons"
import { AmenitiesTableSkeleton } from "./amenities-table-skeleton"
import { FilterErrorState } from "@/components/ui/filter-error-state"
import { OfflineStateHandler } from "@/components/ui/offline-state-handler"
import { 
  TABLE_SPACING, 
  TOUCH_TARGETS, 
  FOCUS_STYLES, 
  TABLE_LAYOUT,
  SORT_STYLES,
  ACTION_STYLES,
  TABLE_TYPOGRAPHY,
  ANIMATIONS,
  combineTableClasses 
} from "@/lib/utils/table-styles"

interface AmenitiesTableProps {
  amenities: Amenity[]
  loading: boolean
  error?: Error | string | null
  onEdit: (amenity: Amenity) => void
  onDelete: (amenityId: number) => void
  onRefresh: () => void
  retryCount?: number
  isRetrying?: boolean
  onRetry?: () => void
}

type SortField = 'name' | 'icon' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

// Mobile card component for individual amenities - memoized for performance
const AmenityCard = memo(({ 
  amenity, 
  onEdit, 
  onDelete 
}: { 
  amenity: Amenity
  onEdit: (amenity: Amenity) => void
  onDelete: (amenityId: number) => void
}) => {
  // Memoize icon component to avoid re-rendering when icon doesn't change
  const IconComponent = useMemo(() => getAmenityIcon(amenity.icon), [amenity.icon])
  
  // Memoize event handlers to prevent unnecessary re-renders
  const handleEdit = useCallback(() => onEdit(amenity), [onEdit, amenity])
  const handleDelete = useCallback(() => onDelete(amenity.id), [onDelete, amenity.id])
  
  // Memoize formatted icon name to avoid recalculation
  const formattedIconName = useMemo(() => 
    amenity.icon.replace(/_/g, ' ').toLowerCase(), 
    [amenity.icon]
  )
  
  // Memoize formatted date to avoid recalculation
  const formattedDate = useMemo(() => {
    if (!amenity.createdAt) return 'N/A'
    try {
      return format(new Date(amenity.createdAt), 'MMM dd, yyyy')
    } catch (error) {
      return 'Invalid date'
    }
  }, [amenity.createdAt])
  
  return (
    <Card className={combineTableClasses("mb-4", ANIMATIONS.transition, ANIMATIONS.hoverBg)}>
      <CardContent className={TABLE_SPACING.mobileCardPadding}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <div 
                className="flex items-center justify-center w-10 h-10 rounded-md bg-muted"
                aria-hidden="true"
              >
                <IconComponent className="h-5 w-5" />
              </div>
              <h3 className={TABLE_TYPOGRAPHY.cardTitle} id={`amenity-${amenity.id}`}>
                {amenity.name}
              </h3>
            </div>
            
            <div className={combineTableClasses("space-y-1", TABLE_TYPOGRAPHY.cardSubtitle)}>
              <div className="flex items-center space-x-2">
                <span className="font-medium">Icon:</span>
                <span className="capitalize">
                  {formattedIconName}
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Created:</span>
                <span>
                  {formattedDate}
                </span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className={combineTableClasses(TOUCH_TARGETS.iconButton, "p-0", FOCUS_STYLES.enhanced)}
                aria-label={`Actions for ${amenity.name}`}
              >
                <span className="sr-only">Open actions menu for {amenity.name}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className={ACTION_STYLES.dropdownContent}>
              <DropdownMenuItem 
                onClick={handleEdit}
                className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                role="menuitem"
              >
                <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                Edit {amenity.name}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleDelete}
                className={combineTableClasses(ACTION_STYLES.dropdownItemDestructive, FOCUS_STYLES.enhanced)}
                role="menuitem"
              >
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                Delete {amenity.name}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
})

// Desktop table row component - memoized for performance
const AmenityTableRow = memo(({ 
  amenity, 
  onEdit, 
  onDelete 
}: { 
  amenity: Amenity
  onEdit: (amenity: Amenity) => void
  onDelete: (amenityId: number) => void
}) => {
  // Memoize icon component to avoid re-rendering when icon doesn't change
  const IconComponent = useMemo(() => getAmenityIcon(amenity.icon), [amenity.icon])
  
  // Memoize event handlers to prevent unnecessary re-renders
  const handleEdit = useCallback(() => onEdit(amenity), [onEdit, amenity])
  const handleDelete = useCallback(() => onDelete(amenity.id), [onDelete, amenity.id])
  
  // Memoize formatted icon name to avoid recalculation
  const formattedIconName = useMemo(() => 
    amenity.icon.replace(/_/g, ' ').toLowerCase(), 
    [amenity.icon]
  )
  
  // Memoize formatted date to avoid recalculation
  const formattedDate = useMemo(() => {
    if (!amenity.createdAt) return 'N/A'
    try {
      return format(new Date(amenity.createdAt), 'MMM dd, yyyy')
    } catch (error) {
      return 'Invalid date'
    }
  }, [amenity.createdAt])

  return (
    <TableRow className={combineTableClasses(TABLE_LAYOUT.tableRow, ANIMATIONS.transition)}>
      <TableCell className={combineTableClasses(TABLE_LAYOUT.tableCell, TABLE_TYPOGRAPHY.cellTextBold)}>
        {amenity.name}
      </TableCell>
      <TableCell className={TABLE_LAYOUT.tableCell}>
        <div className={combineTableClasses("flex items-center", TABLE_SPACING.iconGap)}>
          <div className="flex items-center justify-center w-6 h-6 rounded bg-muted">
            <IconComponent className="h-4 w-4" />
          </div>
          <span className={combineTableClasses("capitalize", TABLE_TYPOGRAPHY.cellText)}>
            {formattedIconName}
          </span>
        </div>
      </TableCell>
      <TableCell className={combineTableClasses(TABLE_LAYOUT.tableCell, TABLE_TYPOGRAPHY.cellTextMuted, "hidden lg:table-cell")}>
        {formattedDate}
      </TableCell>
      <TableCell className={combineTableClasses(TABLE_LAYOUT.tableCell, "w-[70px] min-w-[70px]")}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              className={combineTableClasses(ACTION_STYLES.dropdownTrigger, FOCUS_STYLES.enhanced)}
              aria-label={`Actions for ${amenity.name}`}
            >
              <span className="sr-only">Open actions menu for {amenity.name}</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className={ACTION_STYLES.dropdownContent} role="menu">
            <DropdownMenuItem 
              onClick={handleEdit}
              className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
              role="menuitem"
            >
              <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
              Edit {amenity.name}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleDelete}
              className={combineTableClasses(ACTION_STYLES.dropdownItemDestructive, FOCUS_STYLES.enhanced)}
              role="menuitem"
            >
              <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
              Delete {amenity.name}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  )
})

export const AmenitiesTable = memo(({
  amenities,
  loading,
  error,
  onEdit,
  onDelete,
  onRefresh,
  retryCount = 0,
  isRetrying = false,
  onRetry,
}: AmenitiesTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdAt',
    direction: 'desc',
  })

  // Performance monitoring for large datasets
  const performanceMetrics = useMemo(() => {
    const startTime = performance.now()
    const result = {
      amenityCount: amenities.length,
      isLargeDataset: amenities.length > 100,
      renderStartTime: startTime
    }
    
    // Log performance warning for large datasets
    if (result.isLargeDataset && process.env.NODE_ENV === 'development') {
      console.warn(`AmenitiesTable: Rendering ${result.amenityCount} amenities. Consider implementing virtualization for better performance.`)
    }
    
    return result
  }, [amenities.length])

  const handleSort = useCallback((field: SortField) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }, [])

  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  const sortedAmenities = useMemo(() => {
    const sortStartTime = performance.now()
    
    const sorted = [...amenities].sort((a, b) => {
      const { field, direction } = sortConfig
      let aValue: any = a[field]
      let bValue: any = b[field]

      // Handle date sorting
      if (field === 'createdAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })
    
    // Performance monitoring for sorting operations
    const sortDuration = performance.now() - sortStartTime
    if (sortDuration > 10 && process.env.NODE_ENV === 'development') {
      console.warn(`AmenitiesTable: Sorting ${amenities.length} amenities took ${sortDuration.toFixed(2)}ms. Consider optimizing sort algorithm for field: ${sortConfig.field}`)
    }
    
    return sorted
  }, [amenities, sortConfig])

  // Handle error state
  if (error && !loading) {
    return (
      <FilterErrorState
        error={error}
        filterType="amenity"
        onRetry={onRetry || onRefresh}
        loading={isRetrying}
        context="amenities"
        showDetails={true}
      />
    )
  }

  if (loading) {
    return <AmenitiesTableSkeleton />
  }

  if (amenities.length === 0) {
    return (
      <>
        {/* Mobile empty state */}
        <div className="block md:hidden">
          <Card className={TABLE_LAYOUT.card}>
            <CardContent className={combineTableClasses("p-8 text-center", TABLE_LAYOUT.emptyState)}>
              <Package className={TABLE_LAYOUT.emptyStateIcon} />
              <div className="space-y-2">
                <p className={TABLE_LAYOUT.emptyStateTitle}>No amenities found</p>
                <p className={TABLE_LAYOUT.emptyStateDescription}>
                  Try adjusting your search or add a new amenity
                </p>
              </div>
              <Button 
                variant="outline" 
                onClick={onRefresh} 
                className={combineTableClasses(TOUCH_TARGETS.button, FOCUS_STYLES.enhanced, "px-6")}
                aria-label="Refresh amenities list"
              >
                Refresh
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Desktop empty state */}
        <div className={combineTableClasses("hidden md:block", TABLE_LAYOUT.tableContainer)}>
          <div className={TABLE_LAYOUT.tableWrapper}>
            <Table className={TABLE_LAYOUT.table}>
              <TableHeader className={TABLE_LAYOUT.tableHeader}>
                <TableRow>
                  <TableHead className="min-w-[150px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('name')}
                      className={SORT_STYLES.sortButton}
                    >
                      Name
                      {getSortIcon('name')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('icon')}
                      className={SORT_STYLES.sortButton}
                    >
                      Icon
                      {getSortIcon('icon')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('createdAt')}
                      className={SORT_STYLES.sortButton}
                    >
                      Created
                      {getSortIcon('createdAt')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={4} className={combineTableClasses("h-24 text-center", TABLE_LAYOUT.emptyState)}>
                    <p className={TABLE_TYPOGRAPHY.cellTextMuted}>No amenities found</p>
                    <Button 
                      variant="outline" 
                      onClick={onRefresh} 
                      className={combineTableClasses(TOUCH_TARGETS.button, FOCUS_STYLES.enhanced, "px-6")}
                      aria-label="Refresh amenities list"
                    >
                      Refresh
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </>
    )
  }

  return (
    <OfflineStateHandler 
      onConnectionRestored={onRefresh}
      enablePersistence={true}
      storageKey="amenities_offline"
    >
      {/* Mobile card view */}
      <div className="block md:hidden">
        {/* Mobile sort controls */}
        <div className={SORT_STYLES.mobileSortContainer}>
          <span className={SORT_STYLES.mobileSortLabel} id="sort-label">Sort by:</span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className={combineTableClasses(TOUCH_TARGETS.button, FOCUS_STYLES.enhanced)}
                aria-labelledby="sort-label"
                aria-expanded="false"
              >
                {sortConfig.field === 'name' && 'Name'}
                {sortConfig.field === 'icon' && 'Icon'}
                {sortConfig.field === 'createdAt' && 'Created'}
                {sortConfig.direction === 'asc' ? (
                  <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                ) : (
                  <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className={SORT_STYLES.mobileSortDropdown} role="menu">
              <DropdownMenuItem 
                onClick={() => handleSort('name')}
                className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                role="menuitem"
              >
                Name {getSortIcon('name')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('icon')}
                className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                role="menuitem"
              >
                Icon {getSortIcon('icon')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('createdAt')}
                className={combineTableClasses(ACTION_STYLES.dropdownItem, FOCUS_STYLES.enhanced)}
                role="menuitem"
              >
                Created {getSortIcon('createdAt')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className={TABLE_SPACING.mobileCardGap}>
          {sortedAmenities.map((amenity) => (
            <AmenityCard
              key={amenity.id}
              amenity={amenity}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      </div>

      {/* Desktop table view */}
      <div className={combineTableClasses("hidden md:block", TABLE_LAYOUT.tableContainer)}>
        <div className={TABLE_LAYOUT.tableWrapper}>
          <Table className={TABLE_LAYOUT.table}>
            <TableHeader className={TABLE_LAYOUT.tableHeader}>
              <TableRow>
                <TableHead className="min-w-[150px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('name')}
                    className={combineTableClasses(SORT_STYLES.sortButton, FOCUS_STYLES.enhanced)}
                    aria-label={`Sort by name ${sortConfig.field === 'name' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Name
                    {getSortIcon('name')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('icon')}
                    className={combineTableClasses(SORT_STYLES.sortButton, FOCUS_STYLES.enhanced)}
                    aria-label={`Sort by icon ${sortConfig.field === 'icon' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Icon
                    {getSortIcon('icon')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('createdAt')}
                    className={combineTableClasses(SORT_STYLES.sortButton, FOCUS_STYLES.enhanced)}
                    aria-label={`Sort by creation date ${sortConfig.field === 'createdAt' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Created
                    {getSortIcon('createdAt')}
                  </Button>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedAmenities.map((amenity) => (
                <AmenityTableRow
                  key={amenity.id}
                  amenity={amenity}
                  onEdit={onEdit}
                  onDelete={onDelete}
                />
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </OfflineStateHandler>
  )
})