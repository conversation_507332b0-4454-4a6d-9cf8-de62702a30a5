"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function BookingDebugPage() {
  const [debugResults, setDebugResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const addDebugResult = (test: string, result: any) => {
    setDebugResults(prev => [...prev, { test, result, timestamp: new Date() }]);
  };

  const runTests = async () => {
    setLoading(true);
    setDebugResults([]);

    // Test 1: Basic API connectivity
    try {
      const response = await fetch('/api/customers');
      const data = await response.json();
      addDebugResult('Customers API', { 
        status: response.status, 
        success: data.success, 
        count: data.data?.data?.length 
      });
    } catch (error) {
      addDebugResult('Customers API', { error: error instanceof Error ? error.message : String(error) });
    }

    // Test 2: Resources API
    try {
      const response = await fetch('/api/resources');
      const data = await response.json();
      addDebugResult('Resources API', { 
        status: response.status, 
        success: data.success, 
        count: data.data?.data?.length 
      });
    } catch (error) {
      addDebugResult('Resources API', { error: error instanceof Error ? error.message : String(error) });
    }

    // Test 3: Conflict detection with valid data
    try {
      const conflictData = {
        resourceIds: [136],
        periods: [{
          start: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          end: new Date(Date.now() + 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString()
        }]
      };
      
      const response = await fetch('/api/bookings/check-overlap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(conflictData)
      });
      
      const data = await response.json();
      addDebugResult('Conflict Check API', { 
        status: response.status, 
        hasConflicts: data.hasConflicts,
        message: data.message,
        data
      });
    } catch (error) {
      addDebugResult('Conflict Check API', { error: error instanceof Error ? error.message : String(error) });
    }

    // Test 4: Booking creation with valid data
    try {
      const bookingData = {
        customerId: 133,
        resourceIds: [136],
        status: "PENDING",
        periods: [{
          start: new Date(Date.now() + 25 * 60 * 60 * 1000),
          end: new Date(Date.now() + 25 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000),
          isRecurring: false
        }],
        caterings: []
      };
      
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(bookingData)
      });
      
      const data = await response.json();
      addDebugResult('Booking Creation API', { 
        status: response.status, 
        success: data.success,
        bookingId: data.data?.id,
        error: data.error,
        details: data.details
      });
    } catch (error) {
      addDebugResult('Booking Creation API', { error: error instanceof Error ? error.message : String(error) });
    }

    // Test 5: Form validation schema
    try {
      const { bookingCreateSchema } = await import('@/lib/validations/booking');
      const testData = {
        customerId: 133,
        resourceIds: [136],
        status: "PENDING",
        periods: [{
          start: new Date(Date.now() + 26 * 60 * 60 * 1000),
          end: new Date(Date.now() + 26 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000),
          isRecurring: false
        }],
        caterings: []
      };
      
      const validationResult = bookingCreateSchema.safeParse(testData);
      addDebugResult('Form Validation', { 
        success: validationResult.success,
        errors: validationResult.success ? null : validationResult.error.errors
      });
    } catch (error) {
      addDebugResult('Form Validation', { error: error instanceof Error ? error.message : String(error) });
    }

    setLoading(false);
  };

  const clearResults = () => {
    setDebugResults([]);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Booking Form Debug Tool</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={runTests} disabled={loading}>
              {loading ? 'Running Tests...' : 'Run All Tests'}
            </Button>
            <Button variant="outline" onClick={clearResults}>
              Clear Results
            </Button>
          </div>

          {debugResults.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Test Results:</h3>
              {debugResults.map((result, index) => (
                <Alert key={index} className={result.result.error ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
                  <AlertDescription>
                    <div className="font-semibold mb-2">{result.test}</div>
                    <pre className="text-xs overflow-auto bg-white p-2 rounded border">
                      {JSON.stringify(result.result, null, 2)}
                    </pre>
                    <div className="text-xs text-gray-500 mt-1">
                      {result.timestamp.toLocaleTimeString()}
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}