"use client"

import { useState, memo, useMemo, useCallback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  FileText,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Calendar,
  User,
} from "lucide-react"
import { 
  Booking, 
  BookingStatus,
  BOOKING_STATUS_LABELS,
  formatBookingDuration,
  formatPeriodDuration,
  formatCurrency
} from "@/lib/types"
import { format } from "date-fns"
import { BookingsTableSkeleton } from "./bookings-table-skeleton"
import { BookingCard } from "./booking-card"
import { FilterErrorState } from "./filter-error-state"
import { OfflineStateHandler } from "./offline-state-handler"

interface BookingsTableProps {
  bookings: Booking[]
  loading: boolean
  error?: Error | string | null
  onEdit: (booking: Booking) => void
  onDelete: (bookingId: number) => void
  onGenerateInvoice: (bookingId: number) => void
  onRefresh: () => void
  retryCount?: number
  isRetrying?: boolean
  onRetry?: () => void
}

type SortField = 'customerName' | 'start' | 'status' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

const getStatusColor = (status: BookingStatus) => {
  switch (status) {
    case 'CONFIRMED':
      return 'bg-green-100 border-green-300 text-green-800'
    case 'PENDING':
      return 'bg-yellow-100 border-yellow-300 text-yellow-800'
    case 'CANCELLED':
      return 'bg-red-100 border-red-300 text-red-800'
    default:
      return 'bg-gray-100 border-gray-300 text-gray-800'
  }
}

const getInvoiceStatusDisplay = (booking: Booking) => {
  if (!booking.invoice) {
    return { text: 'None', color: 'bg-gray-100 text-gray-600' }
  }
  
  switch (booking.invoice.status) {
    case 'PAID':
      return { text: 'Paid', color: 'bg-green-100 text-green-700' }
    case 'PARTIALLY_PAID':
      return { text: 'Partial', color: 'bg-yellow-100 text-yellow-700' }
    case 'PENDING':
      return { text: 'Pending', color: 'bg-blue-100 text-blue-700' }
    case 'CANCELLED':
      return { text: 'Cancelled', color: 'bg-red-100 text-red-700' }
    default:
      return { text: 'Unknown', color: 'bg-gray-100 text-gray-600' }
  }
}

// Calculate total duration from all periods in a booking
const calculateTotalDuration = (booking: Booking): string => {
  if (!booking.periods || booking.periods.length === 0) {
    return 'No periods'
  }

  const totalMs = booking.periods.reduce((total, period) => {
    const duration = new Date(period.end).getTime() - new Date(period.start).getTime()
    return total + duration
  }, 0)

  // Convert milliseconds to hours and minutes
  const totalMinutes = Math.round(totalMs / (1000 * 60))
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60

  if (hours === 0) {
    return `${minutes}min`
  } else if (minutes === 0) {
    return `${hours}h`
  } else {
    return `${hours}h ${minutes}min`
  }
}

// Format the first 3 periods with indication of more
const formatPeriodsDisplay = (booking: Booking) => {
  if (!booking.periods || booking.periods.length === 0) {
    return { periodsToShow: [], hasMore: false, moreCount: 0 }
  }

  // Sort periods by start date
  const sortedPeriods = [...booking.periods].sort((a, b) => 
    new Date(a.start).getTime() - new Date(b.start).getTime()
  )

  const periodsToShow = sortedPeriods.slice(0, 3)
  const hasMore = sortedPeriods.length > 3
  const moreCount = sortedPeriods.length - 3

  return { periodsToShow, hasMore, moreCount }
}

export const BookingsTable = memo(({
  bookings,
  loading,
  error,
  onEdit,
  onDelete,
  onGenerateInvoice,
  onRefresh,
  retryCount = 0,
  isRetrying = false,
  onRetry,
}: BookingsTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdAt',
    direction: 'desc',
  })

  // Memoize sort handler to prevent unnecessary re-renders
  const handleSort = useCallback((field: SortField) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }, [])

  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  const sortedBookings = useMemo(() => {
    return [...bookings].sort((a, b) => {
      const { field, direction } = sortConfig
      let aValue: any
      let bValue: any

      // Handle special sorting cases
      if (field === 'customerName') {
        aValue = a.customer.name.toLowerCase()
        bValue = b.customer.name.toLowerCase()
      } else if (field === 'start') {
        try {
          aValue = a.computedStart ? new Date(a.computedStart).getTime() : 0
          bValue = b.computedStart ? new Date(b.computedStart).getTime() : 0
        } catch (error) {
          aValue = 0
          bValue = 0
        }
      } else if (field === 'status') {
        // Sort by status priority: CONFIRMED, PENDING, CANCELLED
        const statusOrder = { 'CONFIRMED': 1, 'PENDING': 2, 'CANCELLED': 3 }
        aValue = statusOrder[a.status] || 4
        bValue = statusOrder[b.status] || 4
      } else if (field === 'createdAt') {
        try {
          aValue = a.createdAt ? new Date(a.createdAt).getTime() : 0
          bValue = b.createdAt ? new Date(b.createdAt).getTime() : 0
        } catch (error) {
          aValue = 0
          bValue = 0
        }
      } else {
        // Fallback for any other fields
        aValue = (a as any)[field]
        bValue = (b as any)[field]
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [bookings, sortConfig])

  // Memoize handlers to prevent unnecessary re-renders
  const handleRetryAction = useMemo(() => onRetry || onRefresh, [onRetry, onRefresh])
  
  // Memoize edit handler
  const handleEditBooking = useCallback((booking: Booking) => {
    onEdit(booking)
  }, [onEdit])
  
  // Memoize delete handler  
  const handleDeleteBooking = useCallback((bookingId: number) => {
    onDelete(bookingId)
  }, [onDelete])
  
  // Memoize invoice handler
  const handleGenerateInvoiceBooking = useCallback((bookingId: number) => {
    onGenerateInvoice(bookingId)
  }, [onGenerateInvoice])

  if (loading) {
    return (
      <OfflineStateHandler onConnectionRestored={onRefresh}>
        <BookingsTableSkeleton />
      </OfflineStateHandler>
    )
  }

  if (error) {
    return (
      <OfflineStateHandler onConnectionRestored={onRefresh}>
        <FilterErrorState
          error={error}
          filterType="general"
          onRetry={handleRetryAction}
          loading={isRetrying}
          showDetails={process.env.NODE_ENV === 'development'}
          className="mb-4"
        />
        
        {/* Show empty table structure with error state for context */}
        <div className="opacity-50 pointer-events-none">
          <BookingsTableSkeleton />
        </div>
      </OfflineStateHandler>
    )
  }

  if (bookings.length === 0) {
    return (
      <>
        {/* Mobile empty state */}
        <div className="block md:hidden">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center justify-center space-y-4">
                <Calendar className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">No bookings found</p>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your filters or create a new booking
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  onClick={onRefresh} 
                  className="touch-target focus-enhanced px-6"
                  aria-label="Refresh bookings list"
                >
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop empty state */}
        <div className="hidden md:block rounded-md border">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[180px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('customerName')}
                      className="h-auto p-0 font-semibold"
                    >
                      Customer
                      {getSortIcon('customerName')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[140px]">Resources</TableHead>
                  <TableHead className="min-w-[240px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('start')}
                      className="h-auto p-0 font-semibold"
                    >
                      Date & Time
                      {getSortIcon('start')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[160px]">Duration</TableHead>
                  <TableHead className="min-w-[100px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('status')}
                      className="h-auto p-0 font-semibold"
                    >
                      Status
                      {getSortIcon('status')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px]">Invoice</TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('createdAt')}
                      className="h-auto p-0 font-semibold"
                    >
                      Created
                      {getSortIcon('createdAt')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-muted-foreground">No bookings found</p>
                      <Button 
                        variant="outline" 
                        onClick={onRefresh} 
                        className="touch-target focus-enhanced px-6"
                        aria-label="Refresh bookings list"
                      >
                        Refresh
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </>
    )
  }

  return (
    <OfflineStateHandler onConnectionRestored={onRefresh}>
      <>
        {/* Mobile card view */}
      <div className="block md:hidden">
        {/* Mobile sort controls */}
        <div className="flex items-center justify-between mb-4 p-4 bg-muted/50 rounded-lg mobile-spacing">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium" id="sort-label">Sort by:</span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="touch-target focus-enhanced"
                  aria-labelledby="sort-label"
                  aria-expanded="false"
                >
                  {sortConfig.field === 'customerName' && 'Customer'}
                  {sortConfig.field === 'start' && 'Date'}
                  {sortConfig.field === 'status' && 'Status'}
                  {sortConfig.field === 'createdAt' && 'Created'}
                  {sortConfig.direction === 'asc' ? (
                    <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                  ) : (
                    <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40" role="menu">
                <DropdownMenuItem 
                  onClick={() => handleSort('customerName')}
                  className="touch-target focus-enhanced"
                  role="menuitem"
                >
                  Customer {getSortIcon('customerName')}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => handleSort('start')}
                  className="touch-target focus-enhanced"
                  role="menuitem"
                >
                  Date {getSortIcon('start')}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => handleSort('status')}
                  className="touch-target focus-enhanced"
                  role="menuitem"
                >
                  Status {getSortIcon('status')}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => handleSort('createdAt')}
                  className="touch-target focus-enhanced"
                  role="menuitem"
                >
                  Created {getSortIcon('createdAt')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="space-y-4">
          {sortedBookings.map((booking) => (
            <BookingCard
              key={booking.id}
              booking={booking}
              onEdit={handleEditBooking}
              onDelete={handleDeleteBooking}
              onGenerateInvoice={handleGenerateInvoiceBooking}
            />
          ))}
        </div>
      </div>

      {/* Desktop table view */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[180px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('customerName')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by customer name ${sortConfig.field === 'customerName' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Customer
                    {getSortIcon('customerName')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[140px]">Resources</TableHead>
                <TableHead className="min-w-[240px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('start')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by date ${sortConfig.field === 'start' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Date & Time
                    {getSortIcon('start')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[160px]">Duration</TableHead>
                <TableHead className="min-w-[100px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('status')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by status ${sortConfig.field === 'status' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Status
                    {getSortIcon('status')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[100px]">Invoice</TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('createdAt')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by creation date ${sortConfig.field === 'createdAt' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Created
                    {getSortIcon('createdAt')}
                  </Button>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedBookings.map((booking) => {
                const invoiceStatus = getInvoiceStatusDisplay(booking)
                const duration = calculateTotalDuration(booking)
                const { periodsToShow, hasMore, moreCount } = formatPeriodsDisplay(booking)
                
                return (
                  <TableRow key={booking.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-3">
                        <div 
                          className="flex items-center justify-center w-8 h-8 rounded-full bg-muted"
                          aria-hidden="true"
                        >
                          <User className="h-4 w-4" />
                        </div>
                        <span>{booking.customer.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {booking.resources.length > 0 ? (
                          booking.resources.map((resource) => (
                            <Badge key={resource.id} variant="outline" className="text-xs">
                              {resource.name}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-muted-foreground text-sm">No resources</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {periodsToShow.length > 0 ? (
                          <>
                            {periodsToShow.map((period, index) => (
                              <div key={period.id} className="text-sm">
                                <div className="font-medium">
                                  {format(new Date(period.start), 'MMM dd, yyyy h:mm a')}
                                </div>
                                <div className="text-muted-foreground">
                                  to {format(new Date(period.end), 'h:mm a')}
                                  {period.parentPeriodId && (
                                    <Badge variant="outline" className="ml-2 text-xs">
                                      Recurring
                                    </Badge>
                                  )}
                                  {period.isRecurring && !period.parentPeriodId && (
                                    <Badge variant="outline" className="ml-2 text-xs">
                                      Series
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            ))}
                            {hasMore && (
                              <div className="text-sm text-muted-foreground italic">
                                ... and {moreCount} more period{moreCount !== 1 ? 's' : ''}
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-sm text-muted-foreground">No periods</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {duration}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="secondary" 
                        className={getStatusColor(booking.status)}
                      >
                        {BOOKING_STATUS_LABELS[booking.status]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${invoiceStatus.color}`}
                        >
                          {invoiceStatus.text}
                        </Badge>
                        {booking.invoice && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="text-xs cursor-help text-muted-foreground">
                                  {formatCurrency(booking.invoice.total)}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-xs">
                                  <p>Total: {formatCurrency(booking.invoice.total)}</p>
                                  <p>Paid: {formatCurrency(booking.invoice.paid)}</p>
                                  <p>Balance: {formatCurrency(booking.invoice.total - booking.invoice.paid)}</p>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-muted-foreground hidden lg:table-cell">
                      {booking.createdAt 
                        ? (() => {
                            try {
                              return format(new Date(booking.createdAt), 'yyyy-MM-dd');
                            } catch (error) {
                              return 'Invalid date';
                            }
                          })()
                        : 'N/A'
                      }
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            className="h-10 w-10 p-0 focus-enhanced"
                            aria-label={`Actions for booking ${booking.id} - ${booking.customer.name}`}
                          >
                            <span className="sr-only">
                              Open actions menu for booking {booking.id} - {booking.customer.name}
                            </span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48" role="menu">
                          <DropdownMenuItem 
                            onClick={() => handleEditBooking(booking)}
                            className="cursor-pointer focus-enhanced py-3"
                            role="menuitem"
                          >
                            <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                            Edit booking
                          </DropdownMenuItem>
                          
                          {!booking.invoice && booking.status !== 'CANCELLED' && (
                            <DropdownMenuItem
                              onClick={() => handleGenerateInvoiceBooking(booking.id)}
                              className="cursor-pointer focus-enhanced py-3"
                              role="menuitem"
                            >
                              <FileText className="mr-2 h-4 w-4" aria-hidden="true" />
                              {booking.status === 'PENDING' ? 'Confirm & Generate Invoice' : 'Generate invoice'}
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuItem
                            onClick={() => handleDeleteBooking(booking.id)}
                            className="text-destructive cursor-pointer focus-enhanced py-3"
                            role="menuitem"
                          >
                            <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                            Delete booking
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </div>
      </>
    </OfflineStateHandler>
  )
})

BookingsTable.displayName = "BookingsTable"