"use client";

import React, { memo, useState, useEffect, useCallback, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, X, User, AlertCircle } from "lucide-react";
import { useDebounce } from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

interface CustomerSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  debounceMs?: number;
  minSearchLength?: number;
  "aria-label"?: string;
}

export const CustomerSearch = memo(({
  searchQuery,
  onSearchChange,
  disabled = false,
  className,
  placeholder = "Search customers...",
  debounceMs = 300,
  minSearchLength = 3,
  "aria-label": ariaLabel = "Search bookings by customer name",
}: CustomerSearchProps) => {
  const [inputValue, setInputValue] = useState("");
  const debouncedSearchQuery = useDebounce(inputValue.trim(), debounceMs);

  // Update parent component when debounced value changes
  useEffect(() => {
    onSearchChange(debouncedSearchQuery);
  }, [debouncedSearchQuery, onSearchChange]);

  // Clear input when searchQuery is cleared externally
  useEffect(() => {
    if (searchQuery === '' && inputValue !== '') {
      setInputValue('');
    }
  }, [searchQuery]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
  }, []);

  const handleClear = useCallback(() => {
    setInputValue("");
    onSearchChange("");
  }, [onSearchChange]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      handleClear();
    }
  }, [handleClear]);

  // Show active search indicator
  const isActiveSearch = inputValue.trim().length >= minSearchLength;

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <div className="flex items-center space-x-1">
        <User className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
      </div>
      
      <div className="relative flex-1 min-w-[200px] max-w-[300px]">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" aria-hidden="true" />
          <Input
            type="text"
            placeholder={placeholder}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={disabled}
            className={cn(
              "pl-10 pr-10 h-9 text-sm transition-colors",
              isActiveSearch && "border-primary/50 bg-primary/5",
              "focus:ring-2 focus:ring-primary/20 focus:border-primary"
            )}
            aria-label={ariaLabel}
            role="searchbox"
            autoComplete="off"
            spellCheck="false"
          />
          
          {inputValue && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClear}
              disabled={disabled}
              className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 p-0 hover:bg-muted focus:ring-2 focus:ring-primary/20"
              aria-label="Clear customer search"
              tabIndex={0}
            >
              <X className="h-3 w-3" aria-hidden="true" />
              <span className="sr-only">Clear search</span>
            </Button>
          )}
        </div>

      </div>

      {/* Active search indicator */}
      {isActiveSearch && (
        <Badge 
          variant="outline" 
          className="bg-primary/10 text-primary border-primary/20 text-xs font-medium"
        >
          <Search className="h-3 w-3 mr-1" aria-hidden="true" />
          Searching
        </Badge>
      )}

      {/* Screen reader description for active search */}
      {isActiveSearch && (
        <span 
          id="customer-search-description" 
          className="sr-only"
        >
          Currently searching for customers matching "{inputValue.trim()}"
        </span>
      )}
    </div>
  );
});

CustomerSearch.displayName = "CustomerSearch";