"use client";

import React, { memo, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Filter } from "lucide-react";
import { BookingStatus } from "@/lib/types";
import { cn } from "@/lib/utils";

interface ClearFiltersButtonProps {
  // Filter states
  selectedStatus?: BookingStatus | 'ALL';
  selectedResource?: number | 'ALL';
  startDate?: Date;
  endDate?: Date;
  searchQuery?: string;
  
  // Actions
  onClearFilters: () => void;
  
  // UI props
  disabled?: boolean;
  className?: string;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "default" | "lg";
  showFilterCount?: boolean;
  "aria-label"?: string;
}

export const ClearFiltersButton = memo(({
  selectedStatus,
  selectedResource,
  startDate,
  endDate,
  searchQuery,
  onClearFilters,
  disabled = false,
  className,
  variant = "outline",
  size = "default",
  showFilterCount = true,
  "aria-label": ariaLabel = "Clear all applied filters",
}: ClearFiltersButtonProps) => {
  
  // Calculate active filters
  const activeFilters = useMemo(() => {
    const filters: string[] = [];
    
    // Status filter
    if (selectedStatus && selectedStatus !== 'ALL') {
      filters.push('Status');
    }
    
    // Resource filter
    if (selectedResource && selectedResource !== 'ALL') {
      filters.push('Resource');
    }
    
    // Date range filter
    if (startDate || endDate) {
      if (startDate && endDate) {
        filters.push('Date Range');
      } else if (startDate) {
        filters.push('Start Date');
      } else {
        filters.push('End Date');
      }
    }
    
    // Search query filter
    if (searchQuery && searchQuery.trim().length >= 3) {
      filters.push('Customer Search');
    }
    
    return filters;
  }, [selectedStatus, selectedResource, startDate, endDate, searchQuery]);

  const hasActiveFilters = activeFilters.length > 0;

  // Don't render if no filters are active
  if (!hasActiveFilters) {
    return null;
  }

  const handleClearFilters = () => {
    onClearFilters();
  };

  const getButtonText = () => {
    if (showFilterCount && activeFilters.length > 0) {
      return `Clear Filters (${activeFilters.length})`;
    }
    return "Clear Filters";
  };

  const getFilterSummary = () => {
    if (activeFilters.length === 0) return "";
    if (activeFilters.length === 1) return activeFilters[0];
    if (activeFilters.length === 2) return activeFilters.join(" and ");
    return `${activeFilters.slice(0, -1).join(", ")}, and ${activeFilters[activeFilters.length - 1]}`;
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Button
        variant={variant}
        size={size}
        onClick={handleClearFilters}
        disabled={disabled}
        className={cn(
          "transition-all duration-200",
          variant === "outline" && "border-destructive/50 text-destructive hover:bg-destructive/10 hover:border-destructive",
          variant === "ghost" && "text-destructive hover:bg-destructive/10",
          variant === "default" && "bg-destructive hover:bg-destructive/90"
        )}
        aria-label={ariaLabel}
        aria-describedby="clear-filters-description"
      >
        <X className="h-4 w-4 mr-2" aria-hidden="true" />
        <span>{getButtonText()}</span>
      </Button>

      {/* Active filters indicator */}
      {showFilterCount && activeFilters.length > 0 && (
        <div className="flex items-center space-x-1">
          <Filter className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
          <div className="flex flex-wrap gap-1">
            {activeFilters.map((filter, index) => (
              <Badge 
                key={index}
                variant="secondary" 
                className="text-xs bg-primary/10 text-primary border-primary/20"
              >
                {filter}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Screen reader description */}
      <span 
        id="clear-filters-description" 
        className="sr-only"
      >
        {activeFilters.length > 0 
          ? `Currently filtering by: ${getFilterSummary()}. Click to clear all filters and show all bookings.`
          : "No filters are currently applied."
        }
      </span>
    </div>
  );
});

ClearFiltersButton.displayName = "ClearFiltersButton";