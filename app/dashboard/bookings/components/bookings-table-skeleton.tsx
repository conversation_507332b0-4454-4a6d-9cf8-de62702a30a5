"use client"

import { memo, useEffect, useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Loader2, Calendar, Clock } from "lucide-react"

interface BookingsTableSkeletonProps {
  rows?: number
  showLoadingMessage?: boolean
  loadingText?: string
}

export const BookingsTableSkeleton = memo(({ 
  rows = 5, 
  showLoadingMessage = true,
  loadingText = "Loading bookings..."
}: BookingsTableSkeletonProps) => {
  const [loadingDots, setLoadingDots] = useState("");
  const [elapsedTime, setElapsedTime] = useState(0);

  // Animated loading dots
  useEffect(() => {
    const interval = setInterval(() => {
      setLoadingDots(prev => {
        if (prev === "...") return "";
        return prev + ".";
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  // Track loading time
  useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const getVariableWidth = (index: number, baseWidth: number) => {
    // Create varied skeleton widths for more realistic loading appearance
    const variations = [0.8, 1.0, 0.9, 1.1, 0.85];
    const variation = variations[index % variations.length];
    return Math.floor(baseWidth * variation);
  };

  return (
    <div className="space-y-4" role="status" aria-live="polite" aria-label="Loading bookings data">
      {/* Loading Status Header */}
      {showLoadingMessage && (
        <Card className="border-dashed">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                  <Loader2 className="w-4 h-4 text-blue-600 animate-spin" aria-hidden="true" />
                </div>
                <div>
                  <p className="font-medium text-sm">
                    {loadingText}{loadingDots}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Fetching booking data from server
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" aria-hidden="true" />
                  <span aria-label={`Loading for ${elapsedTime} seconds`}>{elapsedTime}s</span>
                </Badge>
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" aria-hidden="true" />
                  Loading
                </Badge>
              </div>
            </div>
          </CardHeader>
        </Card>
      )}

      {/* Mobile loading cards */}
      <div className="block md:hidden space-y-4" aria-label="Loading booking cards for mobile view">
        {Array.from({ length: rows }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-3">
                  {/* Customer name and status */}
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-8 w-8 rounded-full" aria-label="Customer avatar placeholder" />
                    <div className="flex-1 space-y-2">
                      <Skeleton 
                        className="h-5" 
                        style={{ width: `${getVariableWidth(index, 140)}px` }}
                        aria-label="Customer name placeholder"
                      />
                      <Skeleton className="h-4 w-20" aria-label="Booking status placeholder" />
                    </div>
                  </div>
                  
                  {/* Booking details */}
                  <div className="space-y-2 ml-11">
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-16" aria-label="Resources label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 120)}px` }}
                        aria-label="Resources list placeholder"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-12" aria-label="Date label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 100)}px` }}
                        aria-label="Date and time placeholder"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-16" aria-label="Duration label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 60)}px` }}
                        aria-label="Duration placeholder"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-14" aria-label="Invoice label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 80)}px` }}
                        aria-label="Invoice status placeholder"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-3 w-12" aria-label="Created label placeholder" />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 90)}px` }}
                        aria-label="Created date placeholder"
                      />
                    </div>
                  </div>
                </div>
                <Skeleton className="h-9 w-9 rounded-md" aria-label="Actions menu placeholder" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Desktop loading table */}
      <div className="hidden md:block rounded-md border" aria-label="Loading bookings table for desktop view">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[180px]">
                  <div className="flex items-center space-x-2">
                    <span>Customer</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[140px]">
                  <div className="flex items-center space-x-2">
                    <span>Resources</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[160px]">
                  <div className="flex items-center space-x-2">
                    <span>Date & Time</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[80px]">
                  <div className="flex items-center space-x-2">
                    <span>Duration</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <div className="flex items-center space-x-2">
                    <span>Status</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[100px]">
                  <div className="flex items-center space-x-2">
                    <span>Invoice</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <div className="flex items-center space-x-2">
                    <span>Created</span>
                    <Skeleton className="h-4 w-4" aria-label="Sort indicator placeholder" />
                  </div>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: rows }).map((_, index) => (
                <TableRow key={index} className="animate-pulse">
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-8 w-8 rounded-full" aria-label="Customer avatar placeholder" />
                      <Skeleton 
                        className="h-4" 
                        style={{ width: `${getVariableWidth(index, 120)}px` }}
                        aria-label="Customer name placeholder"
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      <Skeleton className="h-5 w-16 rounded-full" aria-label="Resource badge placeholder" />
                      <Skeleton className="h-5 w-20 rounded-full" aria-label="Resource badge placeholder" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Skeleton 
                        className="h-4" 
                        style={{ width: `${getVariableWidth(index, 100)}px` }}
                        aria-label="Date placeholder"
                      />
                      <Skeleton 
                        className="h-3" 
                        style={{ width: `${getVariableWidth(index, 80)}px` }}
                        aria-label="Time range placeholder"
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Skeleton 
                      className="h-4" 
                      style={{ width: `${getVariableWidth(index, 40)}px` }}
                      aria-label="Duration placeholder"
                    />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-20 rounded-full" aria-label="Status badge placeholder" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-16 rounded-full" aria-label="Invoice status placeholder" />
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <Skeleton 
                      className="h-4" 
                      style={{ width: `${getVariableWidth(index, 90)}px` }}
                      aria-label="Created date placeholder"
                    />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-8 w-8 rounded-md" aria-label="Actions menu placeholder" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Loading Progress Indicator */}
      {elapsedTime > 3 && (
        <Card className="border-yellow-200 bg-yellow-50" role="alert">
          <CardContent className="p-3">
            <div className="flex items-center space-x-2 text-sm text-yellow-800">
              <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
              <span>
                Taking longer than expected... 
                {elapsedTime > 10 && " Please check your connection."}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
})

BookingsTableSkeleton.displayName = "BookingsTableSkeleton"