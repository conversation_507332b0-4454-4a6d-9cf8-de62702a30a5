"use client";

import React, { memo, useMemo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Filter } from "lucide-react";
import { BookingStatus, BOOKING_STATUS_LABELS } from "@/lib/types";
import { cn } from "@/lib/utils";

interface BookingStatusFilterProps {
  selectedStatus: BookingStatus | 'ALL';
  onStatusChange: (status: BookingStatus | 'ALL') => void;
  disabled?: boolean;
  className?: string;
  "aria-label"?: string;
}

// Status colors for visual distinction
const STATUS_COLORS: Record<BookingStatus, string> = {
  PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
  CONFIRMED: "bg-green-100 text-green-800 border-green-200", 
  CANCELLED: "bg-red-100 text-red-800 border-red-200",
};

export const BookingStatusFilter = memo(({
  selectedStatus,
  onStatusChange,
  disabled = false,
  className,
  "aria-label": ariaLabel = "Filter bookings by status",
}: BookingStatusFilterProps) => {
  
  // Status options with proper labeling
  const statusOptions = useMemo(() => [
    { value: 'ALL', label: 'All Statuses', count: null },
    { value: 'PENDING', label: BOOKING_STATUS_LABELS.PENDING, count: null },
    { value: 'CONFIRMED', label: BOOKING_STATUS_LABELS.CONFIRMED, count: null },
    { value: 'CANCELLED', label: BOOKING_STATUS_LABELS.CANCELLED, count: null },
  ], []);

  const handleValueChange = (value: string) => {
    const newStatus = value === 'ALL' ? 'ALL' : value as BookingStatus;
    onStatusChange(newStatus);
  };

  const getDisplayValue = () => {
    if (selectedStatus === 'ALL') {
      return 'All Statuses';
    }
    return BOOKING_STATUS_LABELS[selectedStatus];
  };

  const getStatusBadge = (status: BookingStatus | 'ALL') => {
    if (status === 'ALL') {
      return null;
    }
    
    return (
      <Badge 
        variant="outline" 
        className={cn(
          "ml-2 text-xs font-medium border",
          STATUS_COLORS[status]
        )}
      >
        {BOOKING_STATUS_LABELS[status]}
      </Badge>
    );
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <div className="flex items-center space-x-1">
        <Filter className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
      </div>
      
      <Select 
        value={selectedStatus} 
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger 
          className={cn(
            "w-full h-9 text-sm",
            selectedStatus !== 'ALL' && "border-primary/50 bg-primary/5"
          )}
          aria-label={ariaLabel}
          aria-describedby={selectedStatus !== 'ALL' ? "status-filter-description" : undefined}
        >
          <SelectValue>
            <div className="flex items-center">
              <span>{getDisplayValue()}</span>
              {selectedStatus !== 'ALL' && getStatusBadge(selectedStatus)}
            </div>
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent>
          {statusOptions.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value}
              className="cursor-pointer"
            >
              <div className="flex items-center justify-between w-full">
                <span>{option.label}</span>
                {option.value !== 'ALL' && (
                  <Badge 
                    variant="outline" 
                    className={cn(
                      "ml-2 text-xs font-medium border",
                      STATUS_COLORS[option.value as BookingStatus]
                    )}
                  >
                    {option.value}
                  </Badge>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {/* Screen reader description for active filter */}
      {selectedStatus !== 'ALL' && (
        <span 
          id="status-filter-description" 
          className="sr-only"
        >
          Currently filtering by {BOOKING_STATUS_LABELS[selectedStatus]} bookings
        </span>
      )}
    </div>
  );
});

BookingStatusFilter.displayName = "BookingStatusFilter";