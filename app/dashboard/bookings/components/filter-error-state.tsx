"use client"

import { memo, useCallback, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  AlertTriangle,
  RefreshCw,
  Wifi,
  WifiOff,
  Clock,
  Server,
  Shield,
  ChevronDown,
  ChevronUp
} from "lucide-react"

interface FilterErrorStateProps {
  error: Error | string
  filterType: 'status' | 'resource' | 'date' | 'search' | 'general'
  onRetry: () => void
  loading?: boolean
  className?: string
  showDetails?: boolean
}

interface ErrorDetails {
  type: 'network' | 'server' | 'auth' | 'validation' | 'unknown'
  title: string
  description: string
  actionable: string
  icon: React.ComponentType<any>
  severity: 'low' | 'medium' | 'high'
}

const categorizeError = (error: Error | string, filterType: string): ErrorDetails => {
  const errorMessage = typeof error === 'string' ? error : error.message
  const lowerMessage = errorMessage.toLowerCase()

  // Network errors
  if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || 
      lowerMessage.includes('connection') || lowerMessage.includes('timeout') ||
      !navigator.onLine) {
    return {
      type: 'network',
      title: 'Connection Problem',
      description: navigator.onLine 
        ? `Unable to load ${filterType} data due to a network issue.`
        : 'You appear to be offline.',
      actionable: navigator.onLine 
        ? 'Check your internet connection and try again.'
        : 'Please check your internet connection.',
      icon: navigator.onLine ? Wifi : WifiOff,
      severity: 'high'
    }
  }

  // Server errors
  if (lowerMessage.includes('500') || lowerMessage.includes('502') || 
      lowerMessage.includes('503') || lowerMessage.includes('server')) {
    return {
      type: 'server',
      title: 'Server Error',
      description: `The server is having trouble loading ${filterType} data.`,
      actionable: 'Our team has been notified. Please try again in a moment.',
      icon: Server,
      severity: 'high'
    }
  }

  // Authentication errors
  if (lowerMessage.includes('401') || lowerMessage.includes('403') || 
      lowerMessage.includes('unauthorized') || lowerMessage.includes('forbidden')) {
    return {
      type: 'auth',
      title: 'Authentication Required',
      description: `You don't have permission to access ${filterType} data.`,
      actionable: 'Please refresh the page and try again.',
      icon: Shield,
      severity: 'medium'
    }
  }

  // Validation errors
  if (lowerMessage.includes('validation') || lowerMessage.includes('invalid') ||
      lowerMessage.includes('400')) {
    return {
      type: 'validation',
      title: 'Invalid Request',
      description: `There was a problem with the ${filterType} filter request.`,
      actionable: 'Please reset your filters and try again.',
      icon: AlertTriangle,
      severity: 'medium'
    }
  }

  // Unknown errors
  return {
    type: 'unknown',
    title: 'Unexpected Error',
    description: `Something went wrong while loading ${filterType} data.`,
    actionable: 'Please try again. If the problem persists, contact support.',
    icon: AlertTriangle,
    severity: 'low'
  }
}

const getSeverityColor = (severity: ErrorDetails['severity']) => {
  switch (severity) {
    case 'high':
      return 'border-red-200 bg-red-50 text-red-800'
    case 'medium':
      return 'border-yellow-200 bg-yellow-50 text-yellow-800'
    case 'low':
      return 'border-blue-200 bg-blue-50 text-blue-800'
    default:
      return 'border-gray-200 bg-gray-50 text-gray-800'
  }
}

const getRetryDelay = (attempt: number): number => {
  // Exponential backoff: 1s, 2s, 4s, 8s, max 30s
  return Math.min(1000 * Math.pow(2, attempt), 30000)
}

export const FilterErrorState = memo(({
  error,
  filterType,
  onRetry,
  loading = false,
  className = "",
  showDetails = false
}: FilterErrorStateProps) => {
  const [retryCount, setRetryCount] = useState(0)
  const [showExpandedDetails, setShowExpandedDetails] = useState(false)
  const [isRetrying, setIsRetrying] = useState(false)
  
  const errorDetails = categorizeError(error, filterType)
  const Icon = errorDetails.icon
  
  const handleRetry = useCallback(async () => {
    setIsRetrying(true)
    setRetryCount(prev => prev + 1)
    
    // Add exponential backoff delay for retries
    if (retryCount > 0) {
      const delay = getRetryDelay(retryCount - 1)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
    
    try {
      await onRetry()
    } finally {
      setIsRetrying(false)
    }
  }, [onRetry, retryCount])

  const getErrorId = useCallback(() => {
    if (typeof error === 'object' && error.stack) {
      // Generate a simple error ID from stack trace hash
      const stackHash = error.stack.slice(0, 100).split('').reduce(
        (hash, char) => hash + char.charCodeAt(0), 0
      ).toString(36)
      return `err_${Date.now().toString(36)}_${stackHash.slice(-6)}`
    }
    return null
  }, [error])

  const isDisabled = loading || isRetrying
  const retryButtonText = isRetrying ? 'Retrying...' : retryCount > 0 ? `Retry (${retryCount + 1})` : 'Retry'

  return (
    <TooltipProvider>
      <Card className={`border ${getSeverityColor(errorDetails.severity)} ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <Icon className="h-5 w-5" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h4 className="font-medium text-sm">{errorDetails.title}</h4>
                <Badge variant="outline" className={`text-xs ${getSeverityColor(errorDetails.severity)}`}>
                  {filterType} filter
                </Badge>
              </div>
              
              <p className="text-sm text-muted-foreground mb-2">
                {errorDetails.description}
              </p>
              
              <p className="text-xs text-muted-foreground mb-3">
                {errorDetails.actionable}
              </p>
              
              <div className="flex items-center gap-2 flex-wrap">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRetry}
                      disabled={isDisabled}
                      className="h-7"
                    >
                      <RefreshCw className={`h-3 w-3 mr-1 ${isRetrying ? 'animate-spin' : ''}`} />
                      {retryButtonText}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isDisabled ? 'Please wait...' : `Retry loading ${filterType} data`}</p>
                  </TooltipContent>
                </Tooltip>
                
                {retryCount > 0 && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{retryCount} attempt{retryCount !== 1 ? 's' : ''}</span>
                  </div>
                )}
                
                {showDetails && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowExpandedDetails(!showExpandedDetails)}
                    className="h-7 text-xs"
                  >
                    {showExpandedDetails ? (
                      <>
                        <ChevronUp className="h-3 w-3 mr-1" />
                        Hide Details
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-3 w-3 mr-1" />
                        Show Details
                      </>
                    )}
                  </Button>
                )}
              </div>
              
              {showExpandedDetails && showDetails && (
                <Alert className="mt-3">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    <div className="space-y-1">
                      <p><strong>Error Type:</strong> {errorDetails.type}</p>
                      <p><strong>Message:</strong> {typeof error === 'string' ? error : error.message}</p>
                      {getErrorId() && (
                        <p><strong>Error ID:</strong> {getErrorId()}</p>
                      )}
                      <p><strong>Time:</strong> {new Date().toLocaleString()}</p>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  )
})

FilterErrorState.displayName = "FilterErrorState"