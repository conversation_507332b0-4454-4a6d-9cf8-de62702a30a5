"use client"

import React, { memo, useCallback } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertTriangle, User, Calendar, Clock, MapPin, Receipt } from "lucide-react"
import { Booking, formatBookingDuration, formatCurrency } from "@/lib/types"
import { format } from "date-fns"

interface DeleteConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  booking: Booking | null
  onConfirm: () => Promise<void>
  loading?: boolean
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'CONFIRMED':
      return 'bg-green-100 border-green-300 text-green-800'
    case 'PENDING':
      return 'bg-yellow-100 border-yellow-300 text-yellow-800'
    case 'CANCELLED':
      return 'bg-red-100 border-red-300 text-red-800'
    default:
      return 'bg-gray-100 border-gray-300 text-gray-800'
  }
}

const getInvoiceStatusDisplay = (booking: Booking) => {
  if (!booking.invoice) {
    return { text: 'None', color: 'bg-gray-100 text-gray-600' }
  }
  
  switch (booking.invoice.status) {
    case 'PAID':
      return { text: 'Paid', color: 'bg-green-100 text-green-700' }
    case 'PARTIALLY_PAID':
      return { text: 'Partial', color: 'bg-yellow-100 text-yellow-700' }
    case 'PENDING':
      return { text: 'Pending', color: 'bg-blue-100 text-blue-700' }
    case 'CANCELLED':
      return { text: 'Cancelled', color: 'bg-red-100 text-red-700' }
    default:
      return { text: 'Unknown', color: 'bg-gray-100 text-gray-600' }
  }
}

export const DeleteConfirmationDialog = memo(({
  open,
  onOpenChange,
  booking,
  onConfirm,
  loading = false,
}: DeleteConfirmationDialogProps) => {
  if (!booking) return null

  const handleConfirm = useCallback(async () => {
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Delete confirmation error:', error)
    }
  }, [onConfirm, onOpenChange])

  const invoiceStatus = getInvoiceStatusDisplay(booking)
  const duration = booking.computedStart && booking.computedEnd 
    ? formatBookingDuration(booking.computedStart, booking.computedEnd)
    : 'No periods'
  const hasInvoice = !!booking.invoice

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <AlertDialogTitle>Delete Booking</AlertDialogTitle>
          </div>
          <AlertDialogDescription asChild>
            <div className="space-y-3">
              <p>
                Are you sure you want to delete this booking? This action cannot be undone.
              </p>
              
              <div className="rounded-lg border p-4 bg-muted/50">
                <h4 className="font-medium mb-3">Booking Details:</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Customer:</span>
                    </div>
                    <span className="font-medium">{booking.customer.name}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Date:</span>
                    </div>
                    <span className="font-medium">
                      {booking.computedStart ? format(new Date(booking.computedStart), 'MMM dd, yyyy') : 'No periods'}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Time:</span>
                    </div>
                    <span className="font-medium">
                      {booking.computedStart && booking.computedEnd 
                        ? `${format(new Date(booking.computedStart), 'h:mm a')} - ${format(new Date(booking.computedEnd), 'h:mm a')}`
                        : 'No periods'
                      }
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Duration:</span>
                    <span className="font-medium">{duration}</span>
                  </div>
                  
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Resources:</span>
                    </div>
                    <div className="flex flex-wrap gap-1 max-w-[200px] justify-end">
                      {booking.resources.length > 0 ? (
                        booking.resources.map((resource) => (
                          <Badge key={resource.id} variant="outline" className="text-xs">
                            {resource.name}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-muted-foreground text-xs">No resources</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Status:</span>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${getStatusColor(booking.status)}`}
                    >
                      {booking.status}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Receipt className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Invoice:</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${invoiceStatus.color}`}
                      >
                        {invoiceStatus.text}
                      </Badge>
                      {booking.invoice && (
                        <span className="text-xs text-muted-foreground">
                          ({formatCurrency(booking.invoice.total)})
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Created:</span>
                    <span className="font-medium">
                      {booking.createdAt 
                        ? format(new Date(booking.createdAt), 'MMM dd, yyyy')
                        : 'N/A'
                      }
                    </span>
                  </div>
                </div>
              </div>

              {hasInvoice ? (
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-destructive">
                        Cannot Delete Booking
                      </p>
                      <p className="text-xs text-destructive/80">
                        This booking has an associated invoice ({invoiceStatus.text.toLowerCase()}). 
                        Please remove the invoice first before deleting the booking.
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-destructive">
                        Warning: This action is permanent
                      </p>
                      <ul className="text-xs text-destructive/80 space-y-1">
                        <li>• The booking will be permanently removed from the system</li>
                        <li>• All booking data and history will be lost</li>
                        <li>• The time slot will become available for new bookings</li>
                        <li>• Any associated catering orders will also be removed</li>
                        <li>• This action cannot be reversed</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {!hasInvoice && (
                <p className="text-sm text-muted-foreground">
                  Please confirm that you want to permanently delete the booking for <strong>{booking.customer.name}</strong> on <strong>{booking.computedStart ? format(new Date(booking.computedStart), 'MMM dd, yyyy') : 'No periods'}</strong>.
                </p>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={loading}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading || hasInvoice}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {hasInvoice ? "Cannot Delete" : "Delete Booking"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
})

DeleteConfirmationDialog.displayName = "DeleteConfirmationDialog"