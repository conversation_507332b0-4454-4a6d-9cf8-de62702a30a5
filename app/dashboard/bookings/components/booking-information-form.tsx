"use client";

import { useEffect, useState, memo, useCallback, useMemo, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Loader2, 
  AlertCircle, 
  CheckCircle2,
  X, 
  Plus, 
  Minus, 
  Users, 
  Clock, 
  MapPin,
  AlertTriangle 
} from "lucide-react";
import { Booking, BookingFormData, Customer, Resource, Catering, BookingStatus, PeriodFormData, ConflictCheckResult } from "@/lib/types";
import { bookingCreateSchema, bookingUpdateSchema } from "@/lib/validations/booking";
import { 
  mapServerErrorsToForm, 
  getApiErrorMessage, 
  createErrorSummary,
  hasFormErrors 
} from "@/lib/utils/form-errors";
import { useCustomers } from "@/hooks/use-customers";
import { useResources } from "@/hooks/use-resources";
import { useCatering } from "@/hooks/use-catering";
import { useBookingOverlap } from "@/hooks/use-booking-overlap";
import { PeriodForm } from "@/components/periods/period-form";
import { ConflictCheckDisplay } from "@/components/periods/period-conflict-display";

interface BookingInformationFormProps {
  booking?: Booking | null;
  onSubmit: (data: BookingFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  submitButtonText?: string;
}

interface CateringSelection {
  cateringId: number;
  quantity: number;
  catering?: Catering;
}

export const BookingInformationForm = memo(({
  booking,
  onSubmit,
  onCancel,
  loading = false,
  submitButtonText = "Save Booking",
}: BookingInformationFormProps) => {
  const isEdit = !!booking;
  const schema = isEdit ? bookingUpdateSchema : bookingCreateSchema;
  const [serverError, setServerError] = useState<string>("");
  const [selectedResources, setSelectedResources] = useState<number[]>([]);
  const [cateringSelections, setCateringSelections] = useState<CateringSelection[]>([]);
  const [statusChanged, setStatusChanged] = useState(false);

  // Fetch data for dropdowns
  const { customers, fetchCustomers, loading: customersLoading } = useCustomers();
  const { resources, fetchResources, loading: resourcesLoading } = useResources();
  const { catering, fetchCatering, loading: cateringLoading } = useCatering();
  
  // Real-time overlap checking
  const { checkOverlap, debouncedCheckOverlap, checking: checkingOverlap, lastCheck, error: overlapError, clearResults } = useBookingOverlap();

  const defaultValues = useMemo(() => {
    return isEdit && booking ? {
      customerId: booking.customerId,
      resourceIds: booking.resources.map(r => r.id),
      status: booking.status,
      periods: booking.periods?.map(p => ({
        start: new Date(p.start),
        end: new Date(p.end),
        isRecurring: p.isRecurring,
        recurrenceRule: p.recurrenceRule || undefined
      })) || [],
      caterings: booking.caterings.map(c => ({
        cateringId: c.cateringId,
        quantity: c.quantity,
      })),
    } : {
      customerId: 0,
      resourceIds: [],
      status: "PENDING" as BookingStatus,
      periods: [],
      caterings: [],
    };
  }, [isEdit, booking]);

  const form = useForm<BookingFormData>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues,
  });

  // Reset form when editing booking loads/changes so periods and other fields populate correctly
  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  // Fetch data on component mount
  useEffect(() => {
    fetchCustomers();
    fetchResources();
    fetchCatering();
  }, [fetchCustomers, fetchResources, fetchCatering]); // Include function dependencies

  // Clear overlap results when resources change since PeriodForm handles conflict checking
  const watchedResourceIds = form.watch('resourceIds');
  
  useEffect(() => {
    clearResults();
  }, [watchedResourceIds]);

  // Initialize component state when booking data is available
  useEffect(() => {
    if (isEdit && booking) {
      setSelectedResources(booking.resources.map(r => r.id));
      
      // Initialize catering selections only if catering data is available
      if (booking.caterings.length > 0 && catering.length > 0) {
        const initialSelections = booking.caterings.map(c => {
          const cateringId = c.cateringId;
          const cateringObj = catering.find(cat => cat.id === cateringId);
          
          return {
            cateringId: cateringId,
            quantity: c.quantity,
            catering: cateringObj,
          };
        });
        
        setCateringSelections(initialSelections);
      } else {
        setCateringSelections([]);
      }
    } else if (!isEdit) {
      setSelectedResources([]);
      setCateringSelections([]);
    }
  }, [isEdit, booking, catering]); // Use the entire catering array as dependency

  // Sync catering selections with form field
  useEffect(() => {
    const cateringData = cateringSelections.map(cs => ({
      cateringId: cs.cateringId,
      quantity: cs.quantity,
    }));

    // Only update if the data has actually changed
    const currentValue = form.getValues("caterings");
    if (JSON.stringify(currentValue) !== JSON.stringify(cateringData)) {
      form.setValue("caterings", cateringData, { shouldValidate: true, shouldDirty: false });
    }
  }, [cateringSelections, form]);

  // Clear catering selections when booking status is not CONFIRMED
  // Use a ref to avoid recreating the subscription on every render
  const statusWatcherRef = useRef<any>(null);
  
  useEffect(() => {
    // Clean up previous subscription
    if (statusWatcherRef.current) {
      statusWatcherRef.current.unsubscribe();
    }
    
    statusWatcherRef.current = form.watch((value, { name, type }) => {
      // Only clear catering if the user actively changed the status (not during initial load)
      // and only if the form is dirty (user has made changes)
      if (name === 'status' && 
          value.status !== 'CONFIRMED' && 
          cateringSelections.length > 0 &&
          form.formState.isDirty &&
          type === 'change') {
        setCateringSelections([]);
        form.setValue("caterings", [], { shouldValidate: true, shouldDirty: true });
        setStatusChanged(true);
      }
    });

    return () => {
      if (statusWatcherRef.current) {
        statusWatcherRef.current.unsubscribe();
      }
    };
  }, [form]); // Depend on form instead of cateringSelections.length

  const handleSubmit = useCallback(async (data: BookingFormData) => {
    console.log('handleSubmit called with data:', data);
    setServerError("");

    try {
      // Validate that we have at least one valid period
      const validPeriods = data.periods.filter(p => p?.start && p?.end && p.start < p.end);
      console.log('Valid periods:', validPeriods);

      if (validPeriods.length === 0) {
        setServerError("At least one valid time period is required");
        return;
      }

      // Validate that resources are selected
      if (!data.resourceIds || data.resourceIds.length === 0) {
        setServerError("At least one resource must be selected");
        return;
      }

      // Ensure period dates are Date objects and sorted chronologically
      const sortedPeriods = [...validPeriods]
        .map(period => ({
          ...period,
          start: period.start instanceof Date ? period.start : new Date(period.start),
          end: period.end instanceof Date ? period.end : new Date(period.end),
        }))
        .sort((a, b) => a.start.getTime() - b.start.getTime());

      const formattedData: BookingFormData = {
        ...data,
        periods: sortedPeriods
      };

      console.log('Submitting formatted data:', formattedData);
      console.log('Calling onSubmit prop with formatted data...');
      await onSubmit(formattedData);
      console.log('onSubmit completed successfully');
    } catch (error) {
      console.error("Form submission error:", error);

      if (error && typeof error === 'object' && 'details' in error) {
        console.log('Mapping server errors to form:', error);
        mapServerErrorsToForm(error as any, form.setError as any);
      }

      const errorMessage = getApiErrorMessage(error);
      console.log('Setting server error:', errorMessage);
      setServerError(errorMessage);
    }
  }, [onSubmit, form]);

  // Handle resource selection
  const handleResourceToggle = useCallback((resourceId: number, checked: boolean) => {
    const newSelection = checked
      ? [...selectedResources, resourceId]
      : selectedResources.filter(id => id !== resourceId);

    setSelectedResources(newSelection);
    form.setValue("resourceIds", newSelection);
  }, [selectedResources, form]);

  // Memoize the periods change handler to prevent infinite re-renders
  const handlePeriodsChange = useCallback((periods: PeriodFormData[]) => {
    form.setValue('periods', periods, { shouldValidate: true, shouldDirty: true });
  }, [form]);

    // Memoize the conflict check handler
  const handleConflictCheck = useCallback(async (periods: PeriodFormData[]): Promise<ConflictCheckResult> => {
    console.log('handleConflictCheck called with:', { periods, selectedResources });

    if (selectedResources.length === 0) {
      const result = {
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No resources selected'
      };
      console.log('handleConflictCheck returning (no resources):', result);
      return result;
    }

    if (periods.length === 0) {
      const result = {
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: 'No periods to check'
      };
      console.log('handleConflictCheck returning (no periods):', result);
      return result;
    }

    try {
      // For recurring periods during creation, skip frontend conflict checking
      // The backend will do proper conflict checking with all generated periods
      const hasRecurringPeriods = periods.some(p => p.isRecurring && p.recurrenceRule);
      
      if (hasRecurringPeriods && !isEdit) {
        // During creation of recurring periods, return no conflicts
        // The backend will handle proper conflict detection with all generated periods
        const result = {
          hasConflicts: false,
          conflicts: [],
          affectedResources: [],
          message: 'Recurring periods will be checked during submission'
        };
        console.log('handleConflictCheck returning (recurring periods, creation mode):', result);
        return result;
      }

      const requestBody = {
        resourceIds: selectedResources,
        periods: periods.map(period => ({
          start: period.start instanceof Date ? period.start.toISOString() : new Date(period.start).toISOString(),
          end: period.end instanceof Date ? period.end.toISOString() : new Date(period.end).toISOString()
        })),
        excludeBookingId: isEdit ? booking?.id : undefined
      };

      console.log('Making conflict check request:', requestBody);

      // Use the new multiple periods format
      const response = await fetch('/api/bookings/check-overlap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Conflict check failed:', errorData);
        throw new Error(errorData.error || 'Failed to check conflicts');
      }

      const result = await response.json();

      // Normalize shape if the periods endpoint is used inadvertently
      if (result?.success && result?.data) {
        const data = result.data;
        return {
          hasConflicts: !!data.hasConflicts,
          conflicts: (data.conflicts || []).map((c: any) => ({
            conflictingPeriodId: c.conflictingPeriodId,
            conflictingBooking: c.conflictingBooking,
            conflictingResources: c.conflictingResources,
            overlapStart: c.overlap?.start ? new Date(c.overlap.start) : undefined,
            overlapEnd: c.overlap?.end ? new Date(c.overlap.end) : undefined,
            periodId: 0,
          })),
          affectedResources: data.affectedResources || [],
          message: data.summary?.message || 'Conflict check completed',
        } as ConflictCheckResult;
      }
      console.log('Conflict check API response:', result);

      const formattedResult = {
        hasConflicts: result.hasConflicts || false,
        // Ensure overlapStart/overlapEnd are Date objects for UI rendering
        conflicts: (result.conflicts || []).map((c: any) => ({
          ...c,
          overlapStart: c?.overlapStart ? new Date(c.overlapStart) : undefined,
          overlapEnd: c?.overlapEnd ? new Date(c.overlapEnd) : undefined,
        })),
        affectedResources: result.affectedResources || [],
        message: result.message || 'Conflict check completed'
      };

      console.log('handleConflictCheck returning:', formattedResult);
      return formattedResult;
    } catch (error) {
      console.error('Error checking conflicts:', error);
      const errorResult = {
        hasConflicts: false,
        conflicts: [],
        affectedResources: [],
        message: `Error checking conflicts: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
      console.log('handleConflictCheck returning error result:', errorResult);
      return errorResult;
    }
  }, [selectedResources, isEdit, booking?.id]);

  // Handle catering selection
  const addCateringSelection = useCallback(() => {
    if (catering.length > 0) {
      const availableCatering = catering.find(c => 
        !cateringSelections.some(cs => cs.cateringId === c.id)
      );
      
      if (availableCatering) {
        const newSelection: CateringSelection = {
          cateringId: availableCatering.id,
          quantity: 1,
          catering: availableCatering,
        };
        
        // Only update the state, the useEffect will handle form updates
        setCateringSelections(prev => [...prev, newSelection]);
      }
    }
  }, [catering, cateringSelections]);

  const removeCateringSelection = useCallback((index: number) => {
    // Only update the state, the useEffect will handle form updates
    setCateringSelections(prev => prev.filter((_, i) => i !== index));
  }, []);

  const updateCateringQuantity = useCallback((index: number, quantity: number) => {
    if (quantity < 1) return;
    
    // Only update the state, the useEffect will handle form updates
    setCateringSelections(prev => {
      const newSelections = [...prev];
      newSelections[index].quantity = quantity;
      return newSelections;
    });
  }, []);

  const updateCateringSelection = useCallback((index: number, cateringId: number) => {
    const selectedCatering = catering.find(c => c.id === cateringId);
    if (!selectedCatering) return;
    
    // Only update the state, the useEffect will handle form updates
    setCateringSelections(prev => {
      // Check if this catering is already selected in another index
      const existingIndex = prev.findIndex((cs, i) => i !== index && cs.cateringId === cateringId);
      
      const newSelections = [...prev];
      
      // If the catering is already selected elsewhere, swap them
      if (existingIndex !== -1) {
        const currentSelection = newSelections[index];
        newSelections[existingIndex] = {
          ...newSelections[existingIndex],
          cateringId: currentSelection.cateringId,
          catering: currentSelection.catering,
        };
      }
      
      newSelections[index] = {
        ...newSelections[index],
        cateringId,
        catering: selectedCatering,
      };
      
      return newSelections;
    });
  }, [catering]);

  const errorSummary = useMemo(() => 
    createErrorSummary(form.formState.errors, serverError), 
    [form.formState.errors, serverError]
  );
  
  const canSubmit = useMemo(() => {
    const periods = form.watch('periods') || [];
    const customerId = form.watch('customerId');
    const validPeriods = periods.filter(p => p?.start && p?.end && p.start < p.end);

    // Detect conflicts state from PeriodForm via last conflict check in form values (if attached)
    const noConflicts = true; // PeriodForm UI blocks submit via UX; server also checks

    const canSubmitResult = !loading && 
           customerId > 0 && 
           selectedResources.length > 0 && 
           validPeriods.length > 0 &&
           noConflicts &&
           !form.formState.isSubmitting;
    
    return canSubmitResult;
  }, [loading, selectedResources.length, form]);

  const availableCateringOptions = useMemo(() => 
    catering.filter(c => !cateringSelections.some(cs => cs.cateringId === c.id)),
    [catering, cateringSelections]
  );

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-6"
      >
        {/* Error Summary */}
        {errorSummary.hasErrors && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">{errorSummary.summary}</p>
                {errorSummary.messages.length > 1 && (
                  <ul className="list-disc list-inside text-sm space-y-1">
                    {errorSummary.messages.map((message, index) => (
                      <li key={index}>{message}</li>
                    ))}
                  </ul>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Status Change Warning */}
        {statusChanged && (
          <Alert variant="default" className="border-amber-200 bg-amber-50">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              <p className="font-medium">Catering options removed</p>
              <p className="text-sm">Catering is only available for confirmed bookings. The selected catering options have been removed due to the status change.</p>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setStatusChanged(false)}
                className="mt-2 h-auto p-0 text-amber-700 hover:text-amber-800"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        )}



        {/* Customer Selection */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Customer Information
          </h4>
          
          <FormField
            control={form.control}
            name="customerId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Customer *</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(parseInt(value))}
                  value={field.value ? field.value.toString() : ""}
                  disabled={loading || customersLoading}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a customer" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {customers.map((customer) => (
                      <SelectItem key={customer.id} value={customer.id.toString()}>
                        <div className="flex flex-col">
                          <span className="font-medium">{customer.name}</span>
                          <span className="text-sm text-gray-500">{customer.email}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="PENDING">
                      <Badge variant="outline" className="bg-yellow-100 border-yellow-300 text-yellow-800">
                        Pending
                      </Badge>
                    </SelectItem>
                    <SelectItem value="CONFIRMED">
                      <Badge variant="outline" className="bg-green-100 border-green-300 text-green-800">
                        Confirmed
                      </Badge>
                    </SelectItem>
                    <SelectItem value="CANCELLED">
                      <Badge variant="outline" className="bg-red-100 border-red-300 text-red-800">
                        Cancelled
                      </Badge>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Period Management */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Date and Time
          </h4>
          
          {selectedResources.length > 0 && (
            <Alert>
              <MapPin className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-medium">
                    All periods will apply to the following {selectedResources.length} resource{selectedResources.length !== 1 ? 's' : ''}:
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {resources
                      .filter(resource => selectedResources.includes(resource.id))
                      .map((resource) => (
                        <Badge key={resource.id} variant="outline">
                          {resource.name}
                        </Badge>
                      ))}
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}
          
          <FormField
            control={form.control}
            name="periods"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <PeriodForm
                    bookingId={booking?.id}
                    selectedResources={resources.filter(resource => selectedResources.includes(resource.id))}
                    existingPeriods={field.value || []}
                    onPeriodsChange={handlePeriodsChange}
                    onConflictCheck={handleConflictCheck}
                    mode={isEdit ? 'update' : 'create'}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Resource Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Resources *
            </h4>
            {selectedResources.length > 0 && (
              <Badge variant="outline" className="text-xs">
                {selectedResources.length} selected
              </Badge>
            )}
          </div>
          
          {selectedResources.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <p className="text-sm">
                  <strong>Note:</strong> All time periods you define will apply to all selected resources. 
                  Each period will be booked for every resource you select.
                </p>
              </AlertDescription>
            </Alert>
          )}
          
          {resourcesLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading resources...</span>
            </div>
          ) : (
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {resources.map((resource) => (
                    <div key={resource.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`resource-${resource.id}`}
                        checked={selectedResources.includes(resource.id)}
                        onCheckedChange={(checked) => 
                          handleResourceToggle(resource.id, checked as boolean)
                        }
                        disabled={loading}
                      />
                      <label
                        htmlFor={`resource-${resource.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                      >
                        <div className="flex flex-col">
                          <span>{resource.name}</span>
                          <span className="text-xs text-gray-500">
                            {resource.type}
                          </span>
                        </div>
                      </label>
                    </div>
                  ))}
                </div>
                
                {selectedResources.length === 0 && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Please select at least one resource. All time periods will be booked for every selected resource.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Booking Summary */}
        {selectedResources.length > 0 && form.watch('periods')?.length > 0 && (
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900 flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              Booking Summary
            </h4>
            
            <Alert>
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-medium">
                    You are booking {form.watch('periods')?.length || 0} time period{(form.watch('periods')?.length || 0) !== 1 ? 's' : ''} 
                    for {selectedResources.length} resource{selectedResources.length !== 1 ? 's' : ''}:
                  </p>
                  <div className="text-sm space-y-1">
                    <div>
                      <strong>Resources:</strong> {resources
                        .filter(resource => selectedResources.includes(resource.id))
                        .map(resource => resource.name)
                        .join(', ')}
                    </div>
                    <div>
                      <strong>Total bookings:</strong> {(form.watch('periods')?.length || 0) * selectedResources.length} 
                      (each period applies to all selected resources)
                    </div>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Catering Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">Catering Options</h4>
            {form.watch('status') === 'CONFIRMED' ? (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addCateringSelection}
                disabled={loading || cateringLoading || availableCateringOptions.length === 0}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Catering
              </Button>
            ) : (
              <div className="text-sm text-muted-foreground">
                Only available for confirmed bookings
              </div>
            )}
          </div>
          
          {form.watch('status') !== 'CONFIRMED' ? (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3 text-muted-foreground">
                  <AlertCircle className="h-5 w-5" />
                  <div>
                    <p className="font-medium">Catering requires confirmed booking</p>
                    <p className="text-sm">Please set the booking status to "Confirmed" to add catering options.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : cateringLoading ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="ml-2">Loading catering options...</span>
            </div>
          ) : (
            <div className="space-y-3">
              {cateringSelections.map((selection, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h6 className="text-sm font-medium flex items-center gap-2">
                          Catering Item {index + 1}
                        </h6>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCateringSelection(index)}
                          disabled={loading}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                          <label className="text-sm font-medium text-gray-700">Catering Offer *</label>
                          <Select
                            value={selection.cateringId?.toString() || ""}
                            onValueChange={(value) => 
                              updateCateringSelection(index, parseInt(value))
                            }
                            disabled={loading}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue 
                                placeholder="Select catering option"
                              />
                            </SelectTrigger>
                            <SelectContent>
                              {catering.map((cateringOption) => (
                                <SelectItem key={cateringOption.id} value={cateringOption.id.toString()}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{cateringOption.offerName}</span>
                                    <span className="text-sm text-muted-foreground">
                                      IQD {cateringOption.pricePerPerson}/person
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          
                        </div>
                        
                        <div>
                          <label className="text-sm font-medium text-gray-700">Quantity (People) *</label>
                          <div className="mt-1">
                            <Input
                              type="number"
                              min="1"
                              step="1"
                              value={selection.quantity}
                              onChange={(e) => {
                                const newQuantity = parseInt(e.target.value) || 1;
                                if (newQuantity >= 1) {
                                  updateCateringQuantity(index, newQuantity);
                                }
                              }}
                              disabled={loading}
                              className="w-full"
                              placeholder="1"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-gray-700">Unit Price</label>
                          <div className="mt-1 px-3 py-2 bg-gray-50 border rounded-md text-sm text-gray-600">
                            IQD {selection.catering?.pricePerPerson || 0}/person
                            {selection.catering && " (auto-filled from catering offer)"}
                          </div>
                        </div>
                      </div>
                      
                      {selection.catering && (
                        <div className="pt-2 border-t">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Line Total:</span>
                            <span className="font-bold text-lg">
                              IQD {(selection.catering.pricePerPerson * selection.quantity).toFixed(2)}
                            </span>
                          </div>
                          {/* Revenue Split Info (similar to invoice) */}
                          <div className="mt-2 text-xs text-muted-foreground">
                            <div className="space-y-1">
                              <div>Revenue Split: First Party IQD {(selection.catering.firstPartyShare * selection.quantity).toFixed(2)}, Vendor IQD {(selection.catering.vendorShare * selection.quantity).toFixed(2)}</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {cateringSelections.length === 0 && (
                <p className="text-sm text-gray-500 text-center py-4">
                  No catering selected. Click "Add Catering" to include catering options.
                </p>
              )}

              {/* Total Catering Cost */}
              {cateringSelections.length > 0 && (
                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-semibold">Total Catering Cost:</span>
                      </div>
                      <span className="text-2xl font-bold text-primary">
                        IQD {cateringSelections.reduce((total, selection) => 
                          total + (selection.catering?.pricePerPerson || 0) * selection.quantity, 0
                        ).toFixed(2)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
          {/* Form validation status */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2 sm:mb-0">
            {canSubmit ? (
              <div className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Form is valid
              </div>
            ) : hasFormErrors(form.formState.errors) || serverError || selectedResources.length === 0 ? (
              <div className="flex items-center text-red-600">
                <AlertCircle className="h-4 w-4 mr-1" />
                {selectedResources.length === 0 ? "Select resources" : `${errorSummary.errorCount} error${errorSummary.errorCount !== 1 ? 's' : ''}`}
              </div>
            ) : null}
          </div>
          
          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!canSubmit}
              className="min-w-[140px]"
              onClick={() => {
                console.log('Submit button clicked!', {
                  canSubmit,
                  loading,
                  formState: form.formState,
                  formValues: form.getValues()
                });
              }}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {submitButtonText}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
});

BookingInformationForm.displayName = "BookingInformationForm";