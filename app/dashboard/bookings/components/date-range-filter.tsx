"use client";

import React, { memo, useMemo, useCallback, useEffect, useState, useRef } from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, X, Filter } from "lucide-react";
import { DateRange } from "react-day-picker";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { useUrlDateState, useUrlMultiState } from "@/hooks/use-url-state";

interface DateRangeFilterProps {
  startDate?: Date;
  endDate?: Date;
  onDateRangeChange: (startDate?: Date, endDate?: Date) => void;
  disabled?: boolean;
  className?: string;
  "aria-label"?: string;
  enableUrlSync?: boolean; // Enable URL parameter synchronization
}

export const DateRangeFilter = memo(({
  startDate,
  endDate,
  onDateRangeChange,
  disabled = false,
  className,
  "aria-label": ariaLabel = "Filter bookings by date range",
  enableUrlSync = true,
}: DateRangeFilterProps) => {
  
  // URL state management for date range - store the raw dates (not time-adjusted)
  const [urlStartDate, setUrlStartDate] = useUrlDateState('startDate');
  const [urlEndDate, setUrlEndDate] = useUrlDateState('endDate');
  
  // Keep track of the original calendar dates for display (without time adjustments)
  const [calendarStartDate, setCalendarStartDate] = useState<Date | undefined>();
  const [calendarEndDate, setCalendarEndDate] = useState<Date | undefined>();

  // Use URL state if URL sync is enabled AND URL state has values, otherwise use props
  const effectiveStartDate = enableUrlSync ? (urlStartDate || startDate) : startDate;
  const effectiveEndDate = enableUrlSync ? (urlEndDate || endDate) : endDate;

  // Initialize URL state from props only once on mount when URL sync is enabled
  const [hasInitialized, setHasInitialized] = useState(false);
  
  useEffect(() => {
    if (enableUrlSync && !hasInitialized) {
      // Initialize URL state from props only if URL state is empty and props have values
      if (startDate && !urlStartDate) {
        setUrlStartDate(startDate);
      }
      if (endDate && !urlEndDate) {
        setUrlEndDate(endDate);
      }
      setHasInitialized(true);
    }
  }, [enableUrlSync, hasInitialized, startDate, endDate, urlStartDate, urlEndDate, setUrlStartDate, setUrlEndDate]);

  // Handle external clearing (when parent calls with undefined props)
  useEffect(() => {
    if (hasInitialized && !startDate && !endDate && (urlStartDate || urlEndDate || calendarStartDate || calendarEndDate)) {
      setCalendarStartDate(undefined);
      setCalendarEndDate(undefined);
      setUrlStartDate(undefined);
      setUrlEndDate(undefined);
    }
  }, [startDate, endDate, hasInitialized, urlStartDate, urlEndDate, calendarStartDate, calendarEndDate, setUrlStartDate, setUrlEndDate]);

  // When URL state changes (from user interaction), notify parent
  // This should only run when URL state actually changes, not when props change
  // Add a ref to track if we're in the middle of updating to prevent infinite loops
  const isUpdatingRef = useRef(false);
  
  useEffect(() => {
    if (enableUrlSync && hasInitialized && !isUpdatingRef.current) {
      // Set flag to prevent re-entry
      isUpdatingRef.current = true;
      onDateRangeChange(urlStartDate, urlEndDate);
      
      // Reset flag after a short delay
      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 100);
    }
  }, [urlStartDate, urlEndDate, enableUrlSync, hasInitialized, onDateRangeChange]);
  
  // Convert individual dates to DateRange format for the picker
  // Use calendar dates for display, fall back to effective dates if not available
  const dateRange: DateRange = useMemo(() => ({
    from: calendarStartDate || effectiveStartDate,
    to: calendarEndDate || effectiveEndDate,
  }), [calendarStartDate, calendarEndDate, effectiveStartDate, effectiveEndDate]);

  // Handle date range selection from the picker
  const handleDateRangeSelect = useCallback((range: DateRange | undefined) => {
    if (!range) {
      setCalendarStartDate(undefined);
      setCalendarEndDate(undefined);
      if (enableUrlSync) {
        setUrlStartDate(undefined);
        setUrlEndDate(undefined);
      } else {
        onDateRangeChange(undefined, undefined);
      }
      return;
    }

    // Check if we have proper from/to dates
    if (!range.from) {
      return;
    }

    // Store the original calendar dates for display
    setCalendarStartDate(range.from);
    setCalendarEndDate(range.to);
    
    if (enableUrlSync) {
      // Ensure we're working with proper date boundaries for filtering
      const startDate = range.from ? new Date(range.from.getFullYear(), range.from.getMonth(), range.from.getDate(), 0, 0, 0, 0) : range.from;
      const endDate = range.to ? new Date(range.to.getFullYear(), range.to.getMonth(), range.to.getDate(), 23, 59, 59, 999) : range.to;
      
      // Update dates in a single batch to avoid race conditions
      const updateBothDates = () => {
        setUrlStartDate(startDate);
        setUrlEndDate(endDate);
      };
      
      // Use requestAnimationFrame to batch the updates
      requestAnimationFrame(updateBothDates);
    } else {
      // Ensure we're working with proper date boundaries for filtering
      const startDate = range.from ? new Date(range.from.getFullYear(), range.from.getMonth(), range.from.getDate(), 0, 0, 0, 0) : range.from;
      const endDate = range.to ? new Date(range.to.getFullYear(), range.to.getMonth(), range.to.getDate(), 23, 59, 59, 999) : range.to;
      
      onDateRangeChange(startDate, endDate);
    }
  }, [onDateRangeChange, enableUrlSync, setUrlStartDate, setUrlEndDate]);

  // Clear the date range filter
  const handleClearDateRange = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setCalendarStartDate(undefined);
    setCalendarEndDate(undefined);
    if (enableUrlSync) {
      setUrlStartDate(undefined);
      setUrlEndDate(undefined);
    } else {
      onDateRangeChange(undefined, undefined);
    }
  }, [onDateRangeChange, enableUrlSync, setUrlStartDate, setUrlEndDate]);

  // Format display text for the button
  const getDisplayText = () => {
    if (!effectiveStartDate && !effectiveEndDate) {
      return "Select date range";
    }

    if (effectiveStartDate && effectiveEndDate) {
      return `${format(effectiveStartDate, "MMM dd, yyyy")} - ${format(effectiveEndDate, "MMM dd, yyyy")}`;
    }

    if (effectiveStartDate) {
      return `From ${format(effectiveStartDate, "MMM dd, yyyy")}`;
    }

    if (effectiveEndDate) {
      return `Until ${format(effectiveEndDate, "MMM dd, yyyy")}`;
    }

    return "Select date range";
  };

  // Check if any date filter is active
  const hasActiveFilter = effectiveStartDate || effectiveEndDate;

  // Get badge text for active filter
  const getBadgeText = () => {
    if (effectiveStartDate && effectiveEndDate) {
      return "Date Range";
    }
    if (effectiveStartDate) {
      return "From Date";
    }
    if (effectiveEndDate) {
      return "Until Date";
    }
    return null;
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "min-w-[240px] h-9 justify-start text-left font-normal",
              !hasActiveFilter && "text-muted-foreground",
              hasActiveFilter && "border-primary bg-primary/5 text-primary-foreground"
            )}
            disabled={disabled}
            aria-label={ariaLabel}
            aria-describedby={hasActiveFilter ? "date-filter-description" : undefined}
          >
            <CalendarIcon className="mr-2 h-4 w-4 shrink-0" aria-hidden="true" />
            <span className="truncate flex-1 text-sm">{getDisplayText()}</span>
            {hasActiveFilter && (
              <Badge 
                variant="secondary" 
                className="ml-2 text-xs font-medium shrink-0"
                data-testid="badge"
              >
                {getBadgeText()}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-4">
            <div className="mb-4">
              <h4 className="text-sm font-medium text-foreground mb-1">
                Filter by Date Range
              </h4>
              <p className="text-xs text-muted-foreground">
                Select start and/or end dates to filter bookings.
              </p>
            </div>
            
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={effectiveStartDate || new Date()}
              selected={dateRange}
              onSelect={handleDateRangeSelect}
              numberOfMonths={2}
              className="rounded-md"
            />
            
            <div className="flex items-center justify-between mt-4 pt-3 border-t">
              <div className="text-xs text-muted-foreground">
                {hasActiveFilter ? (
                  <span>
                    {effectiveStartDate && effectiveEndDate 
                      ? `${format(effectiveStartDate, "MMM dd")} - ${format(effectiveEndDate, "MMM dd, yyyy")}`
                      : effectiveStartDate 
                        ? `From ${format(effectiveStartDate, "MMM dd, yyyy")}`
                        : `Until ${format(effectiveEndDate!, "MMM dd, yyyy")}`
                    }
                  </span>
                ) : (
                  <span>No date filter applied</span>
                )}
              </div>
              
              {hasActiveFilter && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearDateRange}
                  className="h-7 px-2 text-xs"
                >
                  Clear
                </Button>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Clear button when filter is active */}
      {hasActiveFilter && (
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 hover:bg-destructive/10 shrink-0"
          onClick={handleClearDateRange}
          aria-label="Clear date filter"
        >
          <X className="h-4 w-4 text-muted-foreground hover:text-destructive" />
        </Button>
      )}
      
      {/* Screen reader description for active filter */}
      {hasActiveFilter && (
        <span 
          id="date-filter-description" 
          className="sr-only"
        >
          Currently filtering bookings {
            effectiveStartDate && effectiveEndDate 
              ? `from ${format(effectiveStartDate, "MMMM dd, yyyy")} to ${format(effectiveEndDate, "MMMM dd, yyyy")}`
              : effectiveStartDate 
                ? `from ${format(effectiveStartDate, "MMMM dd, yyyy")} onwards`
                : `until ${format(effectiveEndDate!, "MMMM dd, yyyy")}`
          }
        </span>
      )}
    </div>
  );
});

DateRangeFilter.displayName = "DateRangeFilter";