"use client"

import { useEffect, useState, use<PERSON><PERSON>back, memo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON>ltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  WifiOff,
  Wifi,
  RefreshCw,
  Clock,
  AlertCircle,
  CheckCircle2,
  Download
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface OfflineStateHandlerProps {
  children: React.ReactNode
  onConnectionRestored?: () => void
  showOfflineIndicator?: boolean
  enablePersistence?: boolean
}

interface ConnectionState {
  isOnline: boolean
  lastOnlineTime: Date | null
  retryCount: number
  isRetrying: boolean
}

interface OfflineAction {
  id: string
  type: string
  timestamp: Date
  data: any
  description: string
}

// Utility for managing offline actions queue
class OfflineQueue {
  private static instance: OfflineQueue
  private queue: OfflineAction[] = []
  private storageKey = 'bookings_offline_queue'

  public static getInstance(): OfflineQueue {
    if (!OfflineQueue.instance) {
      OfflineQueue.instance = new OfflineQueue()
    }
    return OfflineQueue.instance
  }

  constructor() {
    this.loadFromStorage()
  }

  public addAction(action: Omit<OfflineAction, 'id' | 'timestamp'>): void {
    const offlineAction: OfflineAction = {
      ...action,
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
      timestamp: new Date()
    }
    
    this.queue.push(offlineAction)
    this.saveToStorage()
  }

  public getQueue(): OfflineAction[] {
    return [...this.queue]
  }

  public removeAction(id: string): void {
    this.queue = this.queue.filter(action => action.id !== id)
    this.saveToStorage()
  }

  public clearQueue(): void {
    this.queue = []
    this.saveToStorage()
  }

  private loadFromStorage(): void {
    if (typeof window === 'undefined') return
    
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const parsed = JSON.parse(stored)
        this.queue = parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }))
      }
    } catch (error) {
      console.warn('Failed to load offline queue from storage:', error)
    }
  }

  private saveToStorage(): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.queue))
    } catch (error) {
      console.warn('Failed to save offline queue to storage:', error)
    }
  }
}

export const OfflineStateHandler = memo(({
  children,
  onConnectionRestored,
  showOfflineIndicator = true,
  enablePersistence = true
}: OfflineStateHandlerProps) => {
  const { toast } = useToast()
  const offlineQueue = enablePersistence ? OfflineQueue.getInstance() : null
  
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    isOnline: typeof window !== 'undefined' ? navigator.onLine : true,
    lastOnlineTime: typeof window !== 'undefined' && !navigator.onLine ? new Date() : null,
    retryCount: 0,
    isRetrying: false
  })
  
  const [queuedActions, setQueuedActions] = useState<OfflineAction[]>([])

  // Update queued actions when offline queue changes
  useEffect(() => {
    if (offlineQueue) {
      setQueuedActions(offlineQueue.getQueue())
    }
  }, [offlineQueue])

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      const wasOffline = !connectionState.isOnline
      
      setConnectionState(prev => ({
        ...prev,
        isOnline: true,
        retryCount: 0,
        isRetrying: false
      }))

      if (wasOffline) {
        toast({
          title: "Connection Restored",
          description: "You're back online! Syncing any pending changes...",
          className: "border-green-200 bg-green-50 text-green-900"
        })
        
        onConnectionRestored?.()
        
        // Process queued actions when coming back online
        if (offlineQueue && queuedActions.length > 0) {
          processQueuedActions()
        }
      }
    }

    const handleOffline = () => {
      setConnectionState(prev => ({
        ...prev,
        isOnline: false,
        lastOnlineTime: new Date(),
        retryCount: 0,
        isRetrying: false
      }))

      toast({
        variant: "destructive",
        title: "Connection Lost",
        description: "You're currently offline. Some features may be limited.",
        duration: 6000
      })
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [connectionState.isOnline, onConnectionRestored, toast, offlineQueue, queuedActions.length])

  const processQueuedActions = useCallback(async () => {
    if (!offlineQueue || queuedActions.length === 0) return

    toast({
      title: "Syncing Changes",
      description: `Processing ${queuedActions.length} pending action${queuedActions.length !== 1 ? 's' : ''}...`,
      className: "border-blue-200 bg-blue-50 text-blue-900"
    })

    let successCount = 0
    let failureCount = 0

    for (const action of queuedActions) {
      try {
        // Here you would implement the actual sync logic based on action type
        // For now, we'll simulate the process
        await new Promise(resolve => setTimeout(resolve, 500))
        
        offlineQueue.removeAction(action.id)
        successCount++
      } catch (error) {
        failureCount++
        console.error('Failed to process offline action:', action, error)
      }
    }

    // Update local state
    setQueuedActions(offlineQueue.getQueue())

    // Show completion message
    if (successCount > 0) {
      toast({
        title: "Sync Complete",
        description: `Successfully synced ${successCount} action${successCount !== 1 ? 's' : ''}${
          failureCount > 0 ? `. ${failureCount} failed and will retry later.` : '.'
        }`,
        className: "border-green-200 bg-green-50 text-green-900"
      })
    }
  }, [offlineQueue, queuedActions, toast])

  const handleRetryConnection = useCallback(async () => {
    setConnectionState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1
    }))

    // Simulate connection test
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)
      
      const response = await fetch('/api/health', {
        signal: controller.signal,
        cache: 'no-cache'
      })
      
      clearTimeout(timeoutId)
      
      if (response.ok) {
        // Connection successful
        setConnectionState(prev => ({
          ...prev,
          isOnline: true,
          isRetrying: false,
          retryCount: 0
        }))
        
        toast({
          title: "Connection Restored",
          description: "Successfully reconnected to the server.",
          className: "border-green-200 bg-green-50 text-green-900"
        })
        
        onConnectionRestored?.()
      } else {
        throw new Error('Server not responding')
      }
    } catch (error) {
      setConnectionState(prev => ({
        ...prev,
        isRetrying: false
      }))
      
      toast({
        variant: "destructive",
        title: "Connection Failed",
        description: "Still unable to connect. Please check your internet connection.",
      })
    }
  }, [onConnectionRestored, toast])

  const getOfflineDuration = useCallback(() => {
    if (!connectionState.lastOnlineTime) return null
    
    const now = new Date()
    const diff = now.getTime() - connectionState.lastOnlineTime.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    } else if (minutes > 0) {
      return `${minutes}m`
    } else {
      return 'Just now'
    }
  }, [connectionState.lastOnlineTime])

  // Don't show offline indicator if online or if disabled
  if (connectionState.isOnline || !showOfflineIndicator) {
    return <>{children}</>
  }

  return (
    <div className="space-y-4">
      {/* Offline Indicator */}
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <WifiOff className="h-5 w-5 text-red-600" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h4 className="font-medium text-sm text-red-800">You're Offline</h4>
                <Badge variant="outline" className="text-xs border-red-300 text-red-700">
                  No Connection
                </Badge>
              </div>
              
              <p className="text-sm text-red-700 mb-2">
                Some features may be limited while you're offline.
              </p>
              
              {connectionState.lastOnlineTime && (
                <p className="text-xs text-red-600 mb-3">
                  Last connected: {getOfflineDuration()} ago
                </p>
              )}
              
              <div className="flex items-center gap-2 flex-wrap">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRetryConnection}
                        disabled={connectionState.isRetrying}
                        className="h-7 border-red-300 text-red-700 hover:bg-red-100"
                      >
                        <RefreshCw className={`h-3 w-3 mr-1 ${connectionState.isRetrying ? 'animate-spin' : ''}`} />
                        {connectionState.isRetrying ? 'Checking...' : 'Retry Connection'}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Test connection to server</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                {connectionState.retryCount > 0 && (
                  <div className="flex items-center gap-1 text-xs text-red-600">
                    <Clock className="h-3 w-3" />
                    <span>{connectionState.retryCount} attempt{connectionState.retryCount !== 1 ? 's' : ''}</span>
                  </div>
                )}
                
                {enablePersistence && queuedActions.length > 0 && (
                  <div className="flex items-center gap-1 text-xs text-red-600">
                    <Download className="h-3 w-3" />
                    <span>{queuedActions.length} pending action{queuedActions.length !== 1 ? 's' : ''}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Queued Actions Info */}
      {enablePersistence && queuedActions.length > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            <strong>Offline Queue:</strong> {queuedActions.length} action{queuedActions.length !== 1 ? 's' : ''} will be synced when connection is restored.
            {queuedActions.slice(0, 3).map((action, index) => (
              <div key={action.id} className="text-xs text-muted-foreground mt-1">
                • {action.description}
              </div>
            ))}
            {queuedActions.length > 3 && (
              <div className="text-xs text-muted-foreground mt-1">
                • ...and {queuedActions.length - 3} more
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}
      
      {/* Content with reduced functionality */}
      <div className="opacity-75 pointer-events-auto">
        {children}
      </div>
    </div>
  )
})

OfflineStateHandler.displayName = "OfflineStateHandler"

// Hook for components to add actions to offline queue
export const useOfflineQueue = (enabled: boolean = true) => {
  const offlineQueue = enabled ? OfflineQueue.getInstance() : null
  
  const addOfflineAction = useCallback((
    type: string,
    data: any,
    description: string
  ) => {
    if (offlineQueue && !navigator.onLine) {
      offlineQueue.addAction({ type, data, description })
    }
  }, [offlineQueue])
  
  return {
    addOfflineAction,
    isOnline: navigator.onLine,
    queueSize: offlineQueue?.getQueue().length || 0
  }
}