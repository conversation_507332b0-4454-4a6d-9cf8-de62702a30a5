import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CustomerSearch } from '../customer-search';
import { vi } from 'vitest';

// Mock the debounce hook to return the value immediately for testing
vi.mock('@/hooks/use-debounce', () => ({
  useDebounce: vi.fn((value) => value),
}));

describe('CustomerSearch', () => {
  const mockOnSearchChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with default props', () => {
    render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
      />
    );

    expect(screen.getByRole('searchbox')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search customers...')).toBeInTheDocument();
    expect(screen.getByText('Customer:')).toBeInTheDocument();
  });

  it('displays the current search query', () => {
    render(
      <CustomerSearch
        searchQuery="John Doe"
        onSearchChange={mockOnSearchChange}
      />
    );

    expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
  });

  it('calls onSearchChange when typing valid search (3+ characters)', async () => {
    render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
      />
    );

    const input = screen.getByRole('searchbox');
    fireEvent.change(input, { target: { value: 'John' } });

    await waitFor(() => {
      expect(mockOnSearchChange).toHaveBeenCalledWith('John');
    });
  });

  it('does not call onSearchChange for searches less than 3 characters', async () => {
    render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
      />
    );

    const input = screen.getByRole('searchbox');
    fireEvent.change(input, { target: { value: 'Jo' } });

    // Should not call onSearchChange for short queries
    expect(mockOnSearchChange).not.toHaveBeenCalledWith('Jo');
  });

  it('clears search when clear button is clicked', async () => {
    render(
      <CustomerSearch
        searchQuery="John Doe"
        onSearchChange={mockOnSearchChange}
      />
    );

    const clearButton = screen.getByLabelText('Clear customer search');
    fireEvent.click(clearButton);

    expect(mockOnSearchChange).toHaveBeenCalledWith('');
  });

  it('clears search when Escape key is pressed', async () => {
    render(
      <CustomerSearch
        searchQuery="John Doe"
        onSearchChange={mockOnSearchChange}
      />
    );

    const input = screen.getByRole('searchbox');
    fireEvent.keyDown(input, { key: 'Escape' });

    expect(mockOnSearchChange).toHaveBeenCalledWith('');
  });

  it('respects custom minimum search length', async () => {
    render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
        minSearchLength={5}
      />
    );

    const input = screen.getByRole('searchbox');
    fireEvent.change(input, { target: { value: 'John' } });

    // Should not call onSearchChange for queries shorter than custom minimum
    expect(mockOnSearchChange).not.toHaveBeenCalledWith('John');
  });

  it('respects custom placeholder', () => {
    render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
        placeholder="Find customer..."
      />
    );

    expect(screen.getByPlaceholderText('Find customer...')).toBeInTheDocument();
  });

  it('can be disabled', () => {
    render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
        disabled={true}
      />
    );

    const input = screen.getByRole('searchbox');
    expect(input).toBeDisabled();
  });

  it('has proper accessibility attributes', () => {
    render(
      <CustomerSearch
        searchQuery="John"
        onSearchChange={mockOnSearchChange}
        aria-label="Search for customer bookings"
      />
    );

    const input = screen.getByRole('searchbox');
    expect(input).toHaveAttribute('aria-label', 'Search for customer bookings');
    expect(input).toHaveAttribute('aria-invalid', 'false');
  });

  it('trims whitespace from search queries', async () => {
    render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
      />
    );

    const input = screen.getByRole('searchbox');
    fireEvent.change(input, { target: { value: '  John  ' } });

    await waitFor(() => {
      expect(mockOnSearchChange).toHaveBeenCalledWith('John');
    });
  });
});