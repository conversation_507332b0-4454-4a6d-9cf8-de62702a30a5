import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { ClearFiltersButton } from '../clear-filters-button';
import { BookingStatus } from '@/lib/types';

describe('ClearFiltersButton', () => {
  const mockOnClearFilters = vi.fn();

  beforeEach(() => {
    mockOnClearFilters.mockClear();
  });

  it('should not render when no filters are active', () => {
    const { container } = render(
      <ClearFiltersButton
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(container.firstChild).toBeNull();
  });

  it('should render when status filter is active', () => {
    render(
      <ClearFiltersButton
        selectedStatus="CONFIRMED"
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.getByRole('button', { name: /clear all applied filters/i })).toBeInTheDocument();
    expect(screen.getByText('Clear Filters (1)')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
  });

  it('should render when resource filter is active', () => {
    render(
      <ClearFiltersButton
        selectedResource={123}
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.getByRole('button', { name: /clear all applied filters/i })).toBeInTheDocument();
    expect(screen.getByText('Clear Filters (1)')).toBeInTheDocument();
    expect(screen.getByText('Resource')).toBeInTheDocument();
  });

  it('should render when date range filter is active', () => {
    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-01-31');
    
    render(
      <ClearFiltersButton
        startDate={startDate}
        endDate={endDate}
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.getByRole('button', { name: /clear all applied filters/i })).toBeInTheDocument();
    expect(screen.getByText('Clear Filters (1)')).toBeInTheDocument();
    expect(screen.getByText('Date Range')).toBeInTheDocument();
  });

  it('should render when search query is active', () => {
    render(
      <ClearFiltersButton
        searchQuery="john doe"
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.getByRole('button', { name: /clear all applied filters/i })).toBeInTheDocument();
    expect(screen.getByText('Clear Filters (1)')).toBeInTheDocument();
    expect(screen.getByText('Customer Search')).toBeInTheDocument();
  });

  it('should not count search query with less than 3 characters', () => {
    render(
      <ClearFiltersButton
        searchQuery="jo"
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('should render with multiple active filters', () => {
    render(
      <ClearFiltersButton
        selectedStatus="PENDING"
        selectedResource={456}
        searchQuery="jane smith"
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.getByRole('button', { name: /clear all applied filters/i })).toBeInTheDocument();
    expect(screen.getByText('Clear Filters (3)')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Resource')).toBeInTheDocument();
    expect(screen.getByText('Customer Search')).toBeInTheDocument();
  });

  it('should call onClearFilters when clicked', () => {
    render(
      <ClearFiltersButton
        selectedStatus="CONFIRMED"
        onClearFilters={mockOnClearFilters}
      />
    );
    
    const button = screen.getByRole('button', { name: /clear all applied filters/i });
    fireEvent.click(button);
    
    expect(mockOnClearFilters).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    render(
      <ClearFiltersButton
        selectedStatus="CONFIRMED"
        onClearFilters={mockOnClearFilters}
        disabled={true}
      />
    );
    
    const button = screen.getByRole('button', { name: /clear all applied filters/i });
    expect(button).toBeDisabled();
  });

  it('should not show filter count when showFilterCount is false', () => {
    render(
      <ClearFiltersButton
        selectedStatus="CONFIRMED"
        onClearFilters={mockOnClearFilters}
        showFilterCount={false}
      />
    );
    
    expect(screen.getByText('Clear Filters')).toBeInTheDocument();
    expect(screen.queryByText('Clear Filters (1)')).not.toBeInTheDocument();
  });

  it('should handle start date only', () => {
    const startDate = new Date('2024-01-01');
    
    render(
      <ClearFiltersButton
        startDate={startDate}
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.getByText('Start Date')).toBeInTheDocument();
  });

  it('should handle end date only', () => {
    const endDate = new Date('2024-01-31');
    
    render(
      <ClearFiltersButton
        endDate={endDate}
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.getByText('End Date')).toBeInTheDocument();
  });

  it('should not count ALL status as active filter', () => {
    render(
      <ClearFiltersButton
        selectedStatus="ALL"
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('should not count ALL resource as active filter', () => {
    render(
      <ClearFiltersButton
        selectedResource="ALL"
        onClearFilters={mockOnClearFilters}
      />
    );
    
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });
});