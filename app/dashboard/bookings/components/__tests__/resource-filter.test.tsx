import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ResourceFilter } from '../resource-filter';

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock the retry utility
vi.mock('@/lib/utils/retry', () => ({
  retryApiCall: vi.fn(),
}));

// Mock fetch
global.fetch = vi.fn();

describe('ResourceFilter', () => {
  const mockOnResourceChange = vi.fn();
  const mockResources = [
    {
      id: 1,
      name: 'Conference Room A',
      type: 'MEETING_ROOM',
      basePrice: 100,
      amenities: [],
      stageStyles: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 2,
      name: 'Event Hall B',
      type: 'INDOOR_EVENT_HALL',
      basePrice: 500,
      amenities: [],
      stageStyles: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // Mock successful API response
    const { retryApiCall } = await import('@/lib/utils/retry');
    const mockRetryApiCall = vi.mocked(retryApiCall);
    mockRetryApiCall.mockResolvedValue({
      json: () => Promise.resolve({
        success: true,
        data: {
          data: mockResources,
        },
      }),
    } as any);
  });

  it('renders loading state initially', () => {
    render(
      <ResourceFilter
        selectedResource="ALL"
        onResourceChange={mockOnResourceChange}
      />
    );

    expect(screen.getByText('Resource:')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders resources after loading', async () => {
    render(
      <ResourceFilter
        selectedResource="ALL"
        onResourceChange={mockOnResourceChange}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('All Resources')).toBeInTheDocument();
    });
  });

  it('displays selected resource correctly', async () => {
    render(
      <ResourceFilter
        selectedResource={1}
        onResourceChange={mockOnResourceChange}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Conference Room A')).toBeInTheDocument();
    });
  });

  it('can be disabled', async () => {
    render(
      <ResourceFilter
        selectedResource="ALL"
        onResourceChange={mockOnResourceChange}
        disabled={true}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Resource:')).toBeInTheDocument();
    });
  });

  it('handles API error gracefully', async () => {
    const { retryApiCall } = await import('@/lib/utils/retry');
    const mockRetryApiCall = vi.mocked(retryApiCall);
    mockRetryApiCall.mockRejectedValue(new Error('API Error'));

    render(
      <ResourceFilter
        selectedResource="ALL"
        onResourceChange={mockOnResourceChange}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load')).toBeInTheDocument();
    });
  });
});