/**
 * @vitest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { DateRangeFilter } from '../date-range-filter';

// Mock the URL state hook
vi.mock('@/hooks/use-url-state', () => ({
  useUrlDateState: vi.fn(() => [undefined, vi.fn()]),
}));

// Mock the UI components
vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/calendar', () => ({
  Calendar: ({ onSelect, selected }: any) => (
    <div data-testid="calendar">
      <button
        onClick={() => onSelect({ from: new Date('2024-01-01'), to: new Date('2024-01-07') })}
      >
        Select Range
      </button>
      <button onClick={() => onSelect(undefined)}>Clear</button>
    </div>
  ),
}));

vi.mock('@/components/ui/popover', () => ({
  Popover: ({ children }: any) => <div>{children}</div>,
  PopoverTrigger: ({ children }: any) => <div>{children}</div>,
  PopoverContent: ({ children }: any) => <div data-testid="popover-content">{children}</div>,
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: any) => <span data-testid="badge">{children}</span>,
}));

vi.mock('lucide-react', () => ({
  Calendar: () => <span data-testid="calendar-icon">📅</span>,
  Filter: () => <span data-testid="filter-icon">🔍</span>,
  X: () => <span data-testid="x-icon">❌</span>,
}));

describe('DateRangeFilter', () => {
  const mockOnDateRangeChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <DateRangeFilter
        onDateRangeChange={mockOnDateRangeChange}
      />
    );

    expect(screen.getByText('Select date range')).toBeInTheDocument();
    expect(screen.getByText('Filter by Date Range')).toBeInTheDocument();
  });

  it('displays start date only when provided', () => {
    const startDate = new Date('2024-01-01');
    
    render(
      <DateRangeFilter
        startDate={startDate}
        onDateRangeChange={mockOnDateRangeChange}
        enableUrlSync={false}
      />
    );

    expect(screen.getAllByText(/From Jan 01, 2024/)).toHaveLength(2); // Button and popover
  });

  it('displays end date only when provided', () => {
    const endDate = new Date('2024-01-07');
    
    render(
      <DateRangeFilter
        endDate={endDate}
        onDateRangeChange={mockOnDateRangeChange}
        enableUrlSync={false}
      />
    );

    expect(screen.getAllByText(/Until Jan 07, 2024/)).toHaveLength(2); // Button and popover
  });

  it('displays date range when both dates provided', () => {
    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-01-07');
    
    render(
      <DateRangeFilter
        startDate={startDate}
        endDate={endDate}
        onDateRangeChange={mockOnDateRangeChange}
        enableUrlSync={false}
      />
    );

    expect(screen.getByText(/Jan 01, 2024 - Jan 07, 2024/)).toBeInTheDocument();
  });

  it('shows badge when filter is active', () => {
    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-01-07');
    
    render(
      <DateRangeFilter
        startDate={startDate}
        endDate={endDate}
        onDateRangeChange={mockOnDateRangeChange}
        enableUrlSync={false}
      />
    );

    expect(screen.getByTestId('badge')).toBeInTheDocument();
    expect(screen.getByText('Date Range')).toBeInTheDocument();
  });

  it('shows correct badge text for start date only', () => {
    const startDate = new Date('2024-01-01');
    
    render(
      <DateRangeFilter
        startDate={startDate}
        onDateRangeChange={mockOnDateRangeChange}
        enableUrlSync={false}
      />
    );

    expect(screen.getByText('From Date')).toBeInTheDocument();
  });

  it('shows correct badge text for end date only', () => {
    const endDate = new Date('2024-01-07');
    
    render(
      <DateRangeFilter
        endDate={endDate}
        onDateRangeChange={mockOnDateRangeChange}
        enableUrlSync={false}
      />
    );

    expect(screen.getByText('Until Date')).toBeInTheDocument();
  });

  it('can be disabled', () => {
    render(
      <DateRangeFilter
        onDateRangeChange={mockOnDateRangeChange}
        disabled={true}
      />
    );

    const button = screen.getByLabelText('Filter bookings by date range');
    expect(button).toBeDisabled();
  });

  it('calls onDateRangeChange when calendar selection changes', async () => {
    render(
      <DateRangeFilter
        onDateRangeChange={mockOnDateRangeChange}
        enableUrlSync={false}
      />
    );

    const selectRangeButton = screen.getByText('Select Range');
    fireEvent.click(selectRangeButton);

    await waitFor(() => {
      expect(mockOnDateRangeChange).toHaveBeenCalledWith(
        new Date('2024-01-01'),
        new Date('2024-01-07')
      );
    });
  });

  it('calls onDateRangeChange with undefined when calendar is cleared', async () => {
    render(
      <DateRangeFilter
        startDate={new Date('2024-01-01')}
        endDate={new Date('2024-01-07')}
        onDateRangeChange={mockOnDateRangeChange}
        enableUrlSync={false}
      />
    );

    const clearButtons = screen.getAllByText('Clear');
    fireEvent.click(clearButtons[0]); // Click the first clear button (calendar)

    await waitFor(() => {
      expect(mockOnDateRangeChange).toHaveBeenCalledWith(undefined, undefined);
    });
  });

  it('has proper accessibility attributes', () => {
    render(
      <DateRangeFilter
        startDate={new Date('2024-01-01')}
        onDateRangeChange={mockOnDateRangeChange}
        enableUrlSync={false}
      />
    );

    const button = screen.getByLabelText('Filter bookings by date range');
    expect(button).toHaveAttribute('aria-label', 'Filter bookings by date range');
    expect(button).toHaveAttribute('aria-describedby', 'date-filter-description');
    
    // Check for screen reader description
    expect(screen.getByText(/Currently filtering bookings from/)).toHaveClass('sr-only');
  });

  it('applies custom className', () => {
    const { container } = render(
      <DateRangeFilter
        onDateRangeChange={mockOnDateRangeChange}
        className="custom-class"
      />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('accepts custom aria-label', () => {
    render(
      <DateRangeFilter
        onDateRangeChange={mockOnDateRangeChange}
        aria-label="Custom date filter"
      />
    );

    const button = screen.getByLabelText('Custom date filter');
    expect(button).toHaveAttribute('aria-label', 'Custom date filter');
  });
});