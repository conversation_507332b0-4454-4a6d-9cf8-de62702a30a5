import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CustomerSearch } from '../customer-search';
import { vi } from 'vitest';

// Mock the debounce hook
vi.mock('@/hooks/use-debounce', () => ({
  useDebounce: vi.fn((value) => value),
}));

describe('CustomerSearch Integration', () => {
  it('integrates correctly with useBookings hook pattern', async () => {
    const mockOnSearchChange = vi.fn();
    
    // Simulate the useBookings hook interface
    const searchQuery = '';
    
    render(
      <CustomerSearch
        searchQuery={searchQuery}
        onSearchChange={mockOnSearchChange}
      />
    );

    const input = screen.getByRole('searchbox');
    
    // Test that it calls the hook's setSearchQuery function
    fireEvent.change(input, { target: { value: '<PERSON>' } });

    await waitFor(() => {
      expect(mockOnSearchChange).toHaveBeenCalledWith('<PERSON>');
    });
  });

  it('handles search query updates from external source', () => {
    const mockOnSearchChange = vi.fn();
    
    const { rerender } = render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
      />
    );

    // Simulate external update (like from URL or other component)
    rerender(
      <CustomerSearch
        searchQuery="Jane Smith"
        onSearchChange={mockOnSearchChange}
      />
    );

    expect(screen.getByDisplayValue('Jane Smith')).toBeInTheDocument();
  });

  it('resets pagination when search changes (verified through hook integration)', async () => {
    const mockOnSearchChange = vi.fn();
    
    render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
      />
    );

    const input = screen.getByRole('searchbox');
    fireEvent.change(input, { target: { value: 'Test Customer' } });

    await waitFor(() => {
      // The useBookings hook handles pagination reset
      // This test verifies the component calls the hook correctly
      expect(mockOnSearchChange).toHaveBeenCalledWith('Test Customer');
    });
  });

  it('provides proper debouncing behavior', async () => {
    const mockOnSearchChange = vi.fn();
    
    render(
      <CustomerSearch
        searchQuery=""
        onSearchChange={mockOnSearchChange}
        debounceMs={100}
      />
    );

    const input = screen.getByRole('searchbox');
    
    // Type multiple characters quickly
    fireEvent.change(input, { target: { value: 'J' } });
    fireEvent.change(input, { target: { value: 'Jo' } });
    fireEvent.change(input, { target: { value: 'John' } });

    // Should only call once with the final value due to debouncing
    await waitFor(() => {
      expect(mockOnSearchChange).toHaveBeenCalledWith('John');
    });
  });
});