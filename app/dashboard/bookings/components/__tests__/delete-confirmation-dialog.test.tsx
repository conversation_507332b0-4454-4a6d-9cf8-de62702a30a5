import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { DeleteConfirmationDialog } from '../delete-confirmation-dialog'
import { Booking } from '@/lib/types'

// Mock booking data
const mockBooking: Booking = {
  id: 1,
  customer: {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phoneNumber: '************',
    companyName: 'Test Company',
    createdAt: new Date(),
    updatedAt: new Date(),
    bookings: []
  },
  customerId: 1,
  resources: [
    {
      id: 1,
      name: 'Conference Room A',
      type: 'ROOM',
      capacity: 10,
      hourlyRate: 50,
      amenities: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],
  status: 'PENDING',
  start: new Date('2024-01-15T10:00:00Z'),
  end: new Date('2024-01-15T12:00:00Z'),
  invoice: null,
  caterings: [],
  createdAt: new Date('2024-01-10T08:00:00Z'),
  updatedAt: new Date('2024-01-10T08:00:00Z'),
  createdById: null,
  updatedById: null
}

const mockBookingWithInvoice: Booking = {
  ...mockBooking,
  invoice: {
    id: 1,
    customerId: 1,
    customer: mockBooking.customer,
    bookingId: 1,
    booking: mockBooking,
    status: 'PENDING',
    total: 100,
    paid: 0,
    lineItems: [],
    payments: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdById: null,
    updatedById: null
  }
}

describe('DeleteConfirmationDialog', () => {
  it('renders without crashing when booking is provided', () => {
    const mockOnConfirm = vi.fn()
    const mockOnOpenChange = vi.fn()

    render(
      <DeleteConfirmationDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        booking={mockBooking}
        onConfirm={mockOnConfirm}
        loading={false}
      />
    )

    expect(screen.getByRole('heading', { name: 'Delete Booking' })).toBeInTheDocument()
    expect(screen.getAllByText('John Doe')).toHaveLength(2) // Appears in details and confirmation text
    expect(screen.getByText('Conference Room A')).toBeInTheDocument()
  })

  it('does not render when booking is null', () => {
    const mockOnConfirm = vi.fn()
    const mockOnOpenChange = vi.fn()

    const { container } = render(
      <DeleteConfirmationDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        booking={null}
        onConfirm={mockOnConfirm}
        loading={false}
      />
    )

    expect(container.firstChild).toBeNull()
  })

  it('shows warning when booking has no invoice', () => {
    const mockOnConfirm = vi.fn()
    const mockOnOpenChange = vi.fn()

    render(
      <DeleteConfirmationDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        booking={mockBooking}
        onConfirm={mockOnConfirm}
        loading={false}
      />
    )

    expect(screen.getByText('Warning: This action is permanent')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Delete Booking' })).toBeInTheDocument()
  })

  it('prevents deletion when booking has invoice', () => {
    const mockOnConfirm = vi.fn()
    const mockOnOpenChange = vi.fn()

    render(
      <DeleteConfirmationDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        booking={mockBookingWithInvoice}
        onConfirm={mockOnConfirm}
        loading={false}
      />
    )

    expect(screen.getByText('Cannot Delete Booking')).toBeInTheDocument()
    expect(screen.getByText('Cannot Delete')).toBeInTheDocument()
    
    const deleteButton = screen.getByText('Cannot Delete')
    expect(deleteButton).toBeDisabled()
  })

  it('calls onConfirm when delete is confirmed', async () => {
    const mockOnConfirm = vi.fn().mockResolvedValue(undefined)
    const mockOnOpenChange = vi.fn()

    render(
      <DeleteConfirmationDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        booking={mockBooking}
        onConfirm={mockOnConfirm}
        loading={false}
      />
    )

    const deleteButton = screen.getByRole('button', { name: 'Delete Booking' })
    fireEvent.click(deleteButton)

    await waitFor(() => {
      expect(mockOnConfirm).toHaveBeenCalledTimes(1)
    })
  })

  it('shows loading state when loading is true', () => {
    const mockOnConfirm = vi.fn()
    const mockOnOpenChange = vi.fn()

    render(
      <DeleteConfirmationDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        booking={mockBooking}
        onConfirm={mockOnConfirm}
        loading={true}
      />
    )

    const deleteButton = screen.getByRole('button', { name: 'Delete Booking' })
    expect(deleteButton).toBeDisabled()
  })

  it('calls onOpenChange when cancel is clicked', () => {
    const mockOnConfirm = vi.fn()
    const mockOnOpenChange = vi.fn()

    render(
      <DeleteConfirmationDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        booking={mockBooking}
        onConfirm={mockOnConfirm}
        loading={false}
      />
    )

    const cancelButton = screen.getByText('Cancel')
    fireEvent.click(cancelButton)

    expect(mockOnOpenChange).toHaveBeenCalledWith(false)
  })
})