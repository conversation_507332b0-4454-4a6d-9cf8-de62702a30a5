import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { BookingStatusFilter } from '../booking-status-filter';

describe('BookingStatusFilter', () => {
  const mockOnStatusChange = vi.fn();

  it('renders without crashing', () => {
    render(
      <BookingStatusFilter
        selectedStatus="ALL"
        onStatusChange={mockOnStatusChange}
      />
    );

    expect(screen.getByText('Status:')).toBeInTheDocument();
    expect(screen.getByText('All Statuses')).toBeInTheDocument();
  });

  it('displays correct status when selected', () => {
    const { rerender } = render(
      <BookingStatusFilter
        selectedStatus="PENDING"
        onStatusChange={mockOnStatusChange}
      />
    );

    expect(screen.getAllByText('Pending')).toHaveLength(2); // One in display, one in badge

    rerender(
      <BookingStatusFilter
        selectedStatus="CONFIRMED"
        onStatusChange={mockOnStatusChange}
      />
    );

    expect(screen.getAllByText('Confirmed')).toHaveLength(2); // One in display, one in badge
  });

  it('can be disabled', () => {
    render(
      <BookingStatusFilter
        selectedStatus="ALL"
        onStatusChange={mockOnStatusChange}
        disabled={true}
      />
    );

    // Component should still render when disabled
    expect(screen.getByText('Status:')).toBeInTheDocument();
  });
});