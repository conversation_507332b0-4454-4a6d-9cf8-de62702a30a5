import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { vi, describe, it, beforeEach, expect } from 'vitest'
import { BookingCard } from '../booking-card'
import { Booking, BookingStatus } from '@/lib/types'

// Mock the date-fns format function
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => {
    if (formatStr === 'MMM dd, yyyy') return 'Jan 15, 2024'
    if (formatStr === 'h:mm a') return '10:00 AM'
    return 'Jan 15, 2024'
  })
}))

const mockBooking: Booking = {
  id: 1,
  customer: {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10')
  },
  customerId: 1,
  resources: [
    {
      id: 1,
      name: 'Conference Room A',
      type: 'MEETING_ROOM',
      basePrice: 50000,
      amenities: [],
      stageStyles: [],
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    }
  ],
  status: 'CONFIRMED' as BookingStatus,
  start: new Date('2024-01-15T10:00:00'),
  end: new Date('2024-01-15T12:00:00'),
  invoice: {
    id: 1,
    bookingId: 1,
    status: 'PAID',
    total: 100000,
    paid: 100000,
    lineItems: [],
    payments: [],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  } as any,
  caterings: [],
  createdAt: new Date('2024-01-10'),
  updatedAt: new Date('2024-01-10')
}

const mockProps = {
  booking: mockBooking,
  onEdit: vi.fn(),
  onDelete: vi.fn(),
  onGenerateInvoice: vi.fn()
}

describe('BookingCard', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders booking information correctly', () => {
    render(<BookingCard {...mockProps} />)
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Confirmed')).toBeInTheDocument()
    expect(screen.getByText('Conference Room A')).toBeInTheDocument()
    expect(screen.getByText('Jan 15, 2024')).toBeInTheDocument()
    expect(screen.getByText('10:00 AM - 10:00 AM')).toBeInTheDocument()
    expect(screen.getByText('Paid')).toBeInTheDocument()
  })

  it('calls onEdit when edit action is clicked', () => {
    render(<BookingCard {...mockProps} />)
    
    const actionsButton = screen.getByLabelText(/Actions for booking/)
    fireEvent.click(actionsButton)
    
    const editButton = screen.getByText('Edit booking')
    fireEvent.click(editButton)
    
    expect(mockProps.onEdit).toHaveBeenCalledWith(mockBooking)
  })

  it('calls onDelete when delete action is clicked', () => {
    render(<BookingCard {...mockProps} />)
    
    const actionsButton = screen.getByLabelText(/Actions for booking/)
    fireEvent.click(actionsButton)
    
    const deleteButton = screen.getByText('Delete booking')
    fireEvent.click(deleteButton)
    
    expect(mockProps.onDelete).toHaveBeenCalledWith(1)
  })

  it('shows generate invoice option when no invoice exists', () => {
    const bookingWithoutInvoice = { ...mockBooking, invoice: null }
    render(<BookingCard {...mockProps} booking={bookingWithoutInvoice} />)
    
    const actionsButton = screen.getByLabelText(/Actions for booking/)
    fireEvent.click(actionsButton)
    
    expect(screen.getByText('Generate invoice')).toBeInTheDocument()
  })

  it('does not show generate invoice option when invoice exists', () => {
    render(<BookingCard {...mockProps} />)
    
    const actionsButton = screen.getByLabelText(/Actions for booking/)
    fireEvent.click(actionsButton)
    
    expect(screen.queryByText('Generate invoice')).not.toBeInTheDocument()
  })

  it('displays correct status colors', () => {
    const pendingBooking = { ...mockBooking, status: 'PENDING' as BookingStatus }
    const { rerender } = render(<BookingCard {...mockProps} booking={pendingBooking} />)
    
    expect(screen.getByText('Pending')).toHaveClass('bg-yellow-100')
    
    const cancelledBooking = { ...mockBooking, status: 'CANCELLED' as BookingStatus }
    rerender(<BookingCard {...mockProps} booking={cancelledBooking} />)
    
    expect(screen.getByText('Cancelled')).toHaveClass('bg-red-100')
  })
})