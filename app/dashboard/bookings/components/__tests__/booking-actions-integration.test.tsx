import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { BookingsTable } from '../bookings-table'
import { BookingCard } from '../booking-card'
import { Booking } from '@/lib/types'

// Mock the useRouter hook
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock booking data
const mockBooking: Booking = {
  id: 1,
  customer: {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phoneNumber: '************',
    companyName: 'Test Company',
    createdAt: new Date(),
    updatedAt: new Date(),
    bookings: []
  },
  customerId: 1,
  resources: [
    {
      id: 1,
      name: 'Conference Room A',
      type: 'ROOM',
      capacity: 10,
      hourlyRate: 50,
      amenities: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],
  status: 'PENDING',
  start: new Date('2024-01-15T10:00:00Z'),
  end: new Date('2024-01-15T12:00:00Z'),
  invoice: null,
  caterings: [],
  createdAt: new Date('2024-01-10T08:00:00Z'),
  updatedAt: new Date('2024-01-10T08:00:00Z'),
  createdById: null,
  updatedById: null
}

const mockBookingWithInvoice: Booking = {
  ...mockBooking,
  id: 2,
  invoice: {
    id: 1,
    customerId: 1,
    customer: mockBooking.customer,
    bookingId: 2,
    booking: mockBooking,
    status: 'PENDING',
    total: 100,
    paid: 0,
    lineItems: [],
    payments: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdById: null,
    updatedById: null
  }
}

describe('Booking Actions Integration', () => {
  let mockOnEdit: ReturnType<typeof vi.fn>
  let mockOnDelete: ReturnType<typeof vi.fn>
  let mockOnGenerateInvoice: ReturnType<typeof vi.fn>
  let mockOnRefresh: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockOnEdit = vi.fn()
    mockOnDelete = vi.fn()
    mockOnGenerateInvoice = vi.fn()
    mockOnRefresh = vi.fn()
  })

  describe('BookingsTable Actions', () => {
    it('renders actions dropdown for each booking row', () => {
      render(
        <BookingsTable
          bookings={[mockBooking]}
          loading={false}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
          onRefresh={mockOnRefresh}
        />
      )

      // Should have actions button for the booking
      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      expect(actionsButton).toBeInTheDocument()
    })

    it('shows edit, delete, and generate invoice options when actions menu is opened', async () => {
      render(
        <BookingsTable
          bookings={[mockBooking]}
          loading={false}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
          onRefresh={mockOnRefresh}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        expect(screen.getByText('Edit booking')).toBeInTheDocument()
        expect(screen.getByText('Delete booking')).toBeInTheDocument()
        expect(screen.getByText('Generate invoice')).toBeInTheDocument()
      })
    })

    it('calls onEdit when edit action is clicked', async () => {
      render(
        <BookingsTable
          bookings={[mockBooking]}
          loading={false}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
          onRefresh={mockOnRefresh}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        const editButton = screen.getByText('Edit booking')
        fireEvent.click(editButton)
      })

      expect(mockOnEdit).toHaveBeenCalledWith(mockBooking)
    })

    it('calls onDelete when delete action is clicked', async () => {
      render(
        <BookingsTable
          bookings={[mockBooking]}
          loading={false}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
          onRefresh={mockOnRefresh}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        const deleteButton = screen.getByText('Delete booking')
        fireEvent.click(deleteButton)
      })

      expect(mockOnDelete).toHaveBeenCalledWith(mockBooking.id)
    })

    it('calls onGenerateInvoice when generate invoice action is clicked', async () => {
      render(
        <BookingsTable
          bookings={[mockBooking]}
          loading={false}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
          onRefresh={mockOnRefresh}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        const generateInvoiceButton = screen.getByText('Generate invoice')
        fireEvent.click(generateInvoiceButton)
      })

      expect(mockOnGenerateInvoice).toHaveBeenCalledWith(mockBooking.id)
    })

    it('does not show generate invoice option when booking already has invoice', async () => {
      render(
        <BookingsTable
          bookings={[mockBookingWithInvoice]}
          loading={false}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
          onRefresh={mockOnRefresh}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 2 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        expect(screen.getByText('Edit booking')).toBeInTheDocument()
        expect(screen.getByText('Delete booking')).toBeInTheDocument()
        expect(screen.queryByText('Generate invoice')).not.toBeInTheDocument()
      })
    })
  })

  describe('BookingCard Actions', () => {
    it('renders actions dropdown for booking card', () => {
      render(
        <BookingCard
          booking={mockBooking}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      expect(actionsButton).toBeInTheDocument()
    })

    it('shows edit, delete, and generate invoice options when actions menu is opened', async () => {
      render(
        <BookingCard
          booking={mockBooking}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        expect(screen.getByText('Edit booking')).toBeInTheDocument()
        expect(screen.getByText('Delete booking')).toBeInTheDocument()
        expect(screen.getByText('Generate invoice')).toBeInTheDocument()
      })
    })

    it('calls onEdit when edit action is clicked', async () => {
      render(
        <BookingCard
          booking={mockBooking}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        const editButton = screen.getByText('Edit booking')
        fireEvent.click(editButton)
      })

      expect(mockOnEdit).toHaveBeenCalledWith(mockBooking)
    })

    it('calls onDelete when delete action is clicked', async () => {
      render(
        <BookingCard
          booking={mockBooking}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        const deleteButton = screen.getByText('Delete booking')
        fireEvent.click(deleteButton)
      })

      expect(mockOnDelete).toHaveBeenCalledWith(mockBooking.id)
    })

    it('calls onGenerateInvoice when generate invoice action is clicked', async () => {
      render(
        <BookingCard
          booking={mockBooking}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 1 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        const generateInvoiceButton = screen.getByText('Generate invoice')
        fireEvent.click(generateInvoiceButton)
      })

      expect(mockOnGenerateInvoice).toHaveBeenCalledWith(mockBooking.id)
    })

    it('does not show generate invoice option when booking already has invoice', async () => {
      render(
        <BookingCard
          booking={mockBookingWithInvoice}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onGenerateInvoice={mockOnGenerateInvoice}
        />
      )

      const actionsButton = screen.getByLabelText(/Actions for booking 2 - John Doe/)
      fireEvent.click(actionsButton)

      await waitFor(() => {
        expect(screen.getByText('Edit booking')).toBeInTheDocument()
        expect(screen.getByText('Delete booking')).toBeInTheDocument()
        expect(screen.queryByText('Generate invoice')).not.toBeInTheDocument()
      })
    })
  })
})