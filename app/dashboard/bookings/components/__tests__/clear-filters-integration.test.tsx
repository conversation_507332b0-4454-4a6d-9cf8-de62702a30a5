import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { ClearFiltersButton } from '../clear-filters-button';
import { BookingStatusFilter } from '../booking-status-filter';
import { CustomerSearch } from '../customer-search';

// Mock the useBookings hook
const mockClearFilters = vi.fn();
const mockSetStatusFilter = vi.fn();
const mockSetSearchQuery = vi.fn();

vi.mock('@/hooks/use-bookings', () => ({
  useBookings: () => ({
    selectedStatus: 'CONFIRMED',
    searchQuery: 'john doe',
    clearFilters: mockClearFilters,
    setStatusFilter: mockSetStatusFilter,
    setSearchQuery: mockSetSearchQuery,
  }),
}));

// Mock other dependencies
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

vi.mock('@/lib/utils/retry', () => ({
  retryApiCall: vi.fn(),
}));

// Integration test component that combines filters with clear button
function FilterIntegrationTest() {
  const [selectedStatus, setSelectedStatus] = React.useState<'ALL' | 'CONFIRMED'>('CONFIRMED');
  const [searchQuery, setSearchQuery] = React.useState('john doe');

  const handleClearFilters = () => {
    setSelectedStatus('ALL');
    setSearchQuery('');
    mockClearFilters();
  };

  return (
    <div>
      <BookingStatusFilter
        selectedStatus={selectedStatus}
        onStatusChange={setSelectedStatus}
      />
      
      <CustomerSearch
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />
      
      <ClearFiltersButton
        selectedStatus={selectedStatus}
        searchQuery={searchQuery}
        onClearFilters={handleClearFilters}
      />
    </div>
  );
}

describe('Clear Filters Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should clear all filters when clear button is clicked', async () => {
    render(<FilterIntegrationTest />);
    
    // Verify initial state - filters are active
    expect(screen.getByDisplayValue('Confirmed')).toBeInTheDocument();
    expect(screen.getByDisplayValue('john doe')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /clear all applied filters/i })).toBeInTheDocument();
    
    // Click clear filters button
    const clearButton = screen.getByRole('button', { name: /clear all applied filters/i });
    fireEvent.click(clearButton);
    
    // Verify filters are cleared
    await waitFor(() => {
      expect(mockClearFilters).toHaveBeenCalledTimes(1);
    });
    
    // Verify UI state is updated
    expect(screen.getByDisplayValue('All Statuses')).toBeInTheDocument();
    expect(screen.getByDisplayValue('')).toBeInTheDocument();
    
    // Clear button should be hidden when no filters are active
    expect(screen.queryByRole('button', { name: /clear all applied filters/i })).not.toBeInTheDocument();
  });

  it('should show correct filter count', () => {
    render(<FilterIntegrationTest />);
    
    // Should show count of 2 active filters (status + search)
    expect(screen.getByText('Clear Filters (2)')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Customer Search')).toBeInTheDocument();
  });

  it('should provide proper accessibility information', () => {
    render(<FilterIntegrationTest />);
    
    const clearButton = screen.getByRole('button', { name: /clear all applied filters/i });
    
    // Should have proper ARIA attributes
    expect(clearButton).toHaveAttribute('aria-describedby', 'clear-filters-description');
    
    // Should have screen reader description
    expect(screen.getByText(/Currently filtering by: Status and Customer Search/)).toBeInTheDocument();
  });

  it('should handle individual filter changes correctly', async () => {
    render(<FilterIntegrationTest />);
    
    // Change status filter
    const statusSelect = screen.getByRole('combobox', { name: /filter bookings by status/i });
    fireEvent.click(statusSelect);
    
    const allStatusOption = screen.getByRole('option', { name: /all statuses/i });
    fireEvent.click(allStatusOption);
    
    // Should update filter count
    await waitFor(() => {
      expect(screen.getByText('Clear Filters (1)')).toBeInTheDocument();
      expect(screen.queryByText('Status')).not.toBeInTheDocument();
      expect(screen.getByText('Customer Search')).toBeInTheDocument();
    });
  });

  it('should handle search query changes correctly', async () => {
    render(<FilterIntegrationTest />);
    
    // Clear search query
    const searchInput = screen.getByRole('searchbox');
    fireEvent.change(searchInput, { target: { value: '' } });
    
    // Should update filter count
    await waitFor(() => {
      expect(screen.getByText('Clear Filters (1)')).toBeInTheDocument();
      expect(screen.getByText('Status')).toBeInTheDocument();
      expect(screen.queryByText('Customer Search')).not.toBeInTheDocument();
    });
  });
});