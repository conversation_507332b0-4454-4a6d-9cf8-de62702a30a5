"use client";

import React from "react";
import { ErrorBoundary } from "@/components/ui/error-boundary";

interface BookingManagementErrorBoundaryProps {
  children: React.ReactNode;
  onRetry?: () => void;
}

// Booking-specific error monitoring
class BookingErrorMonitor {
  private static instance: BookingErrorMonitor;
  private errorQueue: any[] = [];

  public static getInstance(): BookingErrorMonitor {
    if (!BookingErrorMonitor.instance) {
      BookingErrorMonitor.instance = new BookingErrorMonitor();
    }
    return BookingErrorMonitor.instance;
  }

  public captureError(
    error: Error,
    context: string,
    metadata: Record<string, any> = {}
  ): string {
    const errorId = `booking_err_${Date.now().toString(36)}_${Math.random().toString(36).substr(2, 5)}`;
    
    const errorReport = {
      id: errorId,
      error: error.message,
      stack: error.stack,
      context,
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString(),
        component: 'booking-management',
        userAgent: navigator.userAgent,
        url: window.location.href,
      }
    };

    this.errorQueue.push(errorReport);
    
    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Booking Management Error:', errorReport);
    }

    return errorId;
  }

  public getStats() {
    return {
      totalErrors: this.errorQueue.length,
      recentErrors: this.errorQueue.slice(-5)
    };
  }
}

export function BookingManagementErrorBoundary({ 
  children, 
  onRetry 
}: BookingManagementErrorBoundaryProps) {
  const errorMonitor = BookingErrorMonitor.getInstance();

  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Only capture error for monitoring - no state updates or toasts
    const errorId = errorMonitor.captureError(error, 'error-boundary', {
      componentStack: errorInfo.componentStack,
      component: 'booking-management-error-boundary'
    });

    // Log error for debugging
    console.error('Booking Management Error Boundary:', error, errorInfo);
  };

  const handleRetryWithFeedback = () => {
    if (onRetry) {
      onRetry();
    }
  };

  const fallbackComponent = (
    <div className="w-full max-w-lg mx-auto p-6 bg-white border border-red-200 rounded-lg shadow-lg">
      <div className="text-center">
        <div className="mx-auto mb-4 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
          <span className="text-red-600 text-xl">⚠️</span>
        </div>
        <h2 className="text-xl font-bold text-gray-900 mb-2">
          Booking Management Error
        </h2>
        <p className="text-gray-600 mb-6">
          An unexpected error occurred in the booking management system. Please try refreshing the page.
        </p>
      </div>

      <div className="space-y-4">
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="space-y-2">
            <p className="font-medium text-red-800">
              The booking management system encountered an error and cannot be displayed.
            </p>
            <div className="text-sm text-red-700 space-y-2">
              <div>
                <p className="font-medium">Troubleshooting Steps:</p>
                <ul className="space-y-1 ml-4">
                  <li>• Try refreshing the page</li>
                  <li>• Check your internet connection</li>
                  <li>• Clear your browser cache</li>
                  <li>• Contact support if the issue continues</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onRetry && (
            <button 
              onClick={handleRetryWithFeedback} 
              className="flex items-center justify-center min-h-[44px] px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <span className="mr-2">🔄</span>
              Retry Loading Calendar
            </button>
          )}
          
          <button 
            onClick={() => window.location.reload()}
            className="min-h-[44px] px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            <span className="mr-2">🔄</span>
            Refresh Page
          </button>
        </div>

        <div className="text-center text-sm text-gray-500">
          <p>If this problem persists, please contact your system administrator.</p>
        </div>
      </div>
    </div>
  );

  return (
    <ErrorBoundary
      fallback={fallbackComponent}
      onError={handleError}
      maxRetries={3}
      showErrorDetails={process.env.NODE_ENV === "development"}
    >
      {children}
    </ErrorBoundary>
  );
}