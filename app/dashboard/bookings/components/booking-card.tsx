"use client"

import React, { memo, useMemo, useCallback } from "react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  FileText,
  User,
  Calendar,
  Clock,
  MapPin,
  Receipt,
} from "lucide-react"
import { 
  Booking, 
  BookingStatus,
  BOOKING_STATUS_LABELS,
  formatBookingDuration,
  formatCurrency
} from "@/lib/types"
import { format } from "date-fns"

interface BookingCardProps {
  booking: Booking
  onEdit: (booking: Booking) => void
  onDelete: (bookingId: number) => void
  onGenerateInvoice: (bookingId: number) => void
}

const getStatusColor = (status: BookingStatus) => {
  switch (status) {
    case 'CONFIRMED':
      return 'bg-green-100 border-green-300 text-green-800'
    case 'PENDING':
      return 'bg-yellow-100 border-yellow-300 text-yellow-800'
    case 'CANCELLED':
      return 'bg-red-100 border-red-300 text-red-800'
    default:
      return 'bg-gray-100 border-gray-300 text-gray-800'
  }
}

const getInvoiceStatusDisplay = (booking: Booking) => {
  if (!booking.invoice) {
    return { text: 'None', color: 'bg-gray-100 text-gray-600' }
  }
  
  switch (booking.invoice.status) {
    case 'PAID':
      return { text: 'Paid', color: 'bg-green-100 text-green-700' }
    case 'PARTIALLY_PAID':
      return { text: 'Partial', color: 'bg-yellow-100 text-yellow-700' }
    case 'PENDING':
      return { text: 'Pending', color: 'bg-blue-100 text-blue-700' }
    case 'CANCELLED':
      return { text: 'Cancelled', color: 'bg-red-100 text-red-700' }
    default:
      return { text: 'Unknown', color: 'bg-gray-100 text-gray-600' }
  }
}

// Mobile card component for individual bookings - memoized for performance
export const BookingCard = memo(({ 
  booking, 
  onEdit, 
  onDelete,
  onGenerateInvoice
}: BookingCardProps) => {
  // Memoize computed values for better performance
  const invoiceStatus = useMemo(() => getInvoiceStatusDisplay(booking), [booking.invoice])
  const duration = useMemo(() => {
    if (!booking.periods || booking.periods.length === 0) {
      return 'No periods'
    }

    const totalMs = booking.periods.reduce((total, period) => {
      const duration = new Date(period.end).getTime() - new Date(period.start).getTime()
      return total + duration
    }, 0)

    // Convert milliseconds to hours and minutes
    const totalMinutes = Math.round(totalMs / (1000 * 60))
    const hours = Math.floor(totalMinutes / 60)
    const minutes = totalMinutes % 60

    if (hours === 0) {
      return `${minutes}min`
    } else if (minutes === 0) {
      return `${hours}h`
    } else {
      return `${hours}h ${minutes}min`
    }
  }, [booking.periods])
  
  // Memoize handlers to prevent unnecessary re-renders
  const handleEdit = useCallback(() => onEdit(booking), [onEdit, booking])
  const handleDelete = useCallback(() => onDelete(booking.id), [onDelete, booking.id])
  const handleGenerateInvoice = useCallback(() => onGenerateInvoice(booking.id), [onGenerateInvoice, booking.id])
  
  return (
    <Card className="mb-4">
      <CardContent className="mobile-spacing">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <div 
                className="flex items-center justify-center w-10 h-10 rounded-md bg-muted"
                aria-hidden="true"
              >
                <User className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-medium text-base" id={`booking-${booking.id}`}>
                  {booking.customer.name}
                </h3>
                <Badge 
                  variant="secondary" 
                  className={`mt-1 ${getStatusColor(booking.status)}`}
                >
                  {BOOKING_STATUS_LABELS[booking.status]}
                </Badge>
              </div>
            </div>
            
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-start space-x-2">
                <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0" aria-hidden="true" />
                <div className="flex-1">
                  <span className="font-medium">Resources:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {booking.resources.length > 0 ? (
                      booking.resources.map((resource) => (
                        <Badge key={resource.id} variant="outline" className="text-xs">
                          {resource.name}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-muted-foreground">No resources</span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
                <div>
                  <span className="font-medium">Date:</span>
                  <span className="ml-1">
                    {booking.computedStart ? format(new Date(booking.computedStart), 'MMM dd, yyyy') : 'No periods'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
                <div>
                  <span className="font-medium">Time:</span>
                  <span className="ml-1">
                    {booking.computedStart && booking.computedEnd 
                      ? `${format(new Date(booking.computedStart), 'h:mm a')} - ${format(new Date(booking.computedEnd), 'h:mm a')}`
                      : 'No periods'
                    }
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Duration:</span>
                <span>{duration}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Receipt className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
                <div className="flex items-center space-x-2">
                  <span className="font-medium">Invoice:</span>
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${invoiceStatus.color}`}
                  >
                    {invoiceStatus.text}
                  </Badge>
                  {booking.invoice && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="text-xs cursor-help">
                            ({formatCurrency(booking.invoice.total)})
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-xs">
                            <p>Total: {formatCurrency(booking.invoice.total)}</p>
                            <p>Paid: {formatCurrency(booking.invoice.paid)}</p>
                            <p>Balance: {formatCurrency(booking.invoice.total - booking.invoice.paid)}</p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Created:</span>
                <span>
                  {booking.createdAt 
                    ? (() => {
                        try {
                          return format(new Date(booking.createdAt), 'MMM dd, yyyy');
                        } catch (error) {
                          return 'Invalid date';
                        }
                      })()
                    : 'N/A'
                  }
                </span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="h-11 w-11 p-0 touch-target focus-enhanced"
                aria-label={`Actions for booking ${booking.id} - ${booking.customer.name}`}
              >
                <span className="sr-only">
                  Open actions menu for booking {booking.id} - {booking.customer.name}
                </span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={handleEdit}
                className="cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                Edit booking
              </DropdownMenuItem>
              
              {!booking.invoice && (
                <DropdownMenuItem
                  onClick={handleGenerateInvoice}
                  className="cursor-pointer touch-target focus-enhanced"
                  role="menuitem"
                >
                  <FileText className="mr-2 h-4 w-4" aria-hidden="true" />
                  Generate invoice
                </DropdownMenuItem>
              )}
              
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-destructive cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                Delete booking
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
})

BookingCard.displayName = "BookingCard"