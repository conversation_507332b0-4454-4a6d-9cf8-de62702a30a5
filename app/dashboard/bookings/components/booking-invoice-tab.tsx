"use client";

import { useState, useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  FileText, 
  Plus, 
  AlertCircle, 
  CheckCircle2, 
  Clock, 
  DollarSign,
  Calendar,
  User
} from "lucide-react";
import { Invoice, InvoiceStatus } from "@/lib/types";
import { useBookings } from "@/hooks/use-bookings";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import Link from "next/link";

interface BookingInvoiceTabProps {
  bookingId: number;
}

const getStatusColor = (status: InvoiceStatus) => {
  switch (status) {
    case 'PAID':
      return 'bg-green-100 border-green-300 text-green-800';
    case 'PARTIALLY_PAID':
      return 'bg-blue-100 border-blue-300 text-blue-800';
    case 'PENDING':
      return 'bg-yellow-100 border-yellow-300 text-yellow-800';
    case 'CANCELLED':
      return 'bg-red-100 border-red-300 text-red-800';
    default:
      return 'bg-gray-100 border-gray-300 text-gray-800';
  }
};

const getStatusIcon = (status: InvoiceStatus) => {
  switch (status) {
    case 'PAID':
      return <CheckCircle2 className="h-4 w-4" />;
    case 'PARTIALLY_PAID':
      return <Clock className="h-4 w-4" />;
    case 'PENDING':
      return <Clock className="h-4 w-4" />;
    case 'CANCELLED':
      return <AlertCircle className="h-4 w-4" />;
    default:
      return <FileText className="h-4 w-4" />;
  }
};

export const BookingInvoiceTab: React.FC<BookingInvoiceTabProps> = ({ bookingId }) => {
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [generating, setGenerating] = useState(false);
  
  const { generateInvoice } = useBookings();
  const { toast } = useToast();

  const fetchInvoice = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      
      const response = await fetch(`/api/bookings/${bookingId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch booking details");
      }
      
      const result = await response.json();
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch booking details");
      }
      
      setInvoice(result.data.invoice || null);
    } catch (error) {
      console.error("Error fetching invoice:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch invoice";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [bookingId]);

  useEffect(() => {
    fetchInvoice();
  }, [fetchInvoice]);

  const handleGenerateInvoice = useCallback(async () => {
    setGenerating(true);
    try {
      await generateInvoice(bookingId);
      // Refresh the invoice data
      await fetchInvoice();
      toast({
        title: "Success",
        description: "Invoice generated successfully.",
      });
    } catch (error) {
      console.error("Error generating invoice:", error);
      toast({
        title: "Error",
        description: "Failed to generate invoice. Please try again.",
        variant: "destructive",
      });
    } finally {
      setGenerating(false);
    }
  }, [bookingId, generateInvoice, fetchInvoice, toast]);


  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-48" />
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button
            variant="outline"
            size="sm"
            onClick={fetchInvoice}
            className="ml-2"
          >
            Try Again
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!invoice) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Invoice</h3>
          <Button
            onClick={handleGenerateInvoice}
            disabled={generating}
            className="gap-2"
          >
            {generating ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Generating...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4" />
                Generate Invoice
              </>
            )}
          </Button>
        </div>
        
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Invoice Generated</h4>
            <p className="text-gray-600 mb-4">
              This booking doesn't have an invoice yet. Generate one to track payments and billing.
            </p>
            <Button
              onClick={handleGenerateInvoice}
              disabled={generating}
              className="gap-2"
            >
              {generating ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Generating Invoice...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  Generate Invoice
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Invoice</h3>
        <Link
          href={`/dashboard/invoices/${invoice.id}`}
          className="gap-2"
        >
          <Button variant="outline">
          <FileText className="h-4 w-4" /> &nbsp;
          View Full Invoice
          </Button>
        </Link>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Invoice #{invoice.id}
            </div>
            <Badge variant="outline" className={getStatusColor(invoice.status)}>
              <div className="flex items-center gap-1">
                {getStatusIcon(invoice.status)}
                {invoice.status.replace('_', ' ')}
              </div>
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Invoice Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <DollarSign className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Amount</p>
                <p className="font-semibold">{Number(invoice.total).toLocaleString('en-US')} IQD</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Paid Amount</p>
                <p className="font-semibold">{Number(invoice.paid).toLocaleString('en-US')} IQD</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Clock className="h-4 w-4 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Remaining</p>
                <p className="font-semibold">{(Number(invoice.total) - Number(invoice.paid)).toLocaleString('en-US')} IQD</p>
              </div>
            </div>
          </div>

          {/* Invoice Details */}
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-3">Invoice Details</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600">Created:</span>
                <span>
                  {invoice.createdAt 
                    ? (() => {
                        try {
                          return format(new Date(invoice.createdAt), 'MMM d, yyyy h:mm a');
                        } catch (error) {
                          return 'Invalid date';
                        }
                      })()
                    : 'N/A'
                  }
                </span>
              </div>
              
              {invoice.booking && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Customer:</span>
                  <span>{invoice.booking.customer.name}</span>
                </div>
              )}
            </div>
          </div>

          {/* Line Items Preview */}
          {invoice.lineItems && invoice.lineItems.length > 0 && (
            <div className="border-t pt-4">
              <h4 className="font-medium text-gray-900 mb-3">Line Items</h4>
              <div className="space-y-2">
                {invoice.lineItems.map((item, index) => {
                  // Debug log to see the actual structure
                  console.log('Line item structure:', item);
                  
                  // Handle different possible data structures
                  const quantity = (item as any).quantity ?? (item as any).qty ?? 1;
                  const unitAmount = Number(item.amount) || 0;
                  const totalAmount = Number(unitAmount) * Number(quantity);
                  
                  return (
                    <div key={index} className="flex justify-between items-center text-sm">
                      <div>
                        <span className="font-medium">{item.description || 'Unknown Item'}</span>
                        <span className="text-gray-500 ml-1">x{quantity}</span>
                        <span className="text-gray-500 ml-2">@ {unitAmount.toLocaleString('en-US')} IQD</span>
                      </div>
                      <span className="font-medium">{totalAmount.toLocaleString('en-US')} IQD</span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Payment Status */}
          {invoice.status !== 'PAID' && invoice.status !== 'CANCELLED' && (
            <div className="border-t pt-4">
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription>
                  {invoice.status === 'PENDING' 
                    ? "This invoice is pending payment. No payments have been recorded yet."
                    : `This invoice is partially paid. ${(Number(invoice.total) - Number(invoice.paid)).toLocaleString('en-US')} IQD remaining.`
                  }
                </AlertDescription>
              </Alert>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};