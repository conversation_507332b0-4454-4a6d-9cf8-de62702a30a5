"use client";

import React, { memo, useMemo, useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Filter, Loader2, Building2 } from "lucide-react";
import { Resource, getResourceTypeDisplayName } from "@/lib/types";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { retryApiCall } from "@/lib/utils/retry";
import { FilterErrorState } from "./filter-error-state";
import { enhancedRetry, analyzeError } from "@/lib/utils/enhanced-retry";

interface ResourceFilterProps {
  selectedResource: number | 'ALL';
  onResourceChange: (resourceId: number | 'ALL') => void;
  disabled?: boolean;
  className?: string;
  "aria-label"?: string;
}

export const ResourceFilter = memo(({
  selectedResource,
  onResourceChange,
  disabled = false,
  className,
  "aria-label": ariaLabel = "Filter bookings by resource",
}: ResourceFilterProps) => {
  const { toast } = useToast();
  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  // Enhanced fetch available resources with retry mechanism
  const fetchResources = async () => {
    setLoading(true);
    setError(null);
    setIsRetrying(retryCount > 0);

    try {
      const result = await enhancedRetry(
        async () => {
          const response = await fetch('/api/resources?limit=1000');
          
          if (!response.ok) {
            const errorText = await response.text().catch(() => 'Unknown error');
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }
          
          return response;
        },
        {
          maxAttempts: 3,
          baseDelay: 1000,
          maxDelay: 8000,
          onRetry: (error, attempt, delay) => {
            setRetryCount(attempt);
            const errorAnalysis = analyzeError(error);
            console.warn(`Retrying fetch resources for filter (attempt ${attempt}):`, error.message);
            
            toast({
              title: "Loading Resources",
              description: `${errorAnalysis.userMessage} Retrying (${attempt}/3)...`,
              className: "border-yellow-200 bg-yellow-50 text-yellow-900"
            });
          },
        },
        'resource-filter' // Circuit breaker key
      );

      if (!result.success) {
        throw result.error || new Error('Failed to fetch resources');
      }

      const response = result.data!;
      const apiResult = await response.json();
      
      if (!apiResult.success || !apiResult.data) {
        throw new Error(apiResult.error || "Failed to fetch resources");
      }

      setResources(apiResult.data.data || []);
      setRetryCount(0);
    } catch (error) {
      console.error("Error fetching resources for filter:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch resources";
      setError(errorMessage);
      
      const errorAnalysis = analyzeError(errorMessage);
      toast({
        variant: "destructive",
        title: "Failed to Load Resources",
        description: errorAnalysis.userMessage,
        duration: 5000,
      });
    } finally {
      setLoading(false);
      setIsRetrying(false);
    }
  };

  useEffect(() => {
    fetchResources();
  }, [toast]);

  // Resource options with proper labeling
  const resourceOptions = useMemo(() => {
    const options: { value: string; label: string; resource: Resource | null }[] = [
      { value: 'ALL', label: 'All Resources', resource: null },
    ];

    // Sort resources by name for better UX
    const sortedResources = [...resources].sort((a, b) => 
      a.name.localeCompare(b.name)
    );

    sortedResources.forEach(resource => {
      options.push({
        value: resource.id.toString(),
        label: resource.name,
        resource,
      });
    });

    return options;
  }, [resources]);

  const handleValueChange = (value: string) => {
    const newResourceId = value === 'ALL' ? 'ALL' : parseInt(value, 10);
    onResourceChange(newResourceId);
  };

  const getDisplayValue = () => {
    if (selectedResource === 'ALL') {
      return 'All Resources';
    }
    
    const resource = resources.find(r => r.id === selectedResource);
    return resource ? resource.name : 'Unknown Resource';
  };

  const getSelectedResource = () => {
    if (selectedResource === 'ALL') {
      return null;
    }
    return resources.find(r => r.id === selectedResource) || null;
  };

  const getResourceBadge = (resource: Resource | null) => {
    if (!resource) {
      return null;
    }
    
    return (
      <Badge 
        variant="outline" 
        className="ml-2 text-xs font-medium border bg-blue-50 text-blue-700 border-blue-200"
      >
        {getResourceTypeDisplayName(resource.type)}
      </Badge>
    );
  };

  // Show loading state
  if (loading) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <div className="flex items-center space-x-1">
          <Filter className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
        </div>
        
        <div className="flex items-center space-x-2 w-full h-9 px-3 py-2 border rounded-md bg-muted/50">
          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          <span className="text-sm text-muted-foreground">Loading...</span>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <Filter className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
          </div>
          
          <div className="flex items-center space-x-2 w-full h-9 px-3 py-2 border rounded-md bg-red-50 border-red-200">
            <span className="text-sm text-red-600">Failed to load</span>
          </div>
        </div>
        
        <FilterErrorState
          error={error}
          filterType="resource"
          onRetry={fetchResources}
          loading={isRetrying}
          className="w-full max-w-md"
        />
      </div>
    );
  }

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <div className="flex items-center space-x-1">
        <Filter className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
      </div>
      
      <Select 
        value={selectedResource.toString()} 
        onValueChange={handleValueChange}
        disabled={disabled || resources.length === 0}
      >
        <SelectTrigger 
          className={cn(
            "w-full h-9 text-sm",
            selectedResource !== 'ALL' && "border-primary/50 bg-primary/5"
          )}
          aria-label={ariaLabel}
          aria-describedby={selectedResource !== 'ALL' ? "resource-filter-description" : undefined}
        >
          <SelectValue>
            <div className="flex items-center">
              <Building2 className="h-4 w-4 mr-2 text-muted-foreground" aria-hidden="true" />
              <span className="truncate">{getDisplayValue()}</span>
              {selectedResource !== 'ALL' && getResourceBadge(getSelectedResource())}
            </div>
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent className="max-h-[300px]">
          {resourceOptions.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value}
              className="cursor-pointer"
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center space-x-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
                  <span className="truncate">{option.label}</span>
                </div>
                {option.resource && (
                  <Badge 
                    variant="outline" 
                    className="ml-2 text-xs font-medium border bg-blue-50 text-blue-700 border-blue-200"
                  >
                    {getResourceTypeDisplayName(option.resource.type)}
                  </Badge>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {/* Screen reader description for active filter */}
      {selectedResource !== 'ALL' && (
        <span 
          id="resource-filter-description" 
          className="sr-only"
        >
          Currently filtering by {getDisplayValue()} resource
        </span>
      )}
    </div>
  );
});

ResourceFilter.displayName = "ResourceFilter";