"use client";

import { useState, use<PERSON><PERSON>back, use<PERSON>em<PERSON>, useEffect, Suspense } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, RefreshCw, AlertCircle, Calendar, Filter, Users, Clock } from "lucide-react";

// Import components
import { BookingsTable } from "./components/bookings-table";
import { BookingStatusFilter } from "./components/booking-status-filter";
import { DateRangeFilter } from "./components/date-range-filter";
import { ResourceFilter } from "./components/resource-filter";
import { CustomerSearch } from "./components/customer-search";
import { ClearFiltersButton } from "./components/clear-filters-button";
import { DeleteConfirmationDialog } from "./components/delete-confirmation-dialog";
import { BookingsTableSkeleton } from "./components/bookings-table-skeleton";
import { SearchInput } from "@/components/dashboard/search-input";
import { PaginationControls } from "@/components/dashboard/pagination-controls";
import { BookingManagementErrorBoundary } from "./components/booking-management-error-boundary";

// Import custom hook
import { useBookings } from "@/hooks/use-bookings";

// Import types
import { 
  Booking, 
  BookingStatus,
  BOOKING_STATUS_LABELS
} from "@/lib/types";

// Invoice status type and labels
type InvoiceStatus = 'PENDING' | 'PARTIALLY_PAID' | 'PAID' | 'CANCELLED';

const INVOICE_STATUS_LABELS: Record<InvoiceStatus, string> = {
  PENDING: 'Pending',
  PARTIALLY_PAID: 'Partially Paid',
  PAID: 'Paid',
  CANCELLED: 'Cancelled'
};

interface DialogState {
  deleteConfirmation: {
    open: boolean;
    booking: Booking | null;
    loading: boolean;
  };
}

export default function BookingsPage() {
  // State for dialogs
  const [dialogState, setDialogState] = useState<DialogState>({
    deleteConfirmation: {
      open: false,
      booking: null,
      loading: false,
    },
  });

  // Filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<BookingStatus | 'ALL'>('ALL');
  const [selectedResource, setSelectedResource] = useState<number | 'ALL'>('ALL');
  const [selectedInvoiceStatus, setSelectedInvoiceStatus] = useState<InvoiceStatus | 'ALL' | 'NONE'>('ALL');
  
  // Let DateRangeFilter handle URL state, just maintain local state for the useBookings hook
  const [startDate, setStartDate] = useState<Date | undefined>();
  const [endDate, setEndDate] = useState<Date | undefined>();

  // Use bookings hook with external filters
  const {
    bookings,
    loading,
    error,
    totalBookings,
    totalPages,
    currentPage,
    pageSize,
    setCurrentPage,
    setPageSize,
    deleteBooking,
    generateInvoice,
    refresh,
    clearFilters: clearHookFilters,
  } = useBookings(
    {
      page: 1,
      limit: 10,
    },
    {
      searchQuery,
      selectedStatus: selectedStatus === 'ALL' ? undefined : selectedStatus,
      selectedResourceId: selectedResource === 'ALL' ? undefined : selectedResource,
      selectedInvoiceStatus: selectedInvoiceStatus === 'ALL' ? undefined : selectedInvoiceStatus,
      startDate,
      endDate,
    }
  );

  // Handle search query change
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Handle status filter change
  const handleStatusChange = useCallback((status: BookingStatus | 'ALL') => {
    setSelectedStatus(status);
  }, []);

  // Handle resource filter change
  const handleResourceChange = useCallback((resourceId: number | 'ALL') => {
    setSelectedResource(resourceId);
  }, []);

  // Handle invoice status filter change
  const handleInvoiceStatusChange = useCallback((status: InvoiceStatus | 'ALL' | 'NONE') => {
    setSelectedInvoiceStatus(status);
  }, []);

  // Handle date range change
  const handleDateRangeChange = useCallback((start?: Date, end?: Date) => {
    setStartDate(start);
    setEndDate(end);
  }, []);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setSearchQuery("");
    setSelectedStatus('ALL');
    setSelectedResource('ALL');
    setSelectedInvoiceStatus('ALL');
    setStartDate(undefined);
    setEndDate(undefined);
    clearHookFilters();
  }, [clearHookFilters]);

  // Handle booking actions
  const handleEditBooking = useCallback((booking: Booking) => {
    // Navigate to booking edit page
    window.location.href = `/dashboard/bookings/${booking.id}`;
  }, []);

  const handleDeleteBooking = useCallback((bookingId: number) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (booking) {
      setDialogState(prev => ({
        ...prev,
        deleteConfirmation: {
          open: true,
          booking,
          loading: false,
        },
      }));
    }
  }, [bookings]);

  const handleGenerateInvoice = useCallback(async (bookingId: number) => {
    try {
      await generateInvoice(bookingId);
    } catch (error) {
      console.error("Failed to generate invoice:", error);
    }
  }, [generateInvoice]);

  // Confirm delete booking
  const confirmDeleteBooking = useCallback(async () => {
    const { booking } = dialogState.deleteConfirmation;
    if (!booking) return;

    setDialogState(prev => ({
      ...prev,
      deleteConfirmation: {
        ...prev.deleteConfirmation,
        loading: true,
      },
    }));

    try {
      await deleteBooking(booking.id);
      
      // Close dialog
      setDialogState(prev => ({
        ...prev,
        deleteConfirmation: {
          open: false,
          booking: null,
          loading: false,
        },
      }));
    } catch (error) {
      console.error("Failed to delete booking:", error);
      
      // Keep dialog open but stop loading
      setDialogState(prev => ({
        ...prev,
        deleteConfirmation: {
          ...prev.deleteConfirmation,
          loading: false,
        },
      }));
    }
  }, [dialogState.deleteConfirmation, deleteBooking]);

  // Cancel delete booking
  const cancelDeleteBooking = useCallback(() => {
    setDialogState(prev => ({
      ...prev,
      deleteConfirmation: {
        open: false,
        booking: null,
        loading: false,
      },
    }));
  }, []);

  // Calculate filter counts for display
  const filterCounts = useMemo(() => {
    const activeFilters = [];
    if (searchQuery.trim()) activeFilters.push('Search');
    if (selectedStatus !== 'ALL') activeFilters.push('Status');
    if (selectedResource !== 'ALL') activeFilters.push('Resource');
    if (selectedInvoiceStatus !== 'ALL') activeFilters.push('Invoice Status');
    if (startDate || endDate) activeFilters.push('Date Range');
    
    return {
      total: activeFilters.length,
      labels: activeFilters,
    };
  }, [searchQuery, selectedStatus, selectedResource, selectedInvoiceStatus, startDate, endDate]);

  // Status distribution for dashboard stats
  const statusStats = useMemo(() => {
    const stats = {
      PENDING: 0,
      CONFIRMED: 0,
      CANCELLED: 0,
    };

    bookings.forEach(booking => {
      if (booking.status in stats) {
        stats[booking.status as keyof typeof stats]++;
      }
    });

    return stats;
  }, [bookings]);

  // Memoized values for better performance (matching resources page pattern)
  const shouldShowPagination = useMemo(() => 
    !loading && bookings.length > 0, 
    [loading, bookings.length]
  );

  return (
    <BookingManagementErrorBoundary onRetry={refresh}>
      <div className="h-full bg-gray-50 mobile-spacing mobile-no-scroll">
        <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Bookings</h1>
            <p className="text-muted-foreground">
              Manage venue bookings and reservations
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={refresh}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            
            <Button
              onClick={() => window.location.href = '/dashboard/bookings/create'}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              New Booking
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalBookings}</div>
              <p className="text-xs text-muted-foreground">
                {filterCounts.total > 0 ? 'Filtered results' : 'All bookings'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{statusStats.PENDING}</div>
              <p className="text-xs text-muted-foreground">
                Awaiting confirmation
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Confirmed</CardTitle>
              <Users className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{statusStats.CONFIRMED}</div>
              <p className="text-xs text-muted-foreground">
                Ready to go
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
              <AlertCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{statusStats.CANCELLED}</div>
              <p className="text-xs text-muted-foreground">
                Cancelled bookings
              </p>
            </CardContent>
          </Card>
        </div>


        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>{error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={refresh}
                className="ml-4"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Bookings Table */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              Bookings
              {totalBookings > 0 && (
                <Badge variant="outline" className="ml-2">
                  {totalBookings} total
                </Badge>
              )}
              {filterCounts.total > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {filterCounts.total} filters
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {filterCounts.total > 0 
                ? `Showing filtered results (${filterCounts.labels.join(', ')})`
                : 'All venue bookings and reservations'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Filters Section */}
            <div className="space-y-3 pb-4 border-b">
              {/* First Line: Search */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <CustomerSearch
                    searchQuery={searchQuery}
                    onSearchChange={handleSearchChange}
                    placeholder="Search by customer name, email, or phone..."
                    disabled={loading}
                    debounceMs={2000}
                  />
                </div>
              </div>

              {/* Second Line: Filters */}
              <div className="flex flex-col gap-4">
                {/* Four Equal Width Filter Columns */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <BookingStatusFilter
                    selectedStatus={selectedStatus}
                    onStatusChange={handleStatusChange}
                    disabled={loading}
                    className="w-full"
                  />
                  <ResourceFilter
                    selectedResource={selectedResource}
                    onResourceChange={handleResourceChange}
                    disabled={loading}
                    className="w-full"
                  />
                  
                  {/* Invoice Status Filter */}
                  <div className="w-full">
                    <Select
                      value={selectedInvoiceStatus}
                      onValueChange={handleInvoiceStatusChange}
                      disabled={loading}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Invoice Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ALL">All Invoice Status</SelectItem>
                        <SelectItem value="NONE">No Invoice</SelectItem>
                        <SelectItem value="PENDING">Pending</SelectItem>
                        <SelectItem value="PARTIALLY_PAID">Partially Paid</SelectItem>
                        <SelectItem value="PAID">Paid</SelectItem>
                        <SelectItem value="CANCELLED">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <Suspense fallback={<div className="h-9 w-full bg-muted animate-pulse rounded" />}>
                    <DateRangeFilter
                      startDate={startDate}
                      endDate={endDate}
                      onDateRangeChange={handleDateRangeChange}
                      disabled={loading}
                      className="w-full"
                    />
                  </Suspense>
                </div>
                
                {/* Clear Filters - Separate Row */}
                <div className="flex justify-end">
                  <ClearFiltersButton
                    selectedStatus={selectedStatus}
                    selectedResource={selectedResource}
                    startDate={startDate}
                    endDate={endDate}
                    searchQuery={searchQuery}
                    onClearFilters={handleClearFilters}
                    disabled={loading}
                  />
                </div>
              </div>
            </div>

            {/* Table Content */}
            {loading ? (
              <BookingsTableSkeleton 
                rows={pageSize} 
                showLoadingMessage={true}
                loadingText="Loading bookings..."
              />
            ) : (
              <>
                <BookingsTable
                  bookings={bookings}
                  loading={loading}
                  error={error}
                  onEdit={handleEditBooking}
                  onDelete={handleDeleteBooking}
                  onGenerateInvoice={handleGenerateInvoice}
                  onRefresh={refresh}
                />
                
                {!loading && bookings.length > 0 && (
                  <div className="mt-4 sm:mt-6">
                    <PaginationControls
                      currentPage={currentPage}
                      totalPages={totalPages}
                      pageSize={pageSize}
                      totalItems={totalBookings}
                      onPageChange={setCurrentPage}
                      onPageSizeChange={setPageSize}
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>


        {/* Delete Confirmation Dialog */}
        <DeleteConfirmationDialog
          open={dialogState.deleteConfirmation.open}
          onOpenChange={(open) => {
            if (!open) cancelDeleteBooking();
          }}
          onConfirm={confirmDeleteBooking}
          loading={dialogState.deleteConfirmation.loading}
          booking={dialogState.deleteConfirmation.booking}
        />
        </div>
      </div>
    </BookingManagementErrorBoundary>
  );
}