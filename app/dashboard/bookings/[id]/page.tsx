"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, AlertCircle, Calendar, FileText } from "lucide-react";
import { BookingInformationForm } from "../components/booking-information-form";
import { BookingInvoiceTab } from "../components/booking-invoice-tab";
import { useBookings } from "@/hooks/use-bookings";
import { Booking, BookingFormData } from "@/lib/types";
import { toast } from "@/hooks/use-toast";

export default function EditBookingPage() {
  const router = useRouter();
  const params = useParams();
  const bookingId = parseInt(params.id as string);
  const { updateBooking } = useBookings();
  
  const [booking, setBooking] = useState<Booking | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string>("");
  const [activeTab, setActiveTab] = useState("booking-info");

  // Fetch booking data
  const fetchBooking = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      
      const response = await fetch(`/api/bookings/${bookingId}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Booking not found");
        }
        throw new Error("Failed to fetch booking");
      }
      
      const result = await response.json();
      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to fetch booking");
      }
      
      setBooking(result.data);
    } catch (error) {
      console.error("Error fetching booking:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch booking";
      setError(errorMessage);
      
      if (errorMessage === "Booking not found") {
        toast({
          title: "Booking Not Found",
          description: "The booking you're looking for doesn't exist or may have been deleted.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  }, [bookingId]);

  useEffect(() => {
    if (bookingId && !isNaN(bookingId)) {
      fetchBooking();
    } else {
      setError("Invalid booking ID");
      setLoading(false);
    }
  }, [bookingId]); // Remove fetchBooking from dependencies to prevent infinite loop

  const handleSubmit = useCallback(async (data: BookingFormData) => {
    if (!booking) return;
    
    setUpdating(true);
    try {
      const updatedBooking = await updateBooking(booking.id, data);
      setBooking(updatedBooking);
      toast({
        title: "Success",
        description: "Booking updated successfully.",
      });
    } catch (error) {
      console.error("Error updating booking:", error);
      toast({
        title: "Error",
        description: "Failed to update booking. Please try again.",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  }, [booking, updateBooking]);

  const handleCancel = useCallback(() => {
    router.push("/dashboard/bookings");
  }, [router]);

  const handleRetry = useCallback(() => {
    fetchBooking();
  }, [fetchBooking]);

  if (loading) {
    return (
      <div className="h-full bg-gray-50 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-10 w-32" />
            <div>
              <Skeleton className="h-8 w-48 mb-2" />
              <Skeleton className="h-4 w-64" />
            </div>
          </div>
        </div>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full bg-gray-50 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="min-h-[44px] sm:min-h-auto"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Bookings
            </Button>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Edit Booking</h1>
              <p className="text-gray-600 mt-1 text-sm sm:text-base">Booking not found</p>
            </div>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              className="ml-2"
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!booking) {
    return null;
  }

  return (
    <div className="h-full bg-gray-50 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={updating}
            className="min-h-[44px] sm:min-h-auto"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Bookings
          </Button>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Edit Booking</h1>
            <p className="text-gray-600 mt-1 text-sm sm:text-base">
              Update booking for {booking.customer.name}
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Booking Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="booking-info" className="flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                Booking Information
              </TabsTrigger>
              <TabsTrigger value="invoice" className="flex items-center">
                <FileText className="mr-2 h-4 w-4" />
                Invoice
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="booking-info" className="mt-6">
              <BookingInformationForm
                booking={booking}
                onSubmit={handleSubmit}
                onCancel={handleCancel}
                loading={updating}
                submitButtonText="Update Booking"
              />
            </TabsContent>
            
            <TabsContent value="invoice" className="mt-6">
              <BookingInvoiceTab bookingId={booking.id} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}