"use client";

import { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, CheckCircle2 } from "lucide-react";
import { Customer, CustomerFormData } from "@/lib/types";
import { customerCreateSchema, customerUpdateSchema } from "@/lib/validations/customer";
import { 
  mapServerErrorsToForm, 
  getApiErrorMessage, 
  createErrorSummary,
  hasFormErrors 
} from "@/lib/utils/form-errors";

interface CustomerFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customer?: Customer | null; // null for create, Customer object for edit
  onSubmit: (data: CustomerFormData) => Promise<void>;
  loading?: boolean;
}

export const CustomerFormDialog = memo(({
  open,
  onOpenChange,
  customer,
  onSubmit,
  loading = false,
}: CustomerFormDialogProps) => {
  const isEdit = !!customer;
  const schema = isEdit ? customerUpdateSchema : customerCreateSchema;
  const [serverError, setServerError] = useState<string>("");

  const form = useForm<CustomerFormData>({
    resolver: zodResolver(schema),
    mode: "onChange", // Enable real-time validation
    defaultValues: {
      name: "",
      email: "",
      phoneNumber: "",
      companyName: "",
      specialization: "",
      industry: "",
      website: "",
      linkedIn: "",
      socialMedia: "",
      notes: "",
    },
  });

  // Reset form when dialog opens/closes or customer changes
  useEffect(() => {
    if (open) {
      setServerError(""); // Clear server errors when opening
      if (isEdit && customer) {
        form.reset({
          name: customer.name,
          email: customer.email || "",
          phoneNumber: customer.phoneNumber || "",
          companyName: customer.companyName || "",
          specialization: customer.specialization || "",
          industry: customer.industry || "",
          website: customer.website || "",
          linkedIn: customer.linkedIn || "",
          socialMedia: customer.socialMedia || "",
          notes: customer.notes || "",
        });
      } else {
        form.reset({
          name: "",
          email: "",
          phoneNumber: "",
          companyName: "",
          specialization: "",
          industry: "",
          website: "",
          linkedIn: "",
          socialMedia: "",
          notes: "",
        });
      }
    }
  }, [open, isEdit, customer, form]);

  const handleSubmit = useCallback(async (data: CustomerFormData) => {
    setServerError(""); // Clear previous server errors
    
    try {
      await onSubmit(data);
      onOpenChange(false);
    } catch (error) {
      console.error("Form submission error:", error);
      
      // Handle server validation errors
      if (error && typeof error === 'object' && 'details' in error) {
        mapServerErrorsToForm(error as any, form.setError as any);
      }
      
      // Set general server error message
      const errorMessage = getApiErrorMessage(error);
      setServerError(errorMessage);
    }
  }, [onSubmit, onOpenChange, form]);

  const handleCancel = useCallback(() => {
    form.reset();
    setServerError("");
    onOpenChange(false);
  }, [form, onOpenChange]);

  // Get error summary for display - memoized to prevent unnecessary recalculations
  const errorSummary = useMemo(() => 
    createErrorSummary(form.formState.errors, serverError), 
    [form.formState.errors, serverError]
  );
  
  const canSubmit = useMemo(() => 
    form.formState.isValid && !loading, 
    [form.formState.isValid, loading]
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEdit ? "Edit Customer" : "Create New Customer"}</DialogTitle>
          <DialogDescription>
            {isEdit
              ? "Update the customer information below."
              : "Fill in the details below to create a new customer."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            {/* Error Summary */}
            {errorSummary.hasErrors && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">{errorSummary.summary}</p>
                    {errorSummary.messages.length > 1 && (
                      <ul className="list-disc list-inside text-sm space-y-1">
                        {errorSummary.messages.map((message, index) => (
                          <li key={index}>{message}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Basic Information</h4>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Name *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter customer name"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address *</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter email address"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormDescription>
                      This email will be used for booking confirmations and invoices.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder="Enter phone number"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Company Information */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Company Information</h4>
              
              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter company name"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="specialization"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Specialization</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Software Development"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="industry"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Industry</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Technology"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Online Presence */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Online Presence</h4>
              
              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input
                        type="url"
                        placeholder="https://example.com"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="linkedIn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>LinkedIn Profile</FormLabel>
                    <FormControl>
                      <Input
                        type="url"
                        placeholder="https://linkedin.com/in/username"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="socialMedia"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Other Social Media</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Twitter, Instagram, etc."
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Additional Information */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Additional Information</h4>
              
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Any additional notes about the customer..."
                        className="min-h-[80px]"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormDescription>
                      Internal notes about the customer (not visible to the customer).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
              {/* Form validation status */}
              <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2 sm:mb-0">
                {form.formState.isValid && !serverError ? (
                  <div className="flex items-center text-green-600">
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Form is valid
                  </div>
                ) : hasFormErrors(form.formState.errors) || serverError ? (
                  <div className="flex items-center text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errorSummary.errorCount} error{errorSummary.errorCount !== 1 ? 's' : ''}
                  </div>
                ) : null}
              </div>
              
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={!canSubmit}
                  className="min-w-[140px]"
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isEdit ? "Update Customer" : "Create Customer"}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
})

CustomerFormDialog.displayName = "CustomerFormDialog"