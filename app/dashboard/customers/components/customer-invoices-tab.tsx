"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  FileText, 
  AlertCircle, 
  RefreshCw, 
  Edit,
  Calendar,
  DollarSign,
  CreditCard,
  CheckCircle2,
  Clock,
  User
} from "lucide-react";
import { Invoice, InvoiceStatus } from "@/lib/types";
import { formatCurrency, formatDate } from "@/lib/utils";
import { format } from "date-fns";

interface CustomerInvoicesTabProps {
  customerId: number;
}

interface InvoiceWithBooking {
  id: number;
  bookingId: number;
  status: InvoiceStatus;
  total: number;
  paid: number;
  createdAt: Date;
  updatedAt: Date;
  booking: {
    id: number;
    start: Date;
    end: Date;
    resources: { name: string }[];
    customer: {
      name: string;
    };
  };
  lineItems?: {
    description: string;
    qty: number;
    amount: number;
  }[];
}

export function CustomerInvoicesTab({ customerId }: CustomerInvoicesTabProps) {
  const [invoices, setInvoices] = useState<InvoiceWithBooking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");

  const fetchInvoices = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      
      const response = await fetch(`/api/customers/${customerId}/invoices`);
      if (!response.ok) {
        throw new Error("Failed to fetch invoices");
      }
      
      const data = await response.json();
      setInvoices(data);
    } catch (error) {
      console.error("Error fetching invoices:", error);
      setError("Failed to load invoices");
    } finally {
      setLoading(false);
    }
  }, [customerId]);

  useEffect(() => {
    fetchInvoices();
  }, [fetchInvoices]);

  const getStatusColor = (status: InvoiceStatus) => {
    switch (status) {
      case 'PAID':
        return 'bg-green-100 border-green-300 text-green-800';
      case 'PARTIALLY_PAID':
        return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'PENDING':
        return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case 'CANCELLED':
        return 'bg-red-100 border-red-300 text-red-800';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  const getStatusIcon = (status: InvoiceStatus) => {
    switch (status) {
      case 'PAID':
        return <CheckCircle2 className="h-4 w-4" />;
      case 'PARTIALLY_PAID':
        return <Clock className="h-4 w-4" />;
      case 'PENDING':
        return <Clock className="h-4 w-4" />;
      case 'CANCELLED':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PAID":
        return "Paid";
      case "PARTIALLY_PAID":
        return "Partially Paid";
      case "PENDING":
        return "Pending";
      case "CANCELLED":
        return "Cancelled";
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              onClick={fetchInvoices}
              className="ml-2"
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (invoices.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Customer Invoices</h3>
          <Button
            variant="outline"
            onClick={fetchInvoices}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
        </div>
        
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Invoices Found</h3>
            <p className="text-gray-500 text-center max-w-md">
              This customer doesn't have any invoices yet. Invoices will appear here once bookings are created and invoiced.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Customer Invoices</h3>
        <Button
          variant="outline"
          onClick={fetchInvoices}
          disabled={loading}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
          Refresh
        </Button>
      </div>

      <div className="space-y-4">
        {invoices.map((invoice) => (
          <Card key={invoice.id}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Invoice #{invoice.id}
                </div>
                <Badge variant="outline" className={getStatusColor(invoice.status as InvoiceStatus)}>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(invoice.status as InvoiceStatus)}
                    {invoice.status.replace('_', ' ')}
                  </div>
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Invoice Summary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <DollarSign className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Total Amount</p>
                    <p className="font-semibold">{Number(invoice.total).toLocaleString('en-US')} IQD</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Paid Amount</p>
                    <p className="font-semibold">{Number(invoice.paid).toLocaleString('en-US')} IQD</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Clock className="h-4 w-4 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Remaining</p>
                    <p className="font-semibold">{(Number(invoice.total) - Number(invoice.paid)).toLocaleString('en-US')} IQD</p>
                  </div>
                </div>
              </div>

              {/* Invoice Details */}
              <div className="border-t pt-4">
                <h4 className="font-medium text-gray-900 mb-3">Invoice Details</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">Booking Date:</span>
                    <span>{formatDate(invoice.booking.start)}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">Created:</span>
                    <span>
                      {invoice.createdAt 
                        ? (() => {
                            try {
                              return format(new Date(invoice.createdAt), 'MMM d, yyyy h:mm a');
                            } catch (error) {
                              return 'Invalid date';
                            }
                          })()
                        : 'N/A'
                      }
                    </span>
                  </div>

                  {invoice.booking.resources.length > 0 && (
                    <div className="flex items-start gap-2">
                      <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
                      <span className="text-gray-600">Resources:</span>
                      <span>{invoice.booking.resources.map(r => r.name).join(", ")}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="border-t pt-4 flex items-center justify-end gap-2">
                <Link href={`/dashboard/invoices/${invoice.id}`}>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    <Edit className="h-4 w-4" />
                    Edit Invoice
                  </Button>
                </Link>
              </div>

              {/* Payment Status */}
              {invoice.status !== 'PAID' && invoice.status !== 'CANCELLED' && (
                <div className="border-t pt-4">
                  <Alert>
                    <Clock className="h-4 w-4" />
                    <AlertDescription>
                      {invoice.status === 'PENDING' 
                        ? "This invoice is pending payment. No payments have been recorded yet."
                        : `This invoice is partially paid. ${(Number(invoice.total) - Number(invoice.paid)).toLocaleString('en-US')} IQD remaining.`
                      }
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}