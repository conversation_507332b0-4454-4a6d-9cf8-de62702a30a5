"use client"

import { useState, memo, useMemo, useCallback } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Users,
  Mail,
  Phone,
  Building2,
} from "lucide-react"
import { Customer } from "@/lib/types"
import { format } from "date-fns"
import { CustomersTableSkeleton } from "./customers-table-skeleton"
import { FilterErrorState } from "@/components/ui/filter-error-state"
import { OfflineStateHandler } from "@/components/ui/offline-state-handler"

interface CustomersTableProps {
  customers: Customer[]
  loading: boolean
  error?: Error | string | null
  onEdit: (customer: Customer) => void
  onDelete: (customerId: number) => void
  onRefresh: () => void
  retryCount?: number
  isRetrying?: boolean
  onRetry?: () => void
}

type SortField = 'name' | 'email' | 'companyName' | 'createdAt'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

// Mobile card component for individual customers - memoized for performance
const CustomerCard = memo(({ 
  customer, 
  onEdit, 
  onDelete 
}: { 
  customer: Customer
  onEdit: (customer: Customer) => void
  onDelete: (customerId: number) => void
}) => {
  return (
    <Card className="mb-4">
      <CardContent className="mobile-spacing">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <div 
                className="flex items-center justify-center w-10 h-10 rounded-md bg-muted"
                aria-hidden="true"
              >
                <Users className="h-5 w-5" />
              </div>
              <h3 className="font-medium text-base" id={`customer-${customer.id}`}>
                {customer.name}
              </h3>
            </div>
            
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Mail className="h-3 w-3" aria-hidden="true" />
                <span className="break-all">{customer.email}</span>
              </div>
              
              {customer.phoneNumber && (
                <div className="flex items-center space-x-2">
                  <Phone className="h-3 w-3" aria-hidden="true" />
                  <span>{customer.phoneNumber}</span>
                </div>
              )}
              
              {customer.companyName && (
                <div className="flex items-center space-x-2">
                  <Building2 className="h-3 w-3" aria-hidden="true" />
                  <span>{customer.companyName}</span>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Bookings:</span>
                <Badge variant="outline">
                  {customer._count?.bookings || 0}
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Created:</span>
                <span>{format(new Date(customer.createdAt), 'MMM dd, yyyy')}</span>
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="h-11 w-11 p-0 touch-target focus-enhanced"
                aria-label={`Actions for ${customer.name}`}
              >
                <span className="sr-only">Open actions menu for {customer.name}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem 
                onClick={() => onEdit(customer)}
                className="cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                Edit {customer.name}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(customer.id)}
                className="text-destructive cursor-pointer touch-target focus-enhanced"
                role="menuitem"
              >
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                Delete {customer.name}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
})

export const CustomersTable = memo(({
  customers,
  loading,
  error,
  onEdit,
  onDelete,
  onRefresh,
  retryCount = 0,
  isRetrying = false,
  onRetry,
}: CustomersTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'createdAt',
    direction: 'desc',
  })

  const handleSort = useCallback((field: SortField) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }, [])

  const getSortIcon = useCallback((field: SortField) => {
    if (sortConfig.field !== field) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    )
  }, [sortConfig.field, sortConfig.direction])

  const sortedCustomers = useMemo(() => {
    return [...customers].sort((a, b) => {
      const { field, direction } = sortConfig
      let aValue: any = a[field]
      let bValue: any = b[field]

      // Handle date sorting
      if (field === 'createdAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Handle string sorting (case insensitive)
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue?.toLowerCase() || ''
      }

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0
      if (aValue == null) return direction === 'asc' ? -1 : 1
      if (bValue == null) return direction === 'asc' ? 1 : -1

      if (aValue < bValue) {
        return direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [customers, sortConfig])

  // Memoize handlers to prevent unnecessary re-renders
  const handleRetryAction = useMemo(() => onRetry || onRefresh, [onRetry, onRefresh])
  
  // Memoize edit handler
  const handleEditCustomer = useCallback((customer: Customer) => {
    onEdit(customer)
  }, [onEdit])
  
  // Memoize delete handler  
  const handleDeleteCustomer = useCallback((customerId: number) => {
    onDelete(customerId)
  }, [onDelete])

  if (loading) {
    return (
      <OfflineStateHandler onConnectionRestored={onRefresh}>
        <CustomersTableSkeleton />
      </OfflineStateHandler>
    )
  }

  if (error) {
    return (
      <OfflineStateHandler onConnectionRestored={onRefresh}>
        <FilterErrorState
          error={error}
          filterType="customer"
          onRetry={handleRetryAction}
          loading={isRetrying}
          showDetails={process.env.NODE_ENV === 'development'}
          className="mb-4"
        />
        
        {/* Show empty table structure with error state for context */}
        <div className="opacity-50 pointer-events-none">
          <CustomersTableSkeleton />
        </div>
      </OfflineStateHandler>
    )
  }

  if (customers.length === 0) {
    return (
      <>
        {/* Mobile empty state */}
        <div className="block md:hidden">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center justify-center space-y-4">
                <Users className="h-12 w-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">No customers found</p>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your search or add a new customer
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  onClick={onRefresh} 
                  className="touch-target focus-enhanced px-6"
                  aria-label="Refresh customers list"
                >
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop empty state */}
        <div className="hidden md:block rounded-md border">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('name')}
                      className="h-auto p-0 font-semibold"
                    >
                      Name
                      {getSortIcon('name')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[200px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('email')}
                      className="h-auto p-0 font-semibold"
                    >
                      Email
                      {getSortIcon('email')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">Phone</TableHead>
                  <TableHead className="min-w-[150px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('companyName')}
                      className="h-auto p-0 font-semibold"
                    >
                      Company
                      {getSortIcon('companyName')}
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-[100px] hidden lg:table-cell">Bookings</TableHead>
                  <TableHead className="min-w-[120px] hidden lg:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('createdAt')}
                      className="h-auto p-0 font-semibold"
                    >
                      Created
                      {getSortIcon('createdAt')}
                    </Button>
                  </TableHead>
                  <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <p className="text-muted-foreground">No customers found</p>
                      <Button 
                        variant="outline" 
                        onClick={onRefresh} 
                        className="touch-target focus-enhanced px-6"
                        aria-label="Refresh customers list"
                      >
                        Refresh
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </>
    )
  }

  return (
    <OfflineStateHandler onConnectionRestored={onRefresh}>
      <>
        {/* Mobile card view */}
        <div className="block md:hidden">
        {/* Mobile sort controls */}
        <div className="flex items-center justify-between mb-4 p-4 bg-muted/50 rounded-lg mobile-spacing">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium" id="sort-label">Sort by:</span>
            <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="touch-target focus-enhanced"
                aria-labelledby="sort-label"
                aria-expanded="false"
              >
                {sortConfig.field === 'name' && 'Name'}
                {sortConfig.field === 'email' && 'Email'}
                {sortConfig.field === 'companyName' && 'Company'}
                {sortConfig.field === 'createdAt' && 'Created'}
                {sortConfig.direction === 'asc' ? (
                  <ArrowUp className="ml-2 h-4 w-4" aria-hidden="true" />
                ) : (
                  <ArrowDown className="ml-2 h-4 w-4" aria-hidden="true" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40" role="menu">
              <DropdownMenuItem 
                onClick={() => handleSort('name')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Name {getSortIcon('name')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('email')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Email {getSortIcon('email')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('companyName')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Company {getSortIcon('companyName')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleSort('createdAt')}
                className="touch-target focus-enhanced"
                role="menuitem"
              >
                Created {getSortIcon('createdAt')}
              </DropdownMenuItem>
            </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="space-y-4">
          {sortedCustomers.map((customer) => (
            <CustomerCard
              key={customer.id}
              customer={customer}
              onEdit={handleEditCustomer}
              onDelete={handleDeleteCustomer}
            />
          ))}
        </div>
      </div>

      {/* Desktop table view */}
      <div className="hidden md:block rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[150px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('name')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by name ${sortConfig.field === 'name' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Name
                    {getSortIcon('name')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[200px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('email')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by email ${sortConfig.field === 'email' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Email
                    {getSortIcon('email')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">Phone</TableHead>
                <TableHead className="min-w-[150px]">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('companyName')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by company ${sortConfig.field === 'companyName' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Company
                    {getSortIcon('companyName')}
                  </Button>
                </TableHead>
                <TableHead className="min-w-[100px] hidden lg:table-cell">Bookings</TableHead>
                <TableHead className="min-w-[120px] hidden lg:table-cell">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort('createdAt')}
                    className="h-auto p-2 font-semibold focus-enhanced"
                    aria-label={`Sort by creation date ${sortConfig.field === 'createdAt' ? (sortConfig.direction === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
                  >
                    Created
                    {getSortIcon('createdAt')}
                  </Button>
                </TableHead>
                <TableHead className="w-[70px] min-w-[70px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedCustomers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell className="font-medium">
                    {customer.name}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Mail className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
                      <span className="break-all">{customer.email}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    {customer.phoneNumber ? (
                      <div className="flex items-center space-x-2">
                        <Phone className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
                        <span>{customer.phoneNumber}</span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">—</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {customer.companyName ? (
                      <div className="flex items-center space-x-2">
                        <Building2 className="h-3 w-3 text-muted-foreground" aria-hidden="true" />
                        <span>{customer.companyName}</span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">—</span>
                    )}
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <Badge variant="outline">
                      {customer._count?.bookings || 0}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-muted-foreground hidden lg:table-cell">
                    {format(new Date(customer.createdAt), 'MMM dd, yyyy')}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          className="h-10 w-10 p-0 focus-enhanced"
                          aria-label={`Actions for ${customer.name}`}
                        >
                          <span className="sr-only">Open actions menu for {customer.name}</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48" role="menu">
                        <DropdownMenuItem 
                          onClick={() => handleEditCustomer(customer)}
                          className="cursor-pointer focus-enhanced py-3"
                          role="menuitem"
                        >
                          <Edit className="mr-2 h-4 w-4" aria-hidden="true" />
                          Edit {customer.name}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteCustomer(customer.id)}
                          className="text-destructive cursor-pointer focus-enhanced py-3"
                          role="menuitem"
                        >
                          <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                          Delete {customer.name}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
      </>
    </OfflineStateHandler>
  )
})

CustomerCard.displayName = "CustomerCard"
CustomersTable.displayName = "CustomersTable"