"use client";

import { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, CheckCircle2, Save, X } from "lucide-react";
import { Customer, CustomerFormData } from "@/lib/types";
import { customerCreateSchema, customerUpdateSchema } from "@/lib/validations/customer";
import { 
  mapServerErrorsToForm, 
  getApiErrorMessage, 
  createErrorSummary,
  hasFormErrors 
} from "@/lib/utils/form-errors";

interface CustomerInformationFormProps {
  customer?: Customer | null; // null for create, Customer object for edit
  onSubmit: (data: CustomerFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  submitButtonText?: string;
}

export const CustomerInformationForm = memo(({
  customer,
  onSubmit,
  onCancel,
  loading = false,
  submitButtonText = "Save Customer",
}: CustomerInformationFormProps) => {
  const isEdit = !!customer;
  const schema = isEdit ? customerUpdateSchema : customerCreateSchema;
  const [serverError, setServerError] = useState<string>("");

  const form = useForm<CustomerFormData>({
    resolver: zodResolver(schema),
    mode: "onChange", // Enable real-time validation
    defaultValues: {
      name: "",
      email: "",
      phoneNumber: "",
      companyName: "",
      specialization: "",
      industry: "",
      website: "",
      linkedIn: "",
      socialMedia: "",
      notes: "",
    },
  });

  // Reset form when customer changes
  useEffect(() => {
    setServerError(""); // Clear server errors when customer changes
    if (isEdit && customer) {
      form.reset({
        name: customer.name,
        email: customer.email || "",
        phoneNumber: customer.phoneNumber || "",
        companyName: customer.companyName || "",
        specialization: customer.specialization || "",
        industry: customer.industry || "",
        website: customer.website || "",
        linkedIn: customer.linkedIn || "",
        socialMedia: customer.socialMedia || "",
        notes: customer.notes || "",
      });
    } else {
      form.reset({
        name: "",
        email: "",
        phoneNumber: "",
        companyName: "",
        specialization: "",
        industry: "",
        website: "",
        linkedIn: "",
        socialMedia: "",
        notes: "",
      });
    }
  }, [isEdit, customer, form]);

  const handleSubmit = useCallback(async (data: CustomerFormData) => {
    setServerError(""); // Clear previous server errors
    
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
      
      // Handle server validation errors
      if (error && typeof error === 'object' && 'details' in error) {
        mapServerErrorsToForm(error as any, form.setError as any);
      }
      
      // Set general server error message
      const errorMessage = getApiErrorMessage(error);
      setServerError(errorMessage);
    }
  }, [onSubmit, form]);

  const handleCancel = useCallback(() => {
    form.reset();
    setServerError("");
    onCancel();
  }, [form, onCancel]);

  // Get error summary for display - memoized to prevent unnecessary recalculations
  const errorSummary = useMemo(() => 
    createErrorSummary(form.formState.errors, serverError), 
    [form.formState.errors, serverError]
  );
  
  const canSubmit = useMemo(() => 
     !loading, 
    [loading]
  );

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-6"
      >
        {/* Error Summary */}
        {errorSummary.hasErrors && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">{errorSummary.summary}</p>
                {errorSummary.messages.length > 1 && (
                  <ul className="list-disc list-inside text-sm space-y-1">
                    {errorSummary.messages.map((message, index) => (
                      <li key={index}>{message}</li>
                    ))}
                  </ul>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Basic Information */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 border-b pb-2">Basic Information</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Customer Name <span className="text-destructive" aria-label="required">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter customer name"
                      {...field}
                      disabled={loading}
                      className="focus-enhanced"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter email address (optional)"
                      {...field}
                      disabled={loading}
                      className="focus-enhanced"
                    />
                  </FormControl>
                  <FormDescription>
                    If provided, this email will be used for booking confirmations and invoices.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Phone Number <span className="text-destructive" aria-label="required">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="tel"
                    placeholder="Enter phone number"
                    {...field}
                    disabled={loading}
                    className="focus-enhanced"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Company Information */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 border-b pb-2">Company Information</h4>
          
          <FormField
            control={form.control}
            name="companyName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter company name"
                    {...field}
                    disabled={loading}
                    className="focus-enhanced"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="specialization"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Specialization</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Software Development"
                      {...field}
                      disabled={loading}
                      className="focus-enhanced"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="industry"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Industry</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Technology"
                      {...field}
                      disabled={loading}
                      className="focus-enhanced"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Online Presence */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 border-b pb-2">Online Presence</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Website</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://example.com"
                      {...field}
                      disabled={loading}
                      className="focus-enhanced"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="linkedIn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>LinkedIn Profile</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://linkedin.com/in/username"
                      {...field}
                      disabled={loading}
                      className="focus-enhanced"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="socialMedia"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Other Social Media</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Twitter, Instagram, etc."
                    {...field}
                    disabled={loading}
                    className="focus-enhanced"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Additional Information */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 border-b pb-2">Additional Information</h4>
          
          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Any additional notes about the customer..."
                    className="min-h-[100px] focus-enhanced"
                    {...field}
                    disabled={loading}
                  />
                </FormControl>
                <FormDescription>
                  Internal notes about the customer (not visible to the customer).
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center pt-6 border-t space-y-4 sm:space-y-0">
          {/* Form validation status */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            {form.formState.isValid && !serverError ? (
              <div className="flex items-center text-green-600">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Form is valid
              </div>
            ) : hasFormErrors(form.formState.errors) || serverError ? (
              <div className="flex items-center text-red-600">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errorSummary.errorCount} error{errorSummary.errorCount !== 1 ? 's' : ''}
              </div>
            ) : null}
          </div>
          
          <div className="flex space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
              className="min-w-[120px]"
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!canSubmit}
              className="min-w-[140px]"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {!loading && <Save className="mr-2 h-4 w-4" />}
              {submitButtonText}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
});

CustomerInformationForm.displayName = "CustomerInformationForm";