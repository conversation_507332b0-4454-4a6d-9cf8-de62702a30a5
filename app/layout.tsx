"use client"
import { <PERSON><PERSON><PERSON>, Space_Grotesk } from 'next/font/google'
import './globals.css'
import { Toaster } from 'sonner'
import { useLanguageStore } from '@/components/language-provider'

const rubik = Space_Grotesk({ subsets: ['latin'] })

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const language = useLanguageStore(state => state.language);
  const dir = language === 'ar' ? 'rtl' : 'ltr';
  return (
    <html lang={language} dir={dir}>
      <body className={rubik.className + ' ' + dir}>
        {children}
        <Toaster />
      </body>
    </html>
  )
}
