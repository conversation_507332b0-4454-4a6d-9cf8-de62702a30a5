import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const cateringId = parseInt((await params).id);
    
    if (isNaN(cateringId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid catering ID',
          message: 'Catering ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if catering offer exists
    const existingCatering = await prisma.catering.findUnique({
      where: { id: cateringId }
    });

    if (!existingCatering) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Catering offer not found',
          message: 'The requested catering offer does not exist'
        },
        { status: 404 }
      );
    }

    // Count booking associations
    const bookingCount = await prisma.cateringOnBooking.count({
      where: { cateringId }
    });

    return NextResponse.json({
      success: true,
      data: { count: bookingCount }
    });
  } catch (error) {
    console.error('Error checking catering booking associations:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/catering/${(await params).id}/bookings`,
      cateringId: (await params).id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while checking booking associations. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}