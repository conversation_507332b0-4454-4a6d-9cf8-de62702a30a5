import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { organizationSettingsSchema } from '@/lib/validations/settings';

// Enforce singleton: at most one OrganizationSettings row
export async function GET() {
  try {
    const prismaClient = await prisma;
    const existing = await prismaClient.organizationSettings.findFirst();
    return NextResponse.json({ success: true, data: existing ?? null });
  } catch (error) {
    console.error('GET /api/settings error', error);
    return NextResponse.json({ success: false, error: 'Failed to fetch settings' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const prismaClient = await prisma;
    const body = await request.json();
    const data = organizationSettingsSchema.parse(body);

    // Ensure only one row exists
    const existing = await prismaClient.organizationSettings.findFirst();

    const saved = existing
      ? await prismaClient.organizationSettings.update({ where: { id: existing.id }, data })
      : await prismaClient.organizationSettings.create({ data });

    return NextResponse.json({ success: true, data: saved });
  } catch (error: any) {
    if (error?.name === 'ZodError') {
      return NextResponse.json(
        { success: false, error: 'Invalid settings payload', details: error.flatten?.() ?? String(error) },
        { status: 400 }
      );
    }
    console.error('PUT /api/settings error', error);
    return NextResponse.json({ success: false, error: 'Failed to save settings' }, { status: 500 });
  }
}


