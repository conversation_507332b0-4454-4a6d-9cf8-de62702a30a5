import { NextRequest, NextResponse } from 'next/server';
import { put } from '@vercel/blob';
import crypto from 'crypto';

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = new Set(['image/png', 'image/jpeg', 'image/webp', 'image/svg+xml']);

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');

    if (!file || typeof file === 'string') {
      return NextResponse.json({ success: false, error: 'No file uploaded' }, { status: 400 });
    }

    const blob = file as File;
    if (!ALLOWED_TYPES.has(blob.type)) {
      return NextResponse.json({ success: false, error: 'Unsupported file type' }, { status: 400 });
    }

    if (blob.size > MAX_FILE_SIZE) {
      return NextResponse.json({ success: false, error: 'File too large' }, { status: 400 });
    }

    const ext = mimeToExt(blob.type);
    const hash = crypto.createHash('sha256').update(Buffer.from(await blob.arrayBuffer())).digest('hex').slice(0, 16);
    const fileName = `org-logo-${hash}${ext}`;

    // Upload to Vercel Blob Storage
    const blobResult = await put(fileName, blob, {
      access: 'public',
      addRandomSuffix: false, // We control the filename with hash
      allowOverwrite: true, // Allow overwriting existing files with same name
    });

    return NextResponse.json({ success: true, data: { url: blobResult.url } });
  } catch (error) {
    console.error('POST /api/settings/logo error', error);
    return NextResponse.json({ success: false, error: 'Upload failed' }, { status: 500 });
  }
}

function mimeToExt(mime: string): string {
  switch (mime) {
    case 'image/png':
      return '.png';
    case 'image/jpeg':
      return '.jpg';
    case 'image/webp':
      return '.webp';
    case 'image/svg+xml':
      return '.svg';
    default:
      return '';
  }
}


