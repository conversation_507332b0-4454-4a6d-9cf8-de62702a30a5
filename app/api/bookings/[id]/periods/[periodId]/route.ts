import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { periodService } from '@/lib/services/period-service';
import { conflictDetectionService } from '@/lib/services/conflict-detection';
import { 
  periodUpdateSchema,
  recurringPeriodDeleteSchema,
  type PeriodUpdateInput,
  type RecurringPeriodDeleteInput 
} from '@/lib/validations/period';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
    periodId: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id, periodId } = await params;
    const bookingId = parseInt(id);
    const periodIdNum = parseInt(periodId);
    
    if (isNaN(bookingId) || isNaN(periodIdNum)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid ID',
          message: 'Booking ID and Period ID must be valid numbers'
        },
        { status: 400 }
      );
    }

    // Get the period
    const period = await periodService.getPeriodById(periodIdNum);

    if (!period) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Period not found',
          message: 'The requested period does not exist'
        },
        { status: 404 }
      );
    }

    // Verify the period belongs to the specified booking
    if (period.bookingId !== bookingId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Period not found',
          message: 'The requested period does not belong to this booking'
        },
        { status: 404 }
      );
    }

    // Check for conflicts if requested
    const url = new URL(request.url);
    const checkConflicts = url.searchParams.get('checkConflicts') === 'true';
    
    let conflictResult = null;
    if (checkConflicts) {
      const resourceIds = period.booking.resources.map(r => r.id);
      const periodFormData = {
        start: period.start,
        end: period.end,
        isRecurring: period.isRecurring,
        recurrenceRule: period.recurrenceRule || undefined
      };
      
      conflictResult = await conflictDetectionService.checkExistingPeriodConflicts(
        periodIdNum,
        periodFormData,
        resourceIds
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        period,
        conflicts: conflictResult
      }
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error fetching period:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/bookings/${resolvedParams.id}/periods/${resolvedParams.periodId}`,
      bookingId: resolvedParams.id,
      periodId: resolvedParams.periodId
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the period. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const { id, periodId } = await params;
    const bookingId = parseInt(id);
    const periodIdNum = parseInt(periodId);
    
    if (isNaN(bookingId) || isNaN(periodIdNum)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid ID',
          message: 'Booking ID and Period ID must be valid numbers'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Convert date strings to Date objects if needed
    if (body.start && typeof body.start === 'string') {
      body.start = new Date(body.start);
    }
    if (body.end && typeof body.end === 'string') {
      body.end = new Date(body.end);
    }
    
    // Validate input data
    const validatedData: PeriodUpdateInput = periodUpdateSchema.parse(body);

    // Check if period exists and belongs to the booking
    const existingPeriod = await periodService.getPeriodById(periodIdNum);

    if (!existingPeriod) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Period not found',
          message: 'The requested period does not exist'
        },
        { status: 404 }
      );
    }

    if (existingPeriod.bookingId !== bookingId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Period not found',
          message: 'The requested period does not belong to this booking'
        },
        { status: 404 }
      );
    }

    // Check for resource conflicts with the updated period
    const resourceIds = existingPeriod.booking.resources.map(r => r.id);
    const conflictResult = await conflictDetectionService.checkExistingPeriodConflicts(
      periodIdNum,
      validatedData,
      resourceIds
    );

    if (conflictResult.hasConflicts) {
      const conflictDetails = conflictResult.conflicts.map(conflict => ({
        id: conflict.conflictingBooking.id,
        customer: conflict.conflictingBooking.customerName,
        resources: conflict.conflictingResources.map(r => r.name),
        overlapStart: conflict.overlapStart,
        overlapEnd: conflict.overlapEnd,
        status: conflict.conflictingBooking.status
      }));

      return NextResponse.json(
        { 
          success: false,
          error: 'Resource conflict',
          message: conflictResult.message,
          conflicts: conflictDetails,
          affectedResources: conflictResult.affectedResources
        },
        { status: 409 }
      );
    }

    // Update the period
    const updatedPeriod = await periodService.updatePeriod(periodIdNum, validatedData);

    return NextResponse.json({
      success: true,
      data: {
        period: updatedPeriod,
        message: 'Period updated successfully'
      }
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error updating period:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `PUT /api/bookings/${resolvedParams.id}/periods/${resolvedParams.periodId}`,
      bookingId: resolvedParams.id,
      periodId: resolvedParams.periodId,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the period information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle service-level errors
    if (error instanceof Error) {
      if (error.message.includes('overlaps') || error.message.includes('conflict')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Period conflict',
            message: error.message
          },
          { status: 409 }
        );
      }
      
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Period not found',
            message: error.message
          },
          { status: 404 }
        );
      }
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Period not found',
              message: 'The period you are trying to update no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the period. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const { id, periodId } = await params;
    const bookingId = parseInt(id);
    const periodIdNum = parseInt(periodId);
    
    if (isNaN(bookingId) || isNaN(periodIdNum)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid ID',
          message: 'Booking ID and Period ID must be valid numbers'
        },
        { status: 400 }
      );
    }

    // Parse request body for recurring series handling
    try {
      body = await request.json();
    } catch {
      // If no body provided, use default values
      body = {};
    }

    const deleteOptions: RecurringPeriodDeleteInput = recurringPeriodDeleteSchema.parse(body);

    // Check if period exists and belongs to the booking
    const existingPeriod = await periodService.getPeriodById(periodIdNum);

    if (!existingPeriod) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Period not found',
          message: 'The requested period does not exist'
        },
        { status: 404 }
      );
    }

    if (existingPeriod.bookingId !== bookingId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Period not found',
          message: 'The requested period does not belong to this booking'
        },
        { status: 404 }
      );
    }

    // Handle recurring series deletion
    const deleteRecurringSeries = deleteOptions.deleteType === 'series';
    
    // For future deletion, we need to implement logic to delete this and future periods
    if (deleteOptions.deleteType === 'future') {
      // This would require additional logic in the period service
      // For now, we'll treat it as single deletion
      console.warn('Future deletion not fully implemented, treating as single deletion');
    }

    // Delete the period(s)
    await periodService.deletePeriod(periodIdNum, deleteRecurringSeries);

    const message = deleteRecurringSeries 
      ? 'Recurring period series deleted successfully'
      : 'Period deleted successfully';

    return NextResponse.json({
      success: true,
      message
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error deleting period:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `DELETE /api/bookings/${resolvedParams.id}/periods/${resolvedParams.periodId}`,
      bookingId: resolvedParams.id,
      periodId: resolvedParams.periodId,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the deletion options and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle service-level errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Period not found',
            message: error.message
          },
          { status: 404 }
        );
      }
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Period not found',
              message: 'The period you are trying to delete no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the period. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}