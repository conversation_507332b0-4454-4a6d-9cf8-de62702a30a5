import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { bookingUpdateSchema } from '@/lib/validations/booking';
import { periodService } from '@/lib/services/period-service';
import { conflictDetectionService } from '@/lib/services/conflict-detection';
import { recurrenceEngine } from '@/lib/services/recurrence-engine';
import { computeBookingTimeRange } from '@/lib/types';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const bookingId = parseInt(id);
    
    if (isNaN(bookingId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid booking ID',
          message: 'Booking ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const booking = await prisma.booking.findUnique({
      where: { id: bookingId },
      select: {
        id: true,
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phoneNumber: true,
            companyName: true
          }
        },
        customerId: true,
        resources: {
          select: {
            id: true,
            name: true,
            type: true,
            basePrice: true
          }
        },
        status: true,
        periods: {
          select: {
            id: true,
            start: true,
            end: true,
            isRecurring: true,
            recurrenceRule: true,
            parentPeriodId: true,
            createdAt: true,
            updatedAt: true
          },
          orderBy: {
            start: 'asc'
          }
        },
        invoice: {
          select: {
            id: true,
            status: true,
            total: true,
            paid: true,
            createdAt: true,
            booking: {
              select: {
                customer: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            },
            lineItems: {
              select: {
                id: true,
                description: true,
                amount: true,
                qty: true,
                isCatering: true
              }
            }
          }
        },
        caterings: {
          select: {
            id: true,
            quantity: true,
            catering: {
              select: {
                id: true,
                offerName: true,
                pricePerPerson: true
              }
            }
          }
        },
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    if (!booking) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Booking not found',
          message: 'The requested booking does not exist'
        },
        { status: 404 }
      );
    }

    // Add computed start/end times for backward compatibility
    let computedStart: Date | undefined;
    let computedEnd: Date | undefined;
    
    if (booking.periods && booking.periods.length > 0) {
      try {
        // Create minimal period objects for time range calculation
        const periodObjects = booking.periods.map(p => ({
          id: p.id,
          bookingId: booking.id,
          start: p.start,
          end: p.end,
          isRecurring: p.isRecurring,
          recurrenceRule: p.recurrenceRule ? JSON.parse(p.recurrenceRule as string) : null,
          parentPeriodId: p.parentPeriodId,
          booking: booking as any, // We don't need the full booking object for time calculation
          createdAt: p.createdAt,
          updatedAt: p.updatedAt,
          createdById: null,
          updatedById: null
        }));
        const timeRange = computeBookingTimeRange(periodObjects);
        computedStart = timeRange.start;
        computedEnd = timeRange.end;
      } catch (error) {
        console.warn(`Failed to compute time range for booking ${booking.id}:`, error);
      }
    }

    const bookingWithComputedTimes = {
      ...booking,
      computedStart,
      computedEnd,
      periods: booking.periods.map(p => ({
        ...p,
        recurrenceRule: p.recurrenceRule ? JSON.parse(p.recurrenceRule as string) : null
      }))
    };

    // Transform embedded invoice to align with frontend expectations
    const transformedInvoice = bookingWithComputedTimes.invoice
      ? {
          ...bookingWithComputedTimes.invoice,
          total: Number(bookingWithComputedTimes.invoice.total),
          paid: Number(bookingWithComputedTimes.invoice.paid),
          lineItems: bookingWithComputedTimes.invoice.lineItems.map((item) => ({
            ...item,
            amount: Number(item.amount),
            quantity: (item as any).qty,
          })),
        }
      : null;

    return NextResponse.json({
      success: true,
      data: {
        ...bookingWithComputedTimes,
        invoice: transformedInvoice,
      }
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error fetching booking:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/bookings/${resolvedParams.id}`,
      bookingId: resolvedParams.id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the booking. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const { id } = await params;
    const bookingId = parseInt(id);
    
    if (isNaN(bookingId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid booking ID',
          message: 'Booking ID must be a valid number'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Convert date strings to Date objects in periods if needed
    if (body.periods && Array.isArray(body.periods)) {
      body.periods = body.periods.map((period: any) => ({
        ...period,
        start: typeof period.start === 'string' ? new Date(period.start) : period.start,
        end: typeof period.end === 'string' ? new Date(period.end) : period.end
      }));
    }
    
    // Validate input data
    const validatedData = bookingUpdateSchema.parse(body);

    // Check if booking exists
    const existingBooking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        resources: true,
        caterings: true,
        periods: true
      }
    });

    if (!existingBooking) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Booking not found',
          message: 'The requested booking does not exist'
        },
        { status: 404 }
      );
    }

    // Generate all periods (including recurring) for conflict checking
    console.log('Generating all periods for conflict checking...');
    const allPeriodsForConflictCheck = [];
    for (const periodData of validatedData.periods) {
      if (periodData.isRecurring && periodData.recurrenceRule) {
        // Generate all periods from recurrence rule
        const generatedPeriods = recurrenceEngine.generatePeriodsFromFormData(periodData);
        allPeriodsForConflictCheck.push(...generatedPeriods.map(p => ({
          start: p.start,
          end: p.end,
          isRecurring: false, // For conflict checking, treat as individual periods
          recurrenceRule: undefined
        })));
      } else {
        // Single period
        allPeriodsForConflictCheck.push({
          start: periodData.start,
          end: periodData.end,
          isRecurring: false,
          recurrenceRule: undefined
        });
      }
    }

    // Check for resource conflicts using all generated periods
    console.log('Checking for resource conflicts with', allPeriodsForConflictCheck.length, 'periods...');
    const conflictResult = await conflictDetectionService.detectConflicts(
      allPeriodsForConflictCheck,
      validatedData.resourceIds,
      bookingId // Exclude current booking from conflict check
    );

    if (conflictResult.hasConflicts) {
      const conflictDetails = conflictResult.conflicts.map(conflict => ({
        id: conflict.conflictingBooking.id,
        customer: conflict.conflictingBooking.customerName,
        resources: conflict.conflictingResources.map(r => r.name),
        overlapStart: conflict.overlapStart,
        overlapEnd: conflict.overlapEnd,
        status: conflict.conflictingBooking.status
      }));

      return NextResponse.json(
        { 
          success: false,
          error: 'Resource conflict',
          message: conflictResult.message,
          conflicts: conflictDetails,
          affectedResources: conflictResult.affectedResources
        },
        { status: 409 }
      );
    }

    // Update the booking with transaction to handle resources, caterings, and periods
    const updatedBooking = await prisma.$transaction(async (tx) => {
      // Update the booking basic fields (no start/end fields)
      const booking = await tx.booking.update({
        where: { id: bookingId },
        data: {
          customerId: validatedData.customerId,
          status: validatedData.status,
          updatedAt: new Date(),
          // TODO: Add updatedById from authenticated user session
        }
      });

      // Update resources associations
      // First disconnect all existing resources
      await tx.booking.update({
        where: { id: bookingId },
        data: {
          resources: {
            set: [] // Disconnect all
          }
        }
      });

      // Then connect the new resources
      if (validatedData.resourceIds && validatedData.resourceIds.length > 0) {
        await tx.booking.update({
          where: { id: bookingId },
          data: {
            resources: {
              connect: validatedData.resourceIds.map(id => ({ id }))
            }
          }
        });
      }

      // Update periods - delete all existing periods first
      await tx.period.deleteMany({
        where: { bookingId: bookingId }
      });

      // Note: We'll create periods after the transaction completes to use the period service
      // which properly handles recurring periods

      // Update caterings
      // First delete all existing catering associations
      await tx.cateringOnBooking.deleteMany({
        where: { bookingId: bookingId }
      });

      // Then create new catering associations if provided
      if (validatedData.caterings && validatedData.caterings.length > 0) {
        await tx.cateringOnBooking.createMany({
          data: validatedData.caterings.map(catering => ({
            bookingId: bookingId,
            cateringId: catering.cateringId,
            quantity: catering.quantity,
            // TODO: Add createdById from authenticated user session
          }))
        });
      }

      // Return the booking without periods for now
      return await tx.booking.findUnique({
        where: { id: bookingId },
        select: {
          id: true,
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phoneNumber: true,
              companyName: true
            }
          },
          customerId: true,
          resources: {
            select: {
              id: true,
              name: true,
              type: true,
              basePrice: true
            }
          },
          status: true,
          periods: {
            select: {
              id: true,
              start: true,
              end: true,
              isRecurring: true,
              recurrenceRule: true,
              parentPeriodId: true,
              createdAt: true,
              updatedAt: true
            },
            orderBy: {
              start: 'asc'
            }
          },
          invoice: {
            select: {
              id: true,
              status: true,
              total: true,
              paid: true
            }
          },
          caterings: {
            select: {
              id: true,
              quantity: true,
              catering: {
                select: {
                  id: true,
                  offerName: true,
                  pricePerPerson: true
                }
              }
            }
          },
          createdAt: true,
          updatedAt: true,
          createdById: true,
          updatedById: true
        }
      });
    });

    if (!updatedBooking) {
      throw new Error('Failed to update booking');
    }

    console.log('Booking updated successfully:', JSON.stringify(updatedBooking, null, 2));

    // Create periods using the period service which handles recurrence properly
    // Skip overlap check since we just deleted all existing periods
    console.log('Creating periods for booking:', updatedBooking.id);
    const allCreatedPeriods = [];
    try {
      for (const periodData of validatedData.periods) {
        console.log('Creating period for data:', JSON.stringify(periodData, null, 2));
        const periodsForThisPeriod = await periodService.createPeriodsFromFormData(
          updatedBooking.id,
          periodData,
          true // Skip overlap check for updates
        );
        console.log('Created periods for this period:', periodsForThisPeriod.length);
        allCreatedPeriods.push(...periodsForThisPeriod);
      }
      console.log('Total periods created:', allCreatedPeriods.length);
    } catch (periodError) {
      console.error('Error creating periods:', periodError);
      throw periodError;
    }

    // Fetch the final updated booking with all periods
    console.log('Fetching final updated booking with periods...');
    let finalUpdatedBooking;
    try {
      finalUpdatedBooking = await prisma.booking.findUnique({
        where: { id: bookingId },
        select: {
          id: true,
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phoneNumber: true,
              companyName: true
            }
          },
          customerId: true,
          resources: {
            select: {
              id: true,
              name: true,
              type: true,
              basePrice: true,
              details: true,
              numberOfAttendees: true,
              _count: {
                select: {
                  bookings: true
                }
              }
            }
          },
          status: true,
          periods: {
            include: {
              parentPeriod: true,
              childPeriods: true
            },
            orderBy: { start: 'asc' }
          },
          invoice: {
            select: {
              id: true,
              status: true,
              total: true,
              paid: true
            }
          },
          caterings: {
            select: {
              id: true,
              quantity: true,
              catering: {
                select: {
                  id: true,
                  offerName: true,
                  pricePerPerson: true,
                  firstPartyShare: true,
                  vendorShare: true
                }
              }
            }
          },
          createdAt: true,
          updatedAt: true,
          createdById: true,
          updatedById: true
        }
      });

      if (!finalUpdatedBooking) {
        throw new Error('Failed to fetch final updated booking with periods');
      }
      console.log('Successfully fetched final updated booking');
    } catch (fetchError) {
      console.error('Error fetching final updated booking:', fetchError);
      throw fetchError;
    }

    // Add computed start/end times for backward compatibility
    console.log('Computing time range for booking...');
    let computedStart: Date | undefined;
    let computedEnd: Date | undefined;
    
    try {
      if (finalUpdatedBooking && (finalUpdatedBooking as any).periods && (finalUpdatedBooking as any).periods.length > 0) {
        // Create minimal period objects for time range calculation
        const periodObjects = (finalUpdatedBooking as any).periods.map((p: any) => ({
          id: p.id,
          bookingId: finalUpdatedBooking.id,
          start: p.start,
          end: p.end,
          isRecurring: p.isRecurring,
          recurrenceRule: p.recurrenceRule ? (() => {
            try {
              return JSON.parse(p.recurrenceRule as string);
            } catch (e) {
              console.warn('Failed to parse recurrence rule in time computation:', e);
              return null;
            }
          })() : null,
          parentPeriodId: p.parentPeriodId,
          booking: finalUpdatedBooking as any, // We don't need the full booking object for time calculation
          createdAt: p.createdAt,
          updatedAt: p.updatedAt,
          createdById: null,
          updatedById: null
        }));
        try {
          const timeRange = computeBookingTimeRange(periodObjects);
          computedStart = timeRange.start;
          computedEnd = timeRange.end;
          console.log('Successfully computed time range:', { computedStart, computedEnd });
        } catch (timeRangeError) {
          console.warn('Failed to compute time range:', timeRangeError);
          // Don't throw, just continue without computed times
        }
      }
    } catch (error) {
      console.warn(`Failed to compute time range for booking ${finalUpdatedBooking?.id}:`, error);
      // Don't throw here, just log and continue
    }

    console.log('Constructing final response...');
    let bookingWithComputedTimes;
    try {
      bookingWithComputedTimes = {
        ...finalUpdatedBooking,
        computedStart,
        computedEnd,
        periods: (finalUpdatedBooking as any)?.periods?.map((p: any) => ({
          ...p,
          recurrenceRule: p.recurrenceRule ? (() => {
            try {
              return JSON.parse(p.recurrenceRule as string);
            } catch (e) {
              console.warn('Failed to parse recurrence rule in response:', e);
              return null;
            }
          })() : null
        })) || []
      };
      console.log('Successfully constructed final response');
    } catch (responseError) {
      console.error('Error constructing final response:', responseError);
      throw responseError;
    }

    console.log('Returning successful response');
    try {
      return NextResponse.json({
        success: true,
        data: bookingWithComputedTimes
      });
    } catch (jsonError) {
      console.error('Error serializing response:', jsonError);
      // Return a basic success response if JSON serialization fails
      return NextResponse.json({
        success: true,
        data: {
          id: finalUpdatedBooking.id,
          message: 'Booking updated successfully'
        }
      });
    }
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error updating booking:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `PUT /api/bookings/${resolvedParams.id}`,
      bookingId: resolvedParams.id,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the booking information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'One or more selected resources, customer, or catering items do not exist. Please refresh and try again.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Booking not found',
              message: 'The booking you are trying to update no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the booking. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const bookingId = parseInt(id);
    
    if (isNaN(bookingId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid booking ID',
          message: 'Booking ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if booking exists and has an invoice
    const existingBooking = await prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        invoice: true,
        caterings: true,
        periods: true
      }
    });

    if (!existingBooking) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Booking not found',
          message: 'The requested booking does not exist'
        },
        { status: 404 }
      );
    }

    // Check if booking has an associated invoice (referential integrity)
    if (existingBooking.invoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Cannot delete booking: booking has an associated invoice',
          message: 'This booking cannot be deleted because it has an associated invoice. Please delete the invoice first.'
        },
        { status: 409 }
      );
    }

    // Delete the booking with transaction to handle related data including periods
    await prisma.$transaction(async (tx) => {
      // Delete periods first (cascade will handle child periods)
      await tx.period.deleteMany({
        where: { bookingId: bookingId }
      });

      // Delete catering associations
      await tx.cateringOnBooking.deleteMany({
        where: { bookingId: bookingId }
      });

      // Disconnect resources (many-to-many relationship)
      await tx.booking.update({
        where: { id: bookingId },
        data: {
          resources: {
            set: [] // Disconnect all resources
          }
        }
      });

      // Finally delete the booking
      await tx.booking.delete({
        where: { id: bookingId }
      });
    });

    return NextResponse.json({
      success: true,
      message: 'Booking deleted successfully'
    });
  } catch (error) {
    const resolvedParams = await params;
    console.error('Error deleting booking:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `DELETE /api/bookings/${resolvedParams.id}`,
      bookingId: resolvedParams.id
    });
    
    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Cannot delete booking: booking has an associated invoice',
              message: 'This booking cannot be deleted because it has an associated invoice. Please delete the invoice first.'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Booking not found',
              message: 'The booking you are trying to delete no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the booking. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}