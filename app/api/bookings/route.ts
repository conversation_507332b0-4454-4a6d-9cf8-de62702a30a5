import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { bookingSearchSchema, bookingCreateSchema, formatBookingForCalendar } from '@/lib/validations/booking';
import { periodService } from '@/lib/services/period-service';
import { conflictDetectionService } from '@/lib/services/conflict-detection';
import { recurrenceEngine } from '@/lib/services/recurrence-engine';
import { computeBookingTimeRange } from '@/lib/types';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  console.log('GET /api/bookings - Starting request');
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with defaults and validation
    const searchQuery = searchParams.get('search') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1') || 1);
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10') || 10));
    const customerId = searchParams.get('customerId') ? parseInt(searchParams.get('customerId')!) : undefined;
    const status = searchParams.get('status') || undefined;
    const resourceId = searchParams.get('resourceId') ? parseInt(searchParams.get('resourceId')!) : undefined;
    const invoiceStatus = searchParams.get('invoiceStatus') || undefined;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    const format = searchParams.get('format') || 'default'; // 'calendar' for calendar events

    // Validate parameters using Zod schema
    const validatedParams = bookingSearchSchema.parse({
      search: searchQuery,
      page,
      limit,
      customerId,
      status,
      resourceId,
      invoiceStatus,
      startDate,
      endDate
    });

    console.log('Validated params:', validatedParams);

    // Build where clause for search and filtering
    const where: Prisma.BookingWhereInput = {};
    
    if (validatedParams.search) {
      where.OR = [
        { customer: { name: { contains: validatedParams.search } } },
        { customer: { email: { contains: validatedParams.search } } },
        { customer: { companyName: { contains: validatedParams.search } } },
        { resources: { some: { name: { contains: validatedParams.search } } } }
      ];
    }

    if (validatedParams.customerId) {
      where.customerId = validatedParams.customerId;
    }

    if (validatedParams.status) {
      where.status = validatedParams.status as any;
    }

    if (validatedParams.resourceId) {
      where.resources = {
        some: {
          id: validatedParams.resourceId
        }
      };
    }

    // Invoice status filtering
    if (validatedParams.invoiceStatus) {
      if (validatedParams.invoiceStatus === 'NONE') {
        where.invoice = null;
      } else {
        where.invoice = {
          status: validatedParams.invoiceStatus
        };
      }
    }

    // Date range filtering - now works with periods
    if (validatedParams.startDate || validatedParams.endDate) {
      where.AND = [];
      
      if (validatedParams.startDate) {
        where.AND.push({
          periods: {
            some: {
              end: { gte: validatedParams.startDate }
            }
          }
        });
      }
      
      if (validatedParams.endDate) {
        where.AND.push({
          periods: {
            some: {
              start: { lte: validatedParams.endDate }
            }
          }
        });
      }
    }

    console.log('Where clause:', JSON.stringify(where, null, 2));

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;
    console.log('Pagination - skip:', skip, 'take:', validatedParams.limit);

    // Get total count for pagination metadata
    console.log('Getting total count...');
    const totalCount = await prisma.booking.count({ where });
    console.log('Total count:', totalCount);

    // Fetch bookings with comprehensive includes including periods
    console.log('Fetching bookings...');
    const bookings = await prisma.booking.findMany({
      where,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            companyName: true,
            _count: {
              select: {
                bookings: true
              }
            }
          }
        },
        resources: {
          select: {
            id: true,
            name: true,
            type: true,
            basePrice: true
          }
        },
        periods: {
          select: {
            id: true,
            start: true,
            end: true,
            isRecurring: true,
            recurrenceRule: true,
            parentPeriodId: true,
            createdAt: true,
            updatedAt: true
          },
          orderBy: {
            start: 'asc'
          }
        },
        caterings: {
          include: {
            catering: {
              select: {
                id: true,
                offerName: true,
                pricePerPerson: true
              }
            }
          }
        },
        invoice: {
          select: {
            id: true,
            status: true,
            total: true,
            paid: true,
            _count: {
              select: {
                payments: true
              }
            }
          }
        }
      },
      orderBy: [
        { id: 'desc' }
      ],
      skip,
      take: validatedParams.limit
    });
    console.log('Fetched bookings:', bookings.length);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNextPage = validatedParams.page < totalPages;
    const hasPreviousPage = validatedParams.page > 1;

    // Add computed start/end times for backward compatibility
    const bookingsWithComputedTimes = bookings.map(booking => {
      let computedStart: Date | undefined;
      let computedEnd: Date | undefined;
      
      if (booking.periods && booking.periods.length > 0) {
        try {
          // Create minimal period objects for time range calculation
          const periodObjects = booking.periods.map(p => ({
            id: p.id,
            bookingId: booking.id,
            start: p.start,
            end: p.end,
            isRecurring: p.isRecurring,
            recurrenceRule: p.recurrenceRule ? JSON.parse(p.recurrenceRule as string) : null,
            parentPeriodId: p.parentPeriodId,
            booking: booking as any, // We don't need the full booking object for time calculation
            createdAt: p.createdAt,
            updatedAt: p.updatedAt,
            createdById: null,
            updatedById: null
          }));
          const timeRange = computeBookingTimeRange(periodObjects);
          computedStart = timeRange.start;
          computedEnd = timeRange.end;
        } catch (error) {
          console.warn(`Failed to compute time range for booking ${booking.id}:`, error);
        }
      }
      
      return {
        ...booking,
        computedStart,
        computedEnd,
        periods: booking.periods.map(p => ({
          ...p,
          recurrenceRule: p.recurrenceRule ? JSON.parse(p.recurrenceRule as string) : null
        }))
      };
    });

    // Format response based on requested format
    if (format === 'calendar') {
      // Format bookings for calendar display - now handles multiple periods
      const calendarEvents = bookingsWithComputedTimes.flatMap(booking => 
        formatBookingForCalendar({
          ...booking,
          status: booking.status as "PENDING" | "CONFIRMED" | "CANCELLED"
        })
      );
      
      return NextResponse.json({
        success: true,
        data: calendarEvents
      });
    }

    // Default format with pagination
    return NextResponse.json({
      success: true,
      data: {
        data: bookingsWithComputedTimes,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: totalCount,
          totalPages,
          hasNext: hasNextPage,
          hasPrev: hasPreviousPage
        }
      }
    });
  } catch (error) {
    console.error('Error fetching bookings:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'GET /api/bookings'
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid query parameters',
          message: 'Please check your search parameters and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle database connection errors
    if (error instanceof Error && error.message.includes('database')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching bookings. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('POST /api/bookings - Starting booking creation');
  let body: any = null;
  
  try {
    body = await request.json();
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    // Convert date strings to Date objects in periods if needed
    if (body.periods && Array.isArray(body.periods)) {
      body.periods = body.periods.map((period: any) => ({
        ...period,
        start: typeof period.start === 'string' ? new Date(period.start) : period.start,
        end: typeof period.end === 'string' ? new Date(period.end) : period.end
      }));
    }
    
    // Validate input data
    console.log('Validating input data...');
    const validatedData = bookingCreateSchema.parse(body);
    console.log('Validation successful:', JSON.stringify(validatedData, null, 2));

    // Check if customer exists
    console.log('Checking if customer exists...');
    const customer = await prisma.customer.findUnique({
      where: { id: validatedData.customerId }
    });
    
    if (!customer) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Customer not found',
          message: 'The selected customer does not exist.'
        },
        { status: 404 }
      );
    }

    // Check if all resources exist
    console.log('Checking if resources exist...');
    const resources = await prisma.resource.findMany({
      where: { id: { in: validatedData.resourceIds } }
    });
    
    if (resources.length !== validatedData.resourceIds.length) {
      const foundIds = resources.map(r => r.id);
      const missingIds = validatedData.resourceIds.filter(id => !foundIds.includes(id));
      return NextResponse.json(
        { 
          success: false,
          error: 'Resources not found',
          message: `The following resource IDs do not exist: ${missingIds.join(', ')}`
        },
        { status: 404 }
      );
    }

    // Check if all catering items exist (if any)
    if (validatedData.caterings.length > 0) {
      console.log('Checking if catering items exist...');
      const cateringIds = validatedData.caterings.map(c => c.cateringId);
      const caterings = await prisma.catering.findMany({
        where: { id: { in: cateringIds } }
      });
      
      if (caterings.length !== cateringIds.length) {
        const foundIds = caterings.map(c => c.id);
        const missingIds = cateringIds.filter(id => !foundIds.includes(id));
        return NextResponse.json(
          { 
            success: false,
            error: 'Catering items not found',
            message: `The following catering IDs do not exist: ${missingIds.join(', ')}`
          },
          { status: 404 }
        );
      }
    }

    // Generate all periods (including recurring) for conflict checking
    console.log('Generating all periods for conflict checking...');
    const allPeriodsForConflictCheck = [];
    for (const periodData of validatedData.periods) {
      if (periodData.isRecurring && periodData.recurrenceRule) {
        // Generate all periods from recurrence rule
        const generatedPeriods = recurrenceEngine.generatePeriodsFromFormData(periodData);
        allPeriodsForConflictCheck.push(...generatedPeriods.map(p => ({
          start: p.start,
          end: p.end,
          isRecurring: false, // For conflict checking, treat as individual periods
          recurrenceRule: undefined
        })));
      } else {
        // Single period
        allPeriodsForConflictCheck.push({
          start: periodData.start,
          end: periodData.end,
          isRecurring: false,
          recurrenceRule: undefined
        });
      }
    }

    // Check for resource conflicts using all generated periods
    console.log('Checking for resource conflicts with', allPeriodsForConflictCheck.length, 'periods...');
    const conflictResult = await conflictDetectionService.detectConflicts(
      allPeriodsForConflictCheck,
      validatedData.resourceIds
      // Note: No excludeBookingId since this is a new booking
    );

    if (conflictResult.hasConflicts) {
      const conflictDetails = conflictResult.conflicts.map(conflict => ({
        id: conflict.conflictingBooking.id,
        customer: conflict.conflictingBooking.customerName,
        resources: conflict.conflictingResources.map(r => r.name),
        overlapStart: conflict.overlapStart,
        overlapEnd: conflict.overlapEnd,
        status: conflict.conflictingBooking.status
      }));

      return NextResponse.json(
        { 
          success: false,
          error: 'Resource conflict',
          message: conflictResult.message,
          conflicts: conflictDetails,
          affectedResources: conflictResult.affectedResources
        },
        { status: 409 }
      );
    }

    // Create the booking with periods using transaction to ensure data consistency
    console.log('Creating booking in database...');
    const booking = await prisma.$transaction(async (tx) => {
      // Create the booking (without start/end fields)
      const newBooking = await tx.booking.create({
        data: {
          customerId: validatedData.customerId,
          status: validatedData.status,
          resources: {
            connect: validatedData.resourceIds.map(id => ({ id }))
          }
          // TODO: Add createdById from authenticated user session
        }
      });

      // Note: We'll create periods after the transaction completes to use the period service
      // which properly handles recurring periods

      // Create catering relationships if any
      if (validatedData.caterings.length > 0) {
        await tx.cateringOnBooking.createMany({
          data: validatedData.caterings.map(catering => ({
            bookingId: newBooking.id,
            cateringId: catering.cateringId,
            quantity: catering.quantity
            // TODO: Add createdById from authenticated user session
          }))
        });
      }

      // Fetch the complete booking with all relationships including periods
      return await tx.booking.findUnique({
        where: { id: newBooking.id },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              companyName: true,
              _count: {
                select: {
                  bookings: true
                }
              }
            }
          },
          resources: {
            select: {
              id: true,
              name: true,
              type: true,
              basePrice: true
            }
          },
          periods: {
            select: {
              id: true,
              start: true,
              end: true,
              isRecurring: true,
              recurrenceRule: true,
              parentPeriodId: true,
              createdAt: true,
              updatedAt: true
            },
            orderBy: {
              start: 'asc'
            }
          },
          caterings: {
            include: {
              catering: {
                select: {
                  id: true,
                  offerName: true,
                  pricePerPerson: true
                }
              }
            }
          },
          invoice: {
            select: {
              id: true,
              status: true,
              total: true,
              paid: true,
              _count: {
                select: {
                  payments: true
                }
              }
            }
          }
        }
      });
    });

    if (!booking) {
      throw new Error('Failed to create booking');
    }

    console.log('Booking created successfully:', JSON.stringify(booking, null, 2));

    // Create periods using the period service which handles recurrence properly
    console.log('Creating periods for booking:', booking.id);
    const allCreatedPeriods = [];
    for (const periodData of validatedData.periods) {
      const periodsForThisPeriod = await periodService.createPeriodsFromFormData(
        booking.id,
        periodData
      );
      allCreatedPeriods.push(...periodsForThisPeriod);
    }
    console.log('Total periods created:', allCreatedPeriods.length);

    // Fetch the updated booking with all periods
    const updatedBooking = await prisma.booking.findUnique({
      where: { id: booking.id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            companyName: true,
            _count: {
              select: {
                bookings: true
              }
            }
          }
        },
        resources: {
          select: {
            id: true,
            name: true,
            type: true,
            basePrice: true,
            details: true,
            numberOfAttendees: true,
            _count: {
              select: {
                bookings: true
              }
            }
          }
        },
        caterings: {
          include: {
            catering: {
              select: {
                id: true,
                offerName: true,
                pricePerPerson: true,
                firstPartyShare: true,
                vendorShare: true
              }
            }
          }
        },
        periods: {
          include: {
            parentPeriod: true,
            childPeriods: true
          },
          orderBy: { start: 'asc' }
        },
        invoice: {
          select: {
            id: true,
            status: true,
            total: true,
            paid: true
          }
        }
      }
    });

    if (!updatedBooking) {
      throw new Error('Failed to fetch updated booking with periods');
    }

    // Add computed start/end times for backward compatibility
    let computedStart: Date | undefined;
    let computedEnd: Date | undefined;
    
    if (updatedBooking && (updatedBooking as any).periods && (updatedBooking as any).periods.length > 0) {
      try {
        // Create minimal period objects for time range calculation
        const periodObjects = (updatedBooking as any).periods.map((p: any) => ({
          id: p.id,
          bookingId: updatedBooking.id,
          start: p.start,
          end: p.end,
          isRecurring: p.isRecurring,
          recurrenceRule: p.recurrenceRule ? JSON.parse(p.recurrenceRule as string) : null,
          parentPeriodId: p.parentPeriodId,
          booking: updatedBooking as any, // We don't need the full booking object for time calculation
          createdAt: p.createdAt,
          updatedAt: p.updatedAt,
          createdById: null,
          updatedById: null
        }));
        const timeRange = computeBookingTimeRange(periodObjects);
        computedStart = timeRange.start;
        computedEnd = timeRange.end;
      } catch (error) {
        console.warn(`Failed to compute time range for booking ${updatedBooking?.id}:`, error);
      }
    }

    const bookingWithComputedTimes = {
      ...updatedBooking,
      computedStart,
      computedEnd,
      periods: (updatedBooking as any)?.periods?.map((p: any) => ({
        ...p,
        recurrenceRule: p.recurrenceRule ? JSON.parse(p.recurrenceRule as string) : null
      })) || []
    };

    return NextResponse.json({
      success: true,
      data: bookingWithComputedTimes
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating booking:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'POST /api/bookings',
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the booking information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Duplicate booking',
              message: 'A booking with similar details already exists.'
            },
            { status: 409 }
          );
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'Invalid customer, resource, or catering reference. Please check your input.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Record not found',
              message: 'Required record not found in database.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while creating the booking. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}