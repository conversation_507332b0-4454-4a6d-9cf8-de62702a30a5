import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { NextRequest } from 'next/server'
import { POST } from '../route'
import { prisma } from '@/lib/db'

// Mock data for testing
const mockCustomerData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '+964-************'
}

const mockCustomer2Data = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '+964-************'
}

const mockResourceData = {
  name: 'Conference Room A',
  type: 'MEETING_ROOM',
  basePrice: 50000,
  numberOfAttendees: 10,
  details: 'Large conference room with projector'
}

const mockResource2Data = {
  name: 'Conference Room B',
  type: 'MEETING_ROOM',
  basePrice: 60000,
  numberOfAttendees: 15,
  details: 'Extra large conference room'
}

const mockResource3Data = {
  name: 'Training Room',
  type: 'TRAINING_ROOM',
  basePrice: 40000,
  numberOfAttendees: 20,
  details: 'Training room with whiteboard'
}

describe('Booking Overlap API Integration Tests', () => {
  let testCustomer: any
  let testCustomer2: any
  let testResource: any
  let testResource2: any
  let testResource3: any
  let testBooking: any

  // Set up test data before each test
  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.period.deleteMany({})
    await prisma.booking.deleteMany({})
    await prisma.customer.deleteMany({
      where: { 
        OR: [
          { email: mockCustomerData.email },
          { email: mockCustomer2Data.email }
        ]
      }
    })
    await prisma.resource.deleteMany({
      where: { 
        OR: [
          { name: mockResourceData.name },
          { name: mockResource2Data.name },
          { name: mockResource3Data.name }
        ]
      }
    })

    // Create test customers
    testCustomer = await prisma.customer.create({
      data: mockCustomerData
    })

    testCustomer2 = await prisma.customer.create({
      data: mockCustomer2Data
    })

    // Create test resources
    testResource = await prisma.resource.create({
      data: mockResourceData
    })

    testResource2 = await prisma.resource.create({
      data: mockResource2Data
    })

    testResource3 = await prisma.resource.create({
      data: mockResource3Data
    })

    // Create a test booking with periods
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 7) // 7 days from now
    futureDate.setHours(10, 0, 0, 0) // 10:00 AM

    testBooking = await prisma.booking.create({
      data: {
        customerId: testCustomer.id,
        status: 'CONFIRMED',
        resources: {
          connect: [{ id: testResource.id }, { id: testResource2.id }]
        }
      }
    })

    // Create periods for the test booking
    await prisma.period.createMany({
      data: [
        {
          bookingId: testBooking.id,
          start: new Date(futureDate.getTime()), // 10:00 AM
          end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000), // 12:00 PM
          isRecurring: false
        },
        {
          bookingId: testBooking.id,
          start: new Date(futureDate.getTime() + 4 * 60 * 60 * 1000), // 2:00 PM
          end: new Date(futureDate.getTime() + 6 * 60 * 60 * 1000), // 4:00 PM
          isRecurring: false
        }
      ]
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.period.deleteMany({})
    await prisma.booking.deleteMany({})
    await prisma.customer.deleteMany({
      where: { 
        OR: [
          { email: mockCustomerData.email },
          { email: mockCustomer2Data.email }
        ]
      }
    })
    await prisma.resource.deleteMany({
      where: { 
        OR: [
          { name: mockResourceData.name },
          { name: mockResource2Data.name },
          { name: mockResource3Data.name }
        ]
      }
    })
  })

  describe('POST /api/bookings/check-overlap - New Period Format', () => {
    it('should detect conflicts when periods overlap with shared resources', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0) // 11:00 AM - overlaps with existing 10:00-12:00

      const requestData = {
        resourceIds: [testResource.id], // Shared resource
        periods: [
          {
            start: new Date(futureDate.getTime()).toISOString(),
            end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString() // 11:00-1:00 PM
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(true)
      expect(data.conflicts).toHaveLength(1)
      expect(data.conflicts[0]).toHaveProperty('conflictingBooking')
      expect(data.conflicts[0]).toHaveProperty('conflictingResources')
      expect(data.conflicts[0]).toHaveProperty('overlapStart')
      expect(data.conflicts[0]).toHaveProperty('overlapEnd')
      expect(data.conflicts[0].conflictingBooking.id).toBe(testBooking.id)
      expect(data.conflicts[0].conflictingResources).toHaveLength(1)
      expect(data.conflicts[0].conflictingResources[0].id).toBe(testResource.id)
      expect(data.affectedResources).toHaveLength(1)
      expect(data.totalConflicts).toBe(1)
    })

    it('should not detect conflicts when periods overlap but no shared resources', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0) // 11:00 AM - overlaps with existing 10:00-12:00

      const requestData = {
        resourceIds: [testResource3.id], // Different resource, no conflict
        periods: [
          {
            start: new Date(futureDate.getTime()).toISOString(),
            end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString()
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(false)
      expect(data.conflicts).toHaveLength(0)
      expect(data.affectedResources).toHaveLength(0)
      expect(data.message).toBe('No conflicts detected')
    })

    it('should detect conflicts across multiple periods', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(10, 0, 0, 0) // Ensure consistent time

      const requestData = {
        resourceIds: [testResource.id, testResource2.id],
        periods: [
          {
            // First period: 10:30 AM - 11:30 AM (overlaps with existing 10:00 AM - 12:00 PM)
            start: new Date(futureDate.getTime() + 30 * 60 * 1000).toISOString(),
            end: new Date(futureDate.getTime() + 90 * 60 * 1000).toISOString()
          },
          {
            // Second period: 2:30 PM - 3:30 PM (overlaps with existing 2:00 PM - 4:00 PM)
            start: new Date(futureDate.getTime() + 4.5 * 60 * 60 * 1000).toISOString(),
            end: new Date(futureDate.getTime() + 5.5 * 60 * 60 * 1000).toISOString()
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(true)
      // Each test period should conflict with one existing period, so we expect 2 conflicts
      expect(data.conflicts).toHaveLength(2) // Both periods conflict
      expect(data.totalConflicts).toBe(2)
    })

    it('should exclude specified booking from conflict check', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0)

      const requestData = {
        resourceIds: [testResource.id],
        periods: [
          {
            start: new Date(futureDate.getTime()).toISOString(),
            end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString()
          }
        ],
        excludeBookingId: testBooking.id // Exclude the conflicting booking
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(false)
      expect(data.conflicts).toHaveLength(0)
    })

    it('should handle touching periods (no overlap)', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(12, 0, 0, 0) // Starts exactly when existing period ends

      const requestData = {
        resourceIds: [testResource.id],
        periods: [
          {
            start: new Date(futureDate.getTime()).toISOString(), // 12:00 PM
            end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString() // 2:00 PM
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(false)
      expect(data.conflicts).toHaveLength(0)
    })

    it('should only consider active bookings (PENDING, CONFIRMED)', async () => {
      // Create a cancelled booking with overlapping period
      const cancelledBooking = await prisma.booking.create({
        data: {
          customerId: testCustomer2.id,
          status: 'CANCELLED',
          resources: {
            connect: [{ id: testResource.id }]
          }
        }
      })

      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0)

      await prisma.period.create({
        data: {
          bookingId: cancelledBooking.id,
          start: new Date(futureDate.getTime()),
          end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000),
          isRecurring: false
        }
      })

      const requestData = {
        resourceIds: [testResource.id],
        periods: [
          {
            start: new Date(futureDate.getTime()).toISOString(),
            end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString()
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(true) // Should still conflict with the CONFIRMED booking
      expect(data.conflicts).toHaveLength(1)
      expect(data.conflicts[0].conflictingBooking.id).toBe(testBooking.id) // Not the cancelled one
    })

    it('should provide detailed conflict information', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0)

      const requestData = {
        resourceIds: [testResource.id, testResource2.id],
        periods: [
          {
            start: new Date(futureDate.getTime()).toISOString(),
            end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString()
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(true)
      expect(data.conflicts[0]).toHaveProperty('conflictingBooking')
      expect(data.conflicts[0].conflictingBooking).toHaveProperty('id')
      expect(data.conflicts[0].conflictingBooking).toHaveProperty('customerName')
      expect(data.conflicts[0].conflictingBooking).toHaveProperty('status')
      expect(data.conflicts[0].conflictingBooking.customerName).toBe(mockCustomerData.name)
      expect(data.conflicts[0]).toHaveProperty('conflictingResources')
      expect(data.conflicts[0].conflictingResources).toHaveLength(2) // Both resources conflict
      expect(data.conflicts[0]).toHaveProperty('overlapStart')
      expect(data.conflicts[0]).toHaveProperty('overlapEnd')
      expect(data.affectedResources).toHaveLength(2)
      expect(data.message).toContain('Found 1 conflict(s) across 2 resource(s)')
    })
  })

  describe('POST /api/bookings/check-overlap - Legacy Format (Backward Compatibility)', () => {
    it('should handle legacy format with start/end fields', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0)

      const legacyRequestData = {
        resourceIds: [testResource.id],
        start: new Date(futureDate.getTime()).toISOString(),
        end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString()
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(legacyRequestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(true)
      expect(data.conflicts).toBeDefined()
      expect(Array.isArray(data.conflicts)).toBe(true)
      expect(data.conflicts).toHaveLength(1)
      
      // Legacy format should group conflicts by resource
      const conflict = data.conflicts[0]
      expect(conflict).toHaveProperty('resourceId')
      expect(conflict).toHaveProperty('resourceName')
      expect(conflict).toHaveProperty('conflicts')
      expect(conflict.resourceId).toBe(testResource.id)
      expect(conflict.resourceName).toBe(mockResourceData.name)
      expect(Array.isArray(conflict.conflicts)).toBe(true)
      expect(conflict.conflicts).toHaveLength(1)
      
      const resourceConflict = conflict.conflicts[0]
      expect(resourceConflict).toHaveProperty('bookingId')
      expect(resourceConflict).toHaveProperty('customerName')
      expect(resourceConflict).toHaveProperty('start')
      expect(resourceConflict).toHaveProperty('end')
      expect(resourceConflict).toHaveProperty('status')
    })

    it('should handle legacy format with excludeBookingId', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0)

      const legacyRequestData = {
        resourceIds: [testResource.id],
        start: new Date(futureDate.getTime()).toISOString(),
        end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString(),
        excludeBookingId: testBooking.id
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(legacyRequestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(false)
      expect(data.conflicts).toHaveLength(0)
    })
  })

  describe('Validation and Error Handling', () => {
    it('should validate required fields for new format', async () => {
      const invalidData = {
        resourceIds: [testResource.id]
        // Missing periods
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Validation failed')
      expect(data.details).toBeDefined()
      expect(Array.isArray(data.details)).toBe(true)
    })

    it('should validate period time ranges', async () => {
      const invalidData = {
        resourceIds: [testResource.id],
        periods: [
          {
            start: '2024-12-01T12:00:00Z',
            end: '2024-12-01T10:00:00Z' // End before start
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Validation failed')
    })

    it('should validate that periods do not overlap with each other', async () => {
      const invalidData = {
        resourceIds: [testResource.id],
        periods: [
          {
            start: '2024-12-01T10:00:00Z',
            end: '2024-12-01T12:00:00Z'
          },
          {
            start: '2024-12-01T11:00:00Z', // Overlaps with first period
            end: '2024-12-01T13:00:00Z'
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Validation failed')
    })

    it('should validate resource IDs array', async () => {
      const invalidData = {
        resourceIds: [], // Empty array
        periods: [
          {
            start: '2024-12-01T10:00:00Z',
            end: '2024-12-01T12:00:00Z'
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Validation failed')
    })

    it('should validate date format', async () => {
      const invalidData = {
        resourceIds: [testResource.id],
        periods: [
          {
            start: 'invalid-date',
            end: '2024-12-01T12:00:00Z'
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Validation failed')
    })

    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: 'invalid json'
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal server error')
    })

    it('should handle empty request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: ''
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Internal server error')
    })
  })

  describe('Edge Cases and Complex Scenarios', () => {
    it('should handle multiple bookings with overlapping periods', async () => {
      // Create another booking with overlapping periods
      const anotherBooking = await prisma.booking.create({
        data: {
          customerId: testCustomer2.id,
          status: 'PENDING',
          resources: {
            connect: [{ id: testResource.id }]
          }
        }
      })

      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 30, 0, 0) // 11:30 AM

      await prisma.period.create({
        data: {
          bookingId: anotherBooking.id,
          start: new Date(futureDate.getTime()),
          end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000), // 1:30 PM
          isRecurring: false
        }
      })

      const requestData = {
        resourceIds: [testResource.id],
        periods: [
          {
            start: new Date(futureDate.getTime() - 30 * 60 * 1000).toISOString(), // 11:00 AM
            end: new Date(futureDate.getTime() + 30 * 60 * 1000).toISOString() // 12:00 PM
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(true)
      expect(data.conflicts).toHaveLength(2) // Conflicts with both bookings
    })

    it('should handle partial resource overlap', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0)

      const requestData = {
        resourceIds: [testResource.id, testResource3.id], // One shared, one not
        periods: [
          {
            start: new Date(futureDate.getTime()).toISOString(),
            end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString()
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(true)
      expect(data.conflicts).toHaveLength(1)
      expect(data.conflicts[0].conflictingResources).toHaveLength(1)
      expect(data.conflicts[0].conflictingResources[0].id).toBe(testResource.id)
      expect(data.affectedResources).toHaveLength(1)
    })

    it('should calculate correct overlap times', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0) // 11:00 AM

      const requestData = {
        resourceIds: [testResource.id],
        periods: [
          {
            start: new Date(futureDate.getTime()).toISOString(), // 11:00 AM
            end: new Date(futureDate.getTime() + 3 * 60 * 60 * 1000).toISOString() // 2:00 PM
          }
        ]
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(true)
      expect(data.conflicts).toHaveLength(1)
      
      const conflict = data.conflicts[0]
      const overlapStart = new Date(conflict.overlapStart)
      const overlapEnd = new Date(conflict.overlapEnd)
      
      // Should overlap from 11:00 AM to 12:00 PM (1 hour)
      expect(overlapStart.getHours()).toBe(11)
      expect(overlapEnd.getHours()).toBe(12)
    })

    it('should handle very large number of periods', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      
      // Create 50 non-overlapping periods
      const periods = []
      for (let i = 0; i < 50; i++) {
        const startTime = new Date(futureDate.getTime() + i * 2 * 60 * 60 * 1000) // Every 2 hours
        const endTime = new Date(startTime.getTime() + 30 * 60 * 1000) // 30 minutes each
        periods.push({
          start: startTime.toISOString(),
          end: endTime.toISOString()
        })
      }

      const requestData = {
        resourceIds: [testResource3.id], // Different resource, no conflicts
        periods
      }

      const request = new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
        method: 'POST',
        body: JSON.stringify(requestData)
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.hasConflicts).toBe(false)
      expect(data.conflicts).toHaveLength(0)
    })
  })

  describe('Performance and Optimization', () => {
    it('should handle concurrent requests efficiently', async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 7)
      futureDate.setHours(11, 0, 0, 0)

      const requestData = {
        resourceIds: [testResource.id],
        periods: [
          {
            start: new Date(futureDate.getTime()).toISOString(),
            end: new Date(futureDate.getTime() + 2 * 60 * 60 * 1000).toISOString()
          }
        ]
      }

      // Make multiple concurrent requests
      const requests = Array(5).fill(null).map(() => 
        POST(new NextRequest('http://localhost:3000/api/bookings/check-overlap', {
          method: 'POST',
          body: JSON.stringify(requestData)
        }))
      )

      const responses = await Promise.all(requests)
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })

      const results = await Promise.all(responses.map(r => r.json()))
      
      // All should return the same result
      results.forEach(data => {
        expect(data.hasConflicts).toBe(true)
        expect(data.conflicts).toHaveLength(1)
      })
    })
  })
})