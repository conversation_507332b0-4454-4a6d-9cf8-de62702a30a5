import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { conflictDetectionService } from '@/lib/services/conflict-detection';
import { z } from 'zod';

// Schema for overlap check request - updated to support periods
const overlapCheckSchema = z.object({
  resourceIds: z.array(z.number().int().positive()).min(1, "At least one resource must be selected"),
  periods: z.array(z.object({
    start: z.string().datetime("Invalid start date format"),
    end: z.string().datetime("Invalid end date format")
  })).min(1, "At least one period must be provided"),
  excludeBookingId: z.number().int().positive().optional()
}).refine(data => {
  // Validate that all periods have valid time ranges
  return data.periods.every(period => {
    const startDate = new Date(period.start);
    const endDate = new Date(period.end);
    return endDate > startDate;
  });
}, {
  message: "All periods must have end date after start date"
}).refine(data => {
  // Validate that periods don't overlap with each other
  for (let i = 0; i < data.periods.length; i++) {
    for (let j = i + 1; j < data.periods.length; j++) {
      const period1Start = new Date(data.periods[i].start);
      const period1End = new Date(data.periods[i].end);
      const period2Start = new Date(data.periods[j].start);
      const period2End = new Date(data.periods[j].end);
      
      // Check for overlap: period1.start < period2.end && period2.start < period1.end
      if (period1Start < period2End && period2Start < period1End) {
        return false;
      }
    }
  }
  return true;
}, {
  message: "Periods cannot overlap with each other"
});

// Legacy schema for backward compatibility
const legacyOverlapCheckSchema = z.object({
  resourceIds: z.array(z.number().int().positive()).min(1, "At least one resource must be selected"),
  start: z.string().datetime("Invalid start date format"),
  end: z.string().datetime("Invalid end date format"),
  excludeBookingId: z.number().int().positive().optional()
}).refine(data => {
  const startDate = new Date(data.start);
  const endDate = new Date(data.end);
  return endDate > startDate;
}, {
  message: "End date must be after start date"
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Try to parse as new periods-based format first
    let validatedData: any;
    let isLegacyFormat = false;
    
    try {
      validatedData = overlapCheckSchema.parse(body);
    } catch (error) {
      // If that fails, try legacy format for backward compatibility
      try {
        const legacyData = legacyOverlapCheckSchema.parse(body);
        validatedData = {
          resourceIds: legacyData.resourceIds,
          periods: [{
            start: legacyData.start,
            end: legacyData.end
          }],
          excludeBookingId: legacyData.excludeBookingId
        };
        isLegacyFormat = true;
      } catch (legacyError) {
        throw error; // Throw the original error
      }
    }
    
    const { resourceIds, periods, excludeBookingId } = validatedData;
    
    // Convert periods to PeriodFormData format
    const periodFormData = periods.map((period: any) => ({
      start: new Date(period.start),
      end: new Date(period.end),
      isRecurring: false
    }));

    // Use the conflict detection service
    const conflictResult = await conflictDetectionService.detectConflicts(
      periodFormData,
      resourceIds,
      excludeBookingId
    );

    // Format response for backward compatibility
    if (isLegacyFormat) {
      // Legacy format response
      const conflictsByResource: Record<number, any> = {};
      
      conflictResult.conflicts.forEach(conflict => {
        conflict.conflictingResources.forEach(resource => {
          if (!conflictsByResource[resource.id]) {
            conflictsByResource[resource.id] = {
              resourceId: resource.id,
              resourceName: resource.name,
              conflicts: []
            };
          }
          conflictsByResource[resource.id].conflicts.push({
            bookingId: conflict.conflictingBooking.id,
            customerName: conflict.conflictingBooking.customerName,
            start: conflict.overlapStart,
            end: conflict.overlapEnd,
            status: conflict.conflictingBooking.status
          });
        });
      });

      return NextResponse.json({
        hasConflicts: conflictResult.hasConflicts,
        conflicts: Object.values(conflictsByResource),
        message: conflictResult.message
      });
    }

    // New format response with detailed period conflict information
    return NextResponse.json({
      hasConflicts: conflictResult.hasConflicts,
      conflicts: conflictResult.conflicts.map(conflict => ({
        periodIndex: 0, // Since we don't track which period in the request
        conflictingPeriodId: conflict.conflictingPeriodId,
        conflictingBooking: conflict.conflictingBooking,
        conflictingResources: conflict.conflictingResources,
        overlapStart: conflict.overlapStart,
        overlapEnd: conflict.overlapEnd
      })),
      affectedResources: conflictResult.affectedResources,
      message: conflictResult.message,
      totalConflicts: conflictResult.conflicts.length
    });

  } catch (error) {
    console.error('Error checking booking overlap:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}