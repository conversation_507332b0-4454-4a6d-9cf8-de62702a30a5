import { NextRequest, NextResponse } from 'next/server';
import { conflictDetectionService } from '@/lib/services/conflict-detection';
import { 
  periodConflictCheckSchema,
  type PeriodConflictCheckInput 
} from '@/lib/validations/period';
import { Prisma } from '@prisma/client';

export async function POST(request: NextRequest) {
  let body: any = null;
  
  try {
    body = await request.json();
    
    // Convert date strings to Date objects if needed
    if (body.periods && Array.isArray(body.periods)) {
      body.periods = body.periods.map((period: any) => ({
        ...period,
        start: typeof period.start === 'string' ? new Date(period.start) : period.start,
        end: typeof period.end === 'string' ? new Date(period.end) : period.end
      }));
    }
    
    // Validate input data
    const validatedData: PeriodConflictCheckInput = periodConflictCheckSchema.parse(body);

    // Convert the simplified period format to PeriodFormData format
    const periodFormData = validatedData.periods.map(period => ({
      start: period.start,
      end: period.end,
      isRecurring: false, // Conflict checking doesn't need recurrence info
      recurrenceRule: undefined
    }));

    // Check for conflicts
    const conflictResult = await conflictDetectionService.detectConflicts(
      periodFormData,
      validatedData.resourceIds,
      validatedData.excludeBookingId
    );

    // Get additional resource utilization data if conflicts exist
    let utilizationData = null;
    if (conflictResult.hasConflicts && validatedData.periods.length > 0) {
      // Calculate date range from all periods
      const allDates = validatedData.periods.flatMap(p => [p.start, p.end]);
      const startDate = new Date(Math.min(...allDates.map(d => d.getTime())));
      const endDate = new Date(Math.max(...allDates.map(d => d.getTime())));
      
      utilizationData = await conflictDetectionService.getResourceUtilization(
        validatedData.resourceIds,
        startDate,
        endDate
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        hasConflicts: conflictResult.hasConflicts,
        conflicts: conflictResult.conflicts.map(conflict => ({
          conflictingPeriodId: conflict.conflictingPeriodId,
          conflictingBooking: {
            id: conflict.conflictingBooking.id,
            customerName: conflict.conflictingBooking.customerName,
            status: conflict.conflictingBooking.status
          },
          conflictingResources: conflict.conflictingResources,
          overlap: {
            start: conflict.overlapStart,
            end: conflict.overlapEnd,
            duration: conflict.overlapEnd.getTime() - conflict.overlapStart.getTime()
          }
        })),
        affectedResources: conflictResult.affectedResources,
        summary: {
          totalConflicts: conflictResult.conflicts.length,
          affectedResourceCount: conflictResult.affectedResources.length,
          message: conflictResult.message
        },
        utilization: utilizationData
      }
    });
  } catch (error) {
    console.error('Error checking period conflicts:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'POST /api/periods/check-conflicts',
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the conflict check parameters and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle service-level errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Resource not found',
            message: 'One or more specified resources do not exist.'
          },
          { status: 404 }
        );
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while checking conflicts. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

// GET endpoint for checking conflicts with query parameters (alternative interface)
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    
    // Parse query parameters
    const resourceIdsParam = url.searchParams.get('resourceIds');
    const startParam = url.searchParams.get('start');
    const endParam = url.searchParams.get('end');
    const excludeBookingIdParam = url.searchParams.get('excludeBookingId');
    
    if (!resourceIdsParam || !startParam || !endParam) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required parameters',
          message: 'resourceIds, start, and end parameters are required'
        },
        { status: 400 }
      );
    }

    // Parse resource IDs
    const resourceIds = resourceIdsParam.split(',').map(id => {
      const num = parseInt(id.trim());
      if (isNaN(num)) {
        throw new Error(`Invalid resource ID: ${id}`);
      }
      return num;
    });

    // Parse dates
    const start = new Date(startParam);
    const end = new Date(endParam);
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid date format',
          message: 'Start and end dates must be valid ISO date strings'
        },
        { status: 400 }
      );
    }

    if (end <= start) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid date range',
          message: 'End date must be after start date'
        },
        { status: 400 }
      );
    }

    // Parse optional exclude booking ID
    const excludeBookingId = excludeBookingIdParam ? parseInt(excludeBookingIdParam) : undefined;
    if (excludeBookingIdParam && isNaN(excludeBookingId!)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid exclude booking ID',
          message: 'Exclude booking ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Create period data for conflict checking
    const periodFormData = [{
      start,
      end,
      isRecurring: false,
      recurrenceRule: undefined
    }];

    // Check for conflicts
    const conflictResult = await conflictDetectionService.detectConflicts(
      periodFormData,
      resourceIds,
      excludeBookingId
    );

    // Get resource utilization data
    const utilizationData = await conflictDetectionService.getResourceUtilization(
      resourceIds,
      start,
      end
    );

    return NextResponse.json({
      success: true,
      data: {
        hasConflicts: conflictResult.hasConflicts,
        conflicts: conflictResult.conflicts.map(conflict => ({
          conflictingPeriodId: conflict.conflictingPeriodId,
          conflictingBooking: {
            id: conflict.conflictingBooking.id,
            customerName: conflict.conflictingBooking.customerName,
            status: conflict.conflictingBooking.status
          },
          conflictingResources: conflict.conflictingResources,
          overlap: {
            start: conflict.overlapStart,
            end: conflict.overlapEnd,
            duration: conflict.overlapEnd.getTime() - conflict.overlapStart.getTime()
          }
        })),
        affectedResources: conflictResult.affectedResources,
        summary: {
          totalConflicts: conflictResult.conflicts.length,
          affectedResourceCount: conflictResult.affectedResources.length,
          message: conflictResult.message
        },
        utilization: utilizationData,
        query: {
          resourceIds,
          start,
          end,
          excludeBookingId
        }
      }
    });
  } catch (error) {
    console.error('Error checking period conflicts (GET):', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'GET /api/periods/check-conflicts',
      url: request.url
    });
    
    // Handle parameter parsing errors
    if (error instanceof Error && error.message.includes('Invalid resource ID')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid resource ID',
          message: error.message
        },
        { status: 400 }
      );
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while checking conflicts. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}