import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { customerUpdateSchema } from '@/lib/validations/customer';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const customerId = parseInt((await params).id);
    
    if (isNaN(customerId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid customer ID',
          message: 'Customer ID must be a valid number'
        },
        { status: 400 }
      );
    }

    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      select: {
        id: true,
        name: true,
        email: true,
        phoneNumber: true,
        companyName: true,
        specialization: true,
        industry: true,
        website: true,
        linkedIn: true,
        socialMedia: true,
        notes: true,
        bookings: {
          select: {
            id: true,
            status: true,
            periods: {
              select: {
                id: true,
                start: true,
                end: true
              },
              orderBy: {
                start: 'asc'
              }
            },
            resources: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    if (!customer) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Customer not found',
          message: 'The requested customer does not exist'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: customer
    });
  } catch (error) {
    console.error('Error fetching customer:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/customers/${(await params).id}`,
      customerId: (await params).id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching the customer. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}export
 async function PUT(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const customerId = parseInt((await params).id);
    
    if (isNaN(customerId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid customer ID',
          message: 'Customer ID must be a valid number'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Validate input data
    const validatedData = customerUpdateSchema.parse(body);

    // Check if customer exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { id: customerId }
    });

    if (!existingCustomer) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Customer not found',
          message: 'The requested customer does not exist'
        },
        { status: 404 }
      );
    }

    // Update the customer
    const updatedCustomer = await prisma.customer.update({
      where: { id: customerId },
      data: {
        name: validatedData.name,
        email: validatedData.email,
        phoneNumber: validatedData.phoneNumber,
        companyName: validatedData.companyName,
        specialization: validatedData.specialization,
        industry: validatedData.industry,
        website: validatedData.website,
        linkedIn: validatedData.linkedIn,
        socialMedia: validatedData.socialMedia,
        notes: validatedData.notes,
        updatedAt: new Date(),
        // TODO: Add updatedById from authenticated user session
      },
      select: {
        id: true,
        name: true,
        email: true,
        phoneNumber: true,
        companyName: true,
        specialization: true,
        industry: true,
        website: true,
        linkedIn: true,
        socialMedia: true,
        notes: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedCustomer
    });
  } catch (error) {
    console.error('Error updating customer:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `PUT /api/customers/${(await params).id}`,
      customerId: (await params).id,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the customer information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Email already exists',
              message: 'This email address is already registered. Please use a different email.',
              field: 'email'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Customer not found',
              message: 'The customer you are trying to update no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while updating the customer. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const customerId = parseInt((await params).id);
    
    if (isNaN(customerId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid customer ID',
          message: 'Customer ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if customer exists and has bookings
    const existingCustomer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        bookings: true
      }
    });

    if (!existingCustomer) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Customer not found',
          message: 'The requested customer does not exist'
        },
        { status: 404 }
      );
    }

    // Check if customer has existing bookings (referential integrity)
    if (existingCustomer.bookings && existingCustomer.bookings.length > 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Cannot delete customer: customer has existing bookings',
          message: 'This customer cannot be deleted because they have existing bookings. Please cancel or complete these bookings first.'
        },
        { status: 409 }
      );
    }

    // Delete the customer
    await prisma.customer.delete({
      where: { id: customerId }
    });

    return NextResponse.json({
      success: true,
      message: 'Customer deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting customer:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `DELETE /api/customers/${(await params).id}`,
      customerId: (await params).id
    });
    
    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Cannot delete customer: customer has existing bookings',
              message: 'This customer cannot be deleted because they have existing bookings. Please cancel or complete these bookings first.'
            },
            { status: 409 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Customer not found',
              message: 'The customer you are trying to delete no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while deleting the customer. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}