import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const customerId = parseInt((await params).id);
    
    if (isNaN(customerId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid customer ID',
          message: 'Customer ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if customer exists
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      select: { id: true }
    });

    if (!customer) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Customer not found',
          message: 'The requested customer does not exist'
        },
        { status: 404 }
      );
    }

    // Fetch invoices for the customer through bookings
    const invoices = await prisma.invoice.findMany({
      where: {
        booking: {
          customerId: customerId
        }
      },
      include: {
        booking: {
          select: {
            id: true,
            periods: {
              select: {
                id: true,
                start: true,
                end: true
              },
              orderBy: {
                start: 'asc'
              }
            },
            resources: {
              select: {
                name: true
              }
            }
          }
        },
        payments: {
          select: {
            id: true,
            amount: true,
            method: true,
            status: true,
            paidAt: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json(invoices);
  } catch (error) {
    console.error('Error fetching customer invoices:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/customers/${(await params).id}/invoices`,
      customerId: (await params).id
    });

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching customer invoices. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}