import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { userSearchSchema, userCreateSchema } from '@/lib/validations/user';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  console.log('GET /api/users - Starting request');
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with defaults and basic validation
    const searchQuery = searchParams.get('search') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1') || 1);
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10') || 10));
    const role = searchParams.get('role') || undefined;

    // Simple validation without Zod for now
    const validatedParams = {
      search: searchQuery,
      page,
      limit,
      role: role && ['ADMIN', 'LOGISTICS', 'RECEIPTION', 'FINANCE', 'OFFICE'].includes(role) ? role : undefined
    };

    console.log('Validated params:', validatedParams);

    // Build where clause for search and filtering
    const where: Prisma.UserWhereInput = {};
    
    if (validatedParams.search) {
      where.OR = [
        { firstName: { contains: validatedParams.search } },
        { lastName: { contains: validatedParams.search } },
        { username: { contains: validatedParams.search } }
      ];
    }

    if (validatedParams.role) {
      where.role = validatedParams.role as any;
    }

    console.log('Where clause:', JSON.stringify(where, null, 2));

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;
    console.log('Pagination - skip:', skip, 'take:', validatedParams.limit);

    // Get total count for pagination metadata
    console.log('Getting total count...');
    const totalCount = await prisma.user.count({ where });
    console.log('Total count:', totalCount);

    // Fetch users with pagination
    console.log('Fetching users...');
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        username: true,
        role: true,
        isSuperUser: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      },
      orderBy: [
        { createdAt: 'desc' },
        { id: 'desc' }
      ],
      skip,
      take: validatedParams.limit
    });
    console.log('Fetched users:', users.length);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNextPage = validatedParams.page < totalPages;
    const hasPreviousPage = validatedParams.page > 1;

    return NextResponse.json({
      success: true,
      data: {
        data: users,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: totalCount,
          totalPages,
          hasNext: hasNextPage,
          hasPrev: hasPreviousPage
        }
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid query parameters',
          message: error.message
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching users'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('POST /api/users - Starting user creation');
  try {
    const body = await request.json();
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    // Validate input data
    console.log('Validating input data...');
    const validatedData = userCreateSchema.parse(body);
    console.log('Validation successful:', JSON.stringify(validatedData, null, 2));

    // Temporary workaround: use a simple hash instead of bcrypt
    // TODO: Replace with proper bcrypt hashing once bcrypt is fixed
    console.log('Hashing password...');
    const crypto = await import('crypto');
    const hashedPassword = crypto.createHash('sha256').update(validatedData.password).digest('hex');
    console.log('Password hashed successfully');

    // Create the user
    console.log('Creating user in database...');
    const user = await prisma.user.create({
      data: {
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        username: validatedData.username,
        password: hashedPassword,
        role: validatedData.role,
        isSuperUser: validatedData.isSuperUser,
        // TODO: Add createdById from authenticated user session
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        username: true,
        role: true,
        isSuperUser: true,
        createdAt: true,
        updatedAt: true
      }
    });
    console.log('User created successfully:', JSON.stringify(user, null, 2));

    return NextResponse.json({
      success: true,
      data: user
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    console.error('Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: error.message
        },
        { status: 400 }
      );
    }

    // Handle unique constraint violations
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        return NextResponse.json(
          { 
            success: false,
          error: 'Username already exists',
            message: 'This username is already taken. Please choose a different one.'
          },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while creating the user'
      },
      { status: 500 }
    );
  }
}