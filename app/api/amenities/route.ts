import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { amenitySearchSchema, amenityCreateSchema } from '@/lib/validations/amenity';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  console.log('GET /api/amenities - Starting request');
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with defaults and basic validation
    const searchQuery = searchParams.get('search') || '';
    const page = Math.max(1, parseInt(searchParams.get('page') || '1') || 1);
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10') || 10));

    // Simple validation without Zod for now
    const validatedParams = {
      search: searchQuery,
      page,
      limit
    };

    console.log('Validated params:', validatedParams);

    // Build where clause for search
    const where: Prisma.AmenityWhereInput = {};
    
    if (validatedParams.search) {
      where.name = { contains: validatedParams.search };
    }

    console.log('Where clause:', JSON.stringify(where, null, 2));

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;
    console.log('Pagination - skip:', skip, 'take:', validatedParams.limit);

    // Get total count for pagination metadata
    console.log('Getting total count...');
    const totalCount = await prisma.amenity.count({ where });
    console.log('Total count:', totalCount);

    // Fetch amenities with pagination
    console.log('Fetching amenities...');
    const amenities = await prisma.amenity.findMany({
      where,
      select: {
        id: true,
        name: true,
        icon: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      },
      orderBy: [
        { createdAt: 'desc' },
        { id: 'desc' }
      ],
      skip,
      take: validatedParams.limit
    });
    console.log('Fetched amenities:', amenities.length);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNextPage = validatedParams.page < totalPages;
    const hasPreviousPage = validatedParams.page > 1;

    return NextResponse.json({
      success: true,
      data: {
        data: amenities,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: totalCount,
          totalPages,
          hasNext: hasNextPage,
          hasPrev: hasPreviousPage
        }
      }
    });
  } catch (error) {
    console.error('Error fetching amenities:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'GET /api/amenities'
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid query parameters',
          message: 'Please check your search parameters and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle database connection errors
    if (error instanceof Error && error.message.includes('database')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching amenities. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('POST /api/amenities - Starting amenity creation');
  let body: any = null;

  try {
    body = await request.json();
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    // Validate input data
    console.log('Validating input data...');
    const validatedData = amenityCreateSchema.parse(body);
    console.log('Validation successful:', JSON.stringify(validatedData, null, 2));

    // Create the amenity
    console.log('Creating amenity in database...');
    const amenity = await prisma.amenity.create({
      data: {
        name: validatedData.name,
        icon: validatedData.icon,
        // TODO: Add createdById from authenticated user session
      },
      select: {
        id: true,
        name: true,
        icon: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      }
    });
    console.log('Amenity created successfully:', JSON.stringify(amenity, null, 2));

    return NextResponse.json({
      success: true,
      data: amenity
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating amenity:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: 'POST /api/amenities',
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the amenity information and try again.',
          details: error.message
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return NextResponse.json(
            { 
              success: false,
              error: 'Amenity name already exists',
              message: 'This amenity name is already taken. Please choose a different one.',
              field: 'name'
            },
            { status: 409 }
          );
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'Invalid data reference. Please check your input.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Record not found',
              message: 'Required record not found in database.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete. Please try again.'
        },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while creating the amenity. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}