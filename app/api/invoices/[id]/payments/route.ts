import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { paymentCreateSchema, paymentSearchSchema, validatePaymentAgainstInvoice, calculateInvoiceStatus } from '@/lib/validations/invoice';
import { Prisma } from '@prisma/client';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const invoiceId = parseInt((await params).id);
    
    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid invoice ID',
          message: 'Invoice ID must be a valid number'
        },
        { status: 400 }
      );
    }

    // Check if invoice exists
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      select: { id: true }
    });

    if (!invoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invoice not found',
          message: 'The requested invoice does not exist'
        },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with defaults
    const page = Math.max(1, parseInt(searchParams.get('page') || '1') || 1);
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10') || 10));
    const method = searchParams.get('method') || undefined;
    const status = searchParams.get('status') || undefined;

    // Validate parameters
    const validatedParams = paymentSearchSchema.parse({
      invoiceId,
      method,
      status,
      page,
      limit
    });

    // Build where clause for filtering
    const where: Prisma.PaymentWhereInput = {
      invoiceId: invoiceId
    };
    
    if (validatedParams.method) {
      where.method = validatedParams.method;
    }

    if (validatedParams.status) {
      where.status = validatedParams.status;
    }

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;

    // Get total count for pagination metadata
    const totalCount = await prisma.payment.count({ where });

    // Fetch payments with pagination
    const payments = await prisma.payment.findMany({
      where,
      select: {
        id: true,
        invoiceId: true,
        amount: true,
        method: true,
        status: true,
        reference: true,
        notes: true,
        paidAt: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        updatedById: true
      },
      orderBy: [
        { createdAt: 'desc' },
        { id: 'desc' }
      ],
      skip,
      take: validatedParams.limit
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNextPage = validatedParams.page < totalPages;
    const hasPreviousPage = validatedParams.page > 1;

    return NextResponse.json({
      success: true,
      data: {
        data: payments,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: totalCount,
          totalPages,
          hasNext: hasNextPage,
          hasPrev: hasPreviousPage
        }
      }
    });
  } catch (error) {
    console.error('Error fetching payments:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `GET /api/invoices/${(await params).id}/payments`,
      invoiceId: (await params).id
    });

    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError' || (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid query parameters',
          message: 'Please check the search parameters and try again.',
          details: 'errors' in error ? error.errors : []
        },
        { status: 400 }
      );
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while fetching payments. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  let body: any = null;
  
  try {
    const invoiceId = parseInt((await params).id);
    
    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid invoice ID',
          message: 'Invoice ID must be a valid number'
        },
        { status: 400 }
      );
    }

    body = await request.json();
    
    // Validate input data
    const validatedData = paymentCreateSchema.parse(body);

    // Get invoice with current payment information
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      select: {
        id: true,
        total: true,
        paid: true,
        status: true,
        payments: {
          where: {
            status: 'COMPLETED'
          },
          select: {
            amount: true
          }
        }
      }
    });

    if (!invoice) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invoice not found',
          message: 'The requested invoice does not exist'
        },
        { status: 404 }
      );
    }

    // Calculate current paid amount from completed payments
    const currentPaid = invoice.payments.reduce((sum, payment) => {
      return sum + Number(payment.amount);
    }, 0);

    // Validate payment amount against invoice balance
    const paymentValidation = validatePaymentAgainstInvoice(validatedData, {
      total: Number(invoice.total),
      paid: currentPaid
    });

    if (!paymentValidation.isValid) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid payment amount',
          message: paymentValidation.error
        },
        { status: 400 }
      );
    }

    // Create payment and update invoice in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the payment
      const payment = await tx.payment.create({
        data: {
          invoiceId: invoiceId,
          amount: validatedData.amount,
          method: validatedData.method,
          status: 'COMPLETED', // Default to completed for now
          reference: validatedData.reference,
          notes: validatedData.notes,
          paidAt: validatedData.paidAt || new Date(),
          // TODO: Add createdById from authenticated user session
        },
        select: {
          id: true,
          amount: true,
          method: true,
          status: true,
          reference: true,
          notes: true,
          paidAt: true,
          createdAt: true,
          updatedAt: true,
          createdById: true,
          updatedById: true
        }
      });

      // Calculate new paid amount and status
      const newPaidAmount = currentPaid + validatedData.amount;
      const newStatus = calculateInvoiceStatus(Number(invoice.total), newPaidAmount);

      // Update invoice with new paid amount and status
      await tx.invoice.update({
        where: { id: invoiceId },
        data: {
          paid: newPaidAmount,
          status: newStatus,
          updatedAt: new Date(),
          // TODO: Add updatedById from authenticated user session
        }
      });

      return payment;
    });

    return NextResponse.json({
      success: true,
      data: result
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating payment:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      endpoint: `POST /api/invoices/${(await params).id}/payments`,
      invoiceId: (await params).id,
      requestBody: body
    });
    
    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError' || (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid input data',
          message: 'Please check the payment information and try again.',
          details: 'errors' in error ? error.errors : []
        },
        { status: 400 }
      );
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2003':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invalid reference',
              message: 'The selected invoice does not exist. Please refresh and try again.'
            },
            { status: 400 }
          );
        case 'P2025':
          return NextResponse.json(
            { 
              success: false,
              error: 'Invoice not found',
              message: 'The invoice you are trying to add payment to no longer exists.'
            },
            { status: 404 }
          );
        default:
          console.error('Unhandled Prisma error:', error.code, error.message);
      }
    }

    // Handle database connection errors
    if (error instanceof Prisma.PrismaClientInitializationError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Database connection error',
          message: 'Unable to connect to the database. Please try again in a moment.'
        },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred while creating the payment. Our team has been notified.'
      },
      { status: 500 }
    );
  }
}